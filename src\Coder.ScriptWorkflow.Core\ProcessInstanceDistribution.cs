﻿using System;

namespace Coder.ScriptWorkflow;

/// <summary>
///     读取状态
/// </summary>
public class ProcessInstanceDistribution
{
    /// <summary>
    /// </summary>
    protected ProcessInstanceDistribution()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="userName"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public ProcessInstanceDistribution(ProcessInstance processInstance, string userName)
    {
        CreateTime = DateTimeOffset.Now;
        ProcessInstance = processInstance ?? throw new ArgumentNullException(nameof(processInstance));
        UserName = userName ?? throw new ArgumentNullException(nameof(userName));
    }

    /// <summary>
    ///     读取时间
    /// </summary>
    public DateTimeOffset? ReadTime { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTimeOffset CreateTime { get; protected set; }

    /// <summary>
    ///     账号名
    /// </summary>
    public string UserName { get; protected set; }

    /// <summary>
    ///     中文名
    /// </summary>
    public string UserRealName { get; set; }

    /// <summary>
    ///     所属的
    /// </summary>
    public virtual ProcessInstance ProcessInstance { get; set; }

    /// <summary>
    ///     已经看过了。
    /// </summary>
    public bool HasRead { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }
}