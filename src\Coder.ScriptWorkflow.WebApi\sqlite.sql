﻿CREATE TABLE IF NOT EXISTS "swf_efmigrationshistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK_swf_efmigrationshistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);

BEGIN TRANSACTION;

CREATE TABLE "swf_assigner" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_assigner" PRIMARY KEY AUTOINCREMENT,
    "AssignScopeType" INTEGER NOT NULL,
    "Discriminator" TEXT NOT NULL,
    "Script" TEXT NULL,
    "Performers" TEXT NULL
);

CREATE TABLE "swf_global_variable" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_global_variable" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NULL,
    "Variable" TEXT NULL,
    "Env" INTEGER NOT NULL
);

CREATE TABLE "swf_globalScript" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_globalScript" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NULL,
    "Script" text NULL
);

CREATE TABLE "swf_PI_Tags" (
    -- 工作流程标记

    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_Tags" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NULL,
    "Number" INTEGER NOT NULL
);

CREATE TABLE "swf_scripts" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_scripts" PRIMARY KEY AUTOINCREMENT,
    "Script" TEXT NULL,
    "Discriminator" TEXT NOT NULL
);

CREATE TABLE "swf_swf_setting" (
    -- 工作流实例

    -- id
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_swf_setting" PRIMARY KEY AUTOINCREMENT,

    -- 脚本
    "Script" text NULL,

    "Discriminator" TEXT NOT NULL,

    "Plugins" text NULL
);

CREATE TABLE "swf_workflowLog" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workflowLog" PRIMARY KEY AUTOINCREMENT,

    "LogLevel" INTEGER NOT NULL,

    -- 工单id
    "ProcessInstanceId" INTEGER NOT NULL,

    -- 工单号
    "ProcessInstanceNumber" TEXT NULL,

    -- 工作流定义版本
    "Version" INTEGER NOT NULL,

    -- 工作流名称
    "WorkProcessName" TEXT NULL,

    -- 节点名称
    "NodeName" TEXT NULL,

    "WorkActivityId" INTEGER NULL,

    "Type" TEXT NOT NULL,

    "Content" text NULL,

    "PluginName" TEXT NULL,

    "CreateTime" TEXT NOT NULL
);

CREATE TABLE "swf_workProcessPermission" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workProcessPermission" PRIMARY KEY AUTOINCREMENT,
    "ProcessName" TEXT NULL,
    "ManageRole" TEXT NULL
);

CREATE TABLE "swf_workProcess" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workProcess" PRIMARY KEY AUTOINCREMENT,

    -- 图标
    "Icon" TEXT NULL,

    -- 备注
    "Comment" TEXT NULL,

    -- 简称
    "Abbr" TEXT NULL,

    "LogLevel" INTEGER NOT NULL,

    "FormDesign" text NULL,

    "FormManageDesign" text NULL,

    "Name" TEXT NULL,

    "UpdateTimeOffset" TEXT NULL,

    "Version" INTEGER NOT NULL,

    "OnCompleteId" INTEGER NULL,

    "OnCancelId" INTEGER NULL,

    "OnStartId" INTEGER NULL,

    "Enable" INTEGER NOT NULL,

    "Prefix" TEXT NULL,

    "Configurations" text NULL,

    "GlobalScript" text NULL,

    "FormTypeScriptDefined" text NULL,

    "Plugins" TEXT NULL,

    "Creator" TEXT NULL,

    "CanBeDeleteWorkActivityCount" INTEGER NOT NULL,

    "Group" TEXT NULL,
    CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCancelId" FOREIGN KEY ("OnCancelId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCompleteId" FOREIGN KEY ("OnCompleteId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_workProcess_swf_scripts_OnStartId" FOREIGN KEY ("OnStartId") REFERENCES "swf_scripts" ("Id")
);

CREATE TABLE "swf_PermissionPerformer" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PermissionPerformer" PRIMARY KEY AUTOINCREMENT,
    "Type" INTEGER NOT NULL,
    "Name" TEXT NULL,
    "WorkProcessPermissionId" INTEGER NULL,
    CONSTRAINT "FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProcessPermissionId" FOREIGN KEY ("WorkProcessPermissionId") REFERENCES "swf_workProcessPermission" ("Id")
);

CREATE TABLE "swf_node" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_node" PRIMARY KEY AUTOINCREMENT,

    "Name" TEXT NULL,

    "Auto" INTEGER NOT NULL,

    "NextNodeId" INTEGER NULL,

    "WorkProcessId" INTEGER NULL,

    "Position" TEXT NULL,

    "Discriminator" TEXT NOT NULL,

    "MatchDescription" TEXT NULL,

    -- 执行脚本
    "Script" text NULL,

    "ElseNodeId" INTEGER NULL,

    "ElseDescription" TEXT NULL,

    -- 执行脚本
    "ConditionDecision_Script" text NULL,

    "NextTaskPerformers" INTEGER NULL,

    "AssignerId" INTEGER NULL,

    "CanGiveUp" INTEGER NULL,

    "FormDesign" text NULL,

    "WorkActivityCompleteScriptId" INTEGER NULL,

    "WorkTaskCompleteScriptId" INTEGER NULL,

    "WorkTaskStartScriptId" INTEGER NULL,

    "SuggestionComment" TEXT NULL,

    "ExtendInfo" text NULL,
    CONSTRAINT "FK_swf_node_swf_assigner_AssignerId" FOREIGN KEY ("AssignerId") REFERENCES "swf_assigner" ("Id"),
    CONSTRAINT "FK_swf_node_swf_node_ElseNodeId" FOREIGN KEY ("ElseNodeId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_node_swf_node_NextNodeId" FOREIGN KEY ("NextNodeId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_node_swf_scripts_WorkActivityCompleteScriptId" FOREIGN KEY ("WorkActivityCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskCompleteScriptId" FOREIGN KEY ("WorkTaskCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskStartScriptId" FOREIGN KEY ("WorkTaskStartScriptId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_node_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id") ON DELETE SET NULL
);

CREATE TABLE "swf_processInstance" (
    -- 工作流实例

    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_processInstance" PRIMARY KEY AUTOINCREMENT,

    -- 是否处于debug模式
    "IsDebug" INTEGER NOT NULL,

    -- 是否已经删除
    "IsDelete" INTEGER NOT NULL,

    "WorkActivityCount" INTEGER NOT NULL,

    "WorkProcessId" INTEGER NULL,

    "Priority" INTEGER NOT NULL DEFAULT 100,

    "Subject" TEXT NULL,

    "Creator" TEXT NULL,

    "Status" INTEGER NOT NULL,

    "FinishTime" TEXT NULL,

    "CreateTime" TEXT NOT NULL,

    "StartTime" TEXT NULL,

    "Number" TEXT NOT NULL,

    "SuspendTime" TEXT NULL,

    "SuspendComment" TEXT NULL,

    "Comment" TEXT NULL,

    "Form" json NULL,

    "CreatorName" TEXT NULL,
    CONSTRAINT "FK_swf_processInstance_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id")
);

CREATE TABLE "swf_condition_decision" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_condition_decision" PRIMARY KEY AUTOINCREMENT,

    "NodeId" INTEGER NULL,

    -- 匹配的字符串
    "MatchValue" TEXT NULL,

    "Description" TEXT NULL,

    "ConditionDecisionId" INTEGER NULL,
    CONSTRAINT "FK_swf_condition_decision_swf_node_ConditionDecisionId" FOREIGN KEY ("ConditionDecisionId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_condition_decision_swf_node_NodeId" FOREIGN KEY ("NodeId") REFERENCES "swf_node" ("Id")
);

CREATE TABLE "swf_WorkTaskCommand" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_WorkTaskCommand" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NULL,
    "Order" INTEGER NOT NULL,
    "Discriminator" TEXT NOT NULL,
    "WorkTaskId" INTEGER NULL,
    "Script" text NULL,
    CONSTRAINT "FK_swf_WorkTaskCommand_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id")
);

CREATE TABLE "swf_files" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_files" PRIMARY KEY AUTOINCREMENT,
    "FileName" TEXT NULL,
    "FileId" TEXT NULL,
    "FileType" INTEGER NOT NULL,
    "CreateUser" TEXT NULL,
    "ProcessInstanceId" INTEGER NULL,
    CONSTRAINT "FK_swf_files_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

CREATE TABLE "swf_PI_Distribution" (
    -- 工作流程分发记录

    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_Distribution" PRIMARY KEY AUTOINCREMENT,
    "ReadTime" TEXT NULL,
    "CreateTime" TEXT NOT NULL,
    "UserName" TEXT NULL,
    "UserRealName" TEXT NULL,
    "ProcessInstanceId" INTEGER NULL,
    "HasRead" INTEGER NOT NULL,
    CONSTRAINT "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

CREATE TABLE "swf_PI_ProcessInstanceTags" (
    -- 工作流程标记

    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_ProcessInstanceTags" PRIMARY KEY AUTOINCREMENT,
    "TagId" INTEGER NULL,
    "Color" TEXT NULL,
    "ProcessInstanceId" INTEGER NULL,
    "CanDelete" INTEGER NOT NULL,
    CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId" FOREIGN KEY ("TagId") REFERENCES "swf_PI_Tags" ("Id"),
    CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

CREATE TABLE "swf_WorkActivity" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_WorkActivity" PRIMARY KEY AUTOINCREMENT,
    "Priority" INTEGER NOT NULL DEFAULT 100,
    "WorkTaskId" INTEGER NULL,
    "AssignPerformers" TEXT NULL,
    "Status" INTEGER NOT NULL,
    "AssignTime" TEXT NULL,
    "TaskCreatingGroup" TEXT NULL,
    "DisposeUser" TEXT NULL,
    "DisposeUserName" TEXT NULL,
    "DisposeTime" TEXT NULL,
    "CreateTime" TEXT NOT NULL,
    "Command" TEXT NULL,
    "Comment" TEXT NULL,
    "TimeSpan" TEXT NULL,
    "ProcessInstanceId" INTEGER NULL,
    CONSTRAINT "FK_swf_WorkActivity_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

CREATE INDEX "IX_swf_condition_decision_ConditionDecisionId" ON "swf_condition_decision" ("ConditionDecisionId");

CREATE INDEX "IX_swf_condition_decision_NodeId" ON "swf_condition_decision" ("NodeId");

CREATE INDEX "IX_swf_files_ProcessInstanceId" ON "swf_files" ("ProcessInstanceId");

CREATE INDEX "IX_swf_node_AssignerId" ON "swf_node" ("AssignerId");

CREATE INDEX "IX_swf_node_ElseNodeId" ON "swf_node" ("ElseNodeId");

CREATE INDEX "IX_swf_node_NextNodeId" ON "swf_node" ("NextNodeId");

CREATE INDEX "IX_swf_node_WorkActivityCompleteScriptId" ON "swf_node" ("WorkActivityCompleteScriptId");

CREATE INDEX "IX_swf_node_WorkProcessId" ON "swf_node" ("WorkProcessId");

CREATE INDEX "IX_swf_node_WorkTaskCompleteScriptId" ON "swf_node" ("WorkTaskCompleteScriptId");

CREATE INDEX "IX_swf_node_WorkTaskStartScriptId" ON "swf_node" ("WorkTaskStartScriptId");

CREATE INDEX "IX_swf_PermissionPerformer_WorkProcessPermissionId" ON "swf_PermissionPerformer" ("WorkProcessPermissionId");

CREATE INDEX "IX_swf_PI_Distribution_ProcessInstanceId" ON "swf_PI_Distribution" ("ProcessInstanceId");

CREATE INDEX "IX_swf_PI_ProcessInstanceTags_ProcessInstanceId" ON "swf_PI_ProcessInstanceTags" ("ProcessInstanceId");

CREATE INDEX "IX_swf_PI_ProcessInstanceTags_TagId" ON "swf_PI_ProcessInstanceTags" ("TagId");

CREATE INDEX "IX_swf_processInstance_Number" ON "swf_processInstance" ("Number");

CREATE INDEX "IX_swf_processInstance_WorkProcessId" ON "swf_processInstance" ("WorkProcessId");

CREATE INDEX "IX_swf_WorkActivity_ProcessInstanceId" ON "swf_WorkActivity" ("ProcessInstanceId");

CREATE INDEX "IX_swf_WorkActivity_WorkTaskId" ON "swf_WorkActivity" ("WorkTaskId");

CREATE INDEX "IX_swf_workflowLog_ProcessInstanceNumber" ON "swf_workflowLog" ("ProcessInstanceNumber");

CREATE UNIQUE INDEX "IX_swf_workProcess_Name_Version" ON "swf_workProcess" ("Name", "Version");

CREATE INDEX "IX_swf_workProcess_OnCancelId" ON "swf_workProcess" ("OnCancelId");

CREATE INDEX "IX_swf_workProcess_OnCompleteId" ON "swf_workProcess" ("OnCompleteId");

CREATE INDEX "IX_swf_workProcess_OnStartId" ON "swf_workProcess" ("OnStartId");

CREATE INDEX "IX_swf_workProcessPermission_ProcessName" ON "swf_workProcessPermission" ("ProcessName");

CREATE INDEX "IX_swf_WorkTaskCommand_WorkTaskId" ON "swf_WorkTaskCommand" ("WorkTaskId");

INSERT INTO "swf_efmigrationshistory" ("MigrationId", "ProductVersion")
VALUES ('20250616060733_init3', '8.0.10');

COMMIT;

