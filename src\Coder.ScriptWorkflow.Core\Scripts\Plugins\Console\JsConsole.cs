﻿using System.Linq;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts.Plugins.Console;

public class JsConsole
{
    private readonly IWorkflowContext _context;

    public JsConsole(IWorkflowContext context)
    {
        _context = context;
    }

    public void log(string eventName, params JSValue[] pa)
    {
        var ary = pa.Select(_ => _.Value.ToString());
        var str = string.Join(' ', ary);
        _context.Debugger?.SendMessage(_context, eventName ?? "debug", str);
    }

}