﻿namespace Coder.ScriptWorkflow.WorkTaskCommands;

/// <summary>
/// </summary>
public abstract class WorkTaskCommand
{
    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     排序规则
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="workActivity"></param>
    public abstract void Invoke(IWorkflowContext workflowContext, WorkActivity workActivity);
}