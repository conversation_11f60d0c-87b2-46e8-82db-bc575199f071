﻿namespace Coder.ScriptWorkflow.Assigners;

/// <summary>
/// </summary>
public class AssignUser
{
    private int? _hasCode;

    /// <summary>
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     是否位默认用户
    /// </summary>
    public bool Default { get; set; }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public override int GetHashCode()
    {
        if (_hasCode == null) _hasCode = UserName?.GetHashCode() * 3 ?? base.GetHashCode();
        return _hasCode.Value;
    }

    /// <summary>
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public override bool Equals(object obj)
    {
        return obj != null && ((AssignUser)obj).UserName == UserName;
    }
}