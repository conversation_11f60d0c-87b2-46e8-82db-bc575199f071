﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
internal class GlobalScriptStore<T> : IGlobalScriptStore where T : DbContext
{
    private readonly T _dbContext;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public GlobalScriptStore(T dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<GlobalScriptItem> GetAsync(int id)
    {
        return _dbContext.Set<GlobalScriptItem>().FirstOrDefaultAsync(_ => _.Id == id);
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task DeleteAsync(int id)
    {
        var entity = await GetAsync(id);
        _dbContext.Remove(entity);
    }

    /// <summary>
    /// </summary>
    /// <param name="workTask"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public void AddOrUpdate(GlobalScriptItem workTask)
    {
        if (workTask.Name == null) throw new ArgumentOutOfRangeException(nameof(workTask), "workTask.Name 不能为空。");
        _dbContext.Update(workTask);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<GlobalScriptItem>> ListAsync(string name)
    {
        return await _dbContext.Set<GlobalScriptItem>().Where(_ => string.IsNullOrEmpty(name) || _.Name.Contains(name))
            .ToListAsync();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<GlobalScriptViewModel>> ListAllAsync()
    {
        return await _dbContext.Set<GlobalScriptItem>()
            .Select(_ => new GlobalScriptViewModel
            {
                Id = _.Id,
                Name = _.Name
            }).ToListAsync();
    }
}

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
internal class GlobalVariableStore<T> : IGlobalVariableStore where T : DbContext
{
    private readonly T _dbContext;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public GlobalVariableStore(T dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<GlobalVariable> GetAsync(int id)
    {
        return _dbContext.Set<GlobalVariable>().FirstOrDefaultAsync(_ => _.Id == id);
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task DeleteAsync(int id)
    {
        var entity = await GetAsync(id);
        _dbContext.Remove(entity);
    }

    /// <summary>
    /// </summary>
    /// <param name="workTask"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public void AddOrUpdate(GlobalVariable workTask)
    {
        if (workTask.Name == null) throw new ArgumentOutOfRangeException(nameof(workTask), "workTask.Name 不能为空。");
        _dbContext.Update(workTask);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<GlobalVariable>> ListAsync(string name)
    {
        return await _dbContext.Set<GlobalVariable>().Where(_ => string.IsNullOrEmpty(name) || _.Name.Contains(name))
            .ToListAsync();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<GlobalVariable>> ListAllAsync(GlobalVariableEnv env)
    {
        return await _dbContext.Set<GlobalVariable>().Where(_=>_.Env == env).ToListAsync();
    }
}