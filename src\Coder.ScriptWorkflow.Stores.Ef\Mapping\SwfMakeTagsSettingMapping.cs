﻿using Coder.ScriptWorkflow.Settings;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class SwfMakeTagsSettingMapping : IEntityTypeConfiguration<SwfMakeTagsSetting>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<SwfMakeTagsSetting> builder)
    {
        builder.HasBaseType<SwfSetting>();
        builder.Property(_ => _.Plugins).HasJsonValueConversion().HasColumnType("text");
    }
}