﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.Member;
using Coder.Member.Clients;
using Coder.Member.ViewModels.Users;

using Coder.ScriptWorkflow.Assigners;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using Array = NiL.JS.BaseLibrary.Array;
using String = NiL.JS.BaseLibrary.String;

namespace Coder.ScriptWorkflow.Scripts.Plugin.Member;

/// <summary>
/// </summary>
public class JsHttpMember
{
    private readonly IUserClient _client;
    private readonly IWorkflowContext _swfContext;

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    public JsHttpMember(IUserClient client, IWorkflowContext swfContext)
    {
        _client = client;
        _swfContext = swfContext;
    }

    /// <summary>
    /// </summary>
    /// <param name="orgName"></param>
    /// <returns></returns>
    public IEnumerable<AssignUser> GetByOrg(string orgName)
    {
        return _client
            .GetUsersByClaimAsync(UserDefined.ClaimOrg, orgName).Result.Data.Select(_ => new AssignUser
            {
                Name = _.Name,
                UserName = _.UserName
            });
    }

    /// <summary>
    /// </summary>
    /// <param name="roleName"></param>
    /// <returns></returns>
    public IEnumerable<AssignUser> GetByRole(string roleName)
    {
        return _client.GetByRolesAsync(new[] { roleName }).Result.Data.Select(_ => new AssignUser
        {
            Name = _.Name,
            UserName = _.UserName
        });
    }

    /// <summary>
    /// </summary>
    /// <param name="userName"></param>
    /// <returns></returns>
    public AssignUser GetByUserName(string userName)
    {
        var re = _client.GetSimpleUserAsync(userName).Result;
        return new AssignUser
        {
            Name = re.Data.Name,
            UserName = re.Data.UserName
        };
    }

    /// <summary>
    /// </summary>
    /// <param name="userName"></param>
    /// <returns></returns>
    public JSValue GetUserDetailByUserName(string userName)
    {
        var result = _client.GetUserAsync(userName).Result;
        if (result == null) return JSValue.Null;
        var v = To(result.Data);
        return v;
    }

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    public UserViewModel GetByClaim(string name, string value)
    {
        return _client.GetUserByClaimAsync(name, value).Result.Data;
    }

    /// <summary>
    ///     通过角色和
    /// </summary>
    /// <param name="roleName"></param>
    /// <param name="claimType"></param>
    /// <param name="claimValue"></param>
    /// <returns></returns>
    public IEnumerable<UserSimpleModel> GetUsersByRoleAndClaim(string roleName, string claimType, string claimValue)
    {
        return _client.FindByRoleAndClaim(roleName, claimType, claimValue).Result?.Data;
    }
    /// <summary>
    /// 通过角色获取一个用户，而这个用户在当前用户所属的组织总，任职这个角色。
    /// </summary>
    /// <returns></returns>
    public string GetUserByRoleInCurrentUserOrg(string userName, string roleInOrg)
    {
        var user = _client.GetUserAsync(userName).Result.Data;
        var currentUserOrg = user.Claims.FirstOrDefault(claimViewModel => claimViewModel.Type == "org");
        if (currentUserOrg == null)
            throw new Exception("当前用户没有任何组织");

        return _client.FindByRoleAndClaim(roleInOrg, "org", currentUserOrg.Value).Result?.Data.FirstOrDefault()?.UserName;

    }
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public JSValue CurrentUser()
    {
        return To(_swfContext.CurrentUser);
    }


    public static JSValue To(UserViewModel user)
    {
        var result = JSObject.CreateObject();

        result["UserName"] = user.UserName;
        result["Name"] = user.Name;
        result["PhoneNumber"] = user.PhoneNumber;
        result["AvatarUrl"] = user.AvatarUrl;
        result["Email"] = user.Email;

        var ary = new Array();
        foreach (var claimViewModel in user.Claims)
        {
            var claim = JSObject.CreateObject();
            claim["Type"] = claimViewModel.Type;
            claim["Value"] = claimViewModel.Value;

            ary.Add(claim);
        }

        result["Claims"] = ary;

        var rolesAry = new Array();
        foreach (var role in user.Roles) rolesAry.Add(new String(role));

        result["Roles"] = rolesAry;
  
      
        return result;
    }
}