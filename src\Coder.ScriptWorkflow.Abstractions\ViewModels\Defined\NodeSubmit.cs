﻿using Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;
using Newtonsoft.Json;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

public class CheckVersionResult
{
    public int Processing { get; set; }

    public bool ExistVersion { get; set; }

    
}
/// <summary>
///     节点提交
/// </summary>
[JsonConverter(typeof(NodeSubmitJsonConverter))]
public abstract class NodeSubmit
{
    private string _nextNodeName;

    /// <summary>
    /// </summary>
    protected NodeSubmit()
    {
    }

    /// <summary>
    ///     类型
    /// </summary>
    [JsonProperty("$type")]
    public string Type { get; set; }

    /// <summary>
    ///     id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     节点名称
    /// </summary>
    public string Name { get; set; }


    /// <summary>
    ///     下一个节点
    /// </summary>

    public virtual string NextNodeName
    {
        get => _nextNodeName;
        set => _nextNodeName = value?.Trim();
    }


    /// <summary>
    /// </summary>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public virtual bool Validate(out string errorMessage)
    {
        errorMessage = null;
        if (string.IsNullOrWhiteSpace(Name))
        {
            errorMessage = $"{Id}-节点名称必须填写";
            return false;
        }

        if (string.IsNullOrEmpty(NextNodeName))
        {
            errorMessage = $"{Name}-下一个节点必须填写";
            return false;
        }

        return true;
    }
}