﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// </summary>
public class WorkProcessSubmit
{
    /// <summary>
    ///     工单号前序
    /// </summary>
    public string Prefix { get; set; }


    /// <summary>
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    /// </summary>
    public string FormDesign { get; set; }

    /// <summary>
    /// </summary>
    [MaxLengthAttribute(50, ErrorMessage = "长度不能多余50字")]
    public string Group { get; set; }

    /// <summary>
    /// </summary>
    [Required]
    [MaxLengthAttribute(100, ErrorMessage = "长度不能多余50字")]
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; } = 0;

    /// <summary>
    ///     版本号
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    ///     是否覆盖现在的定义
    /// </summary>
    public bool OverWrite { get; set; }

    /// <summary>
    ///     当工作流结束的时候，发起的脚本
    /// </summary>
    public string OnCompleteScript { get; set; }

    /// <summary>
    ///     当工作流取消发起脚本。
    /// </summary>
    public string OnCancelScript { get; set; }

    /// <summary>
    ///     启动脚本
    /// </summary>
    public string OnStartScript { get; set; }


    /// <summary>
    ///     启动的插件
    /// </summary>
    public IEnumerable<string> Plugins { get; set; }

    /// <summary>
    ///     工作流定义配置，用于script环境下
    /// </summary>
    public IList<WorkflowConfiguration> Configurations { get; set; } = new List<WorkflowConfiguration>();

    /// <summary>
    ///     其他节点的保存。主要是json提交避免死循环，以后那次Decision的ElsOther放在这个地方。
    ///     初始化<see cref="WorkflowSubmitContext" />会使用本属性
    /// </summary>
    public List<NodeSubmit> Nodes { get; set; } = new();

    /// <summary>
    ///     form 的 typescript.d.ts 用于设置环境中，不是必须的
    ///     定义环节下
    /// </summary>
    public string FormTypeScriptDefined { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public string UpdateTimeOffset { get; set; }

    /// <summary>
    ///     流程创建人
    /// </summary>
    public string Creator { get; set; }

    /// <summary>
    ///     全功能输入的form，这个form-设计用于管理流程实例数据时候采用。
    /// </summary>
    public string FormManageDesign { get; set; }

    /// <summary>
    /// </summary>
    public LogLevel LogLevel { get; set; } = LogLevel.Error;

    /// <summary>
    /// </summary>
    public string GlobalScript { get; set; }

    public int CanBeDeleteWorkActivityCount { get; set; }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public bool Validate(out string message)
    {
        message = null;
        if (string.IsNullOrEmpty(Prefix))
        {
            message = "工单号前序必须填写";
            return false;
        }

        foreach (var node in Nodes)
            if (!node.Validate(out message))
                return false;

        return true;
    }

    /// <summary>
    ///     尝试把新的nodeSubmit加入到wp中。
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="existNodeSubmit">无论成功与否，都会返回一个与之相关的NodeSubmit</param>
    /// <returns></returns>
    public bool TryAdd(NodeSubmit submit, out NodeSubmit existNodeSubmit)
    {
        existNodeSubmit = Nodes.FirstOrDefault(_ => _.Name == submit.Name);
        if (existNodeSubmit == null)
        {
            existNodeSubmit = submit;
            Nodes.Add(submit);
            return true;
        }

        return false;
    }

    public string Abbr { get; set; }
    public string Icon { get; set; }

    public string Comment { get; set; }
}