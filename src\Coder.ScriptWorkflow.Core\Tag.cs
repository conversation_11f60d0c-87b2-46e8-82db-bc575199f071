﻿using System;
using System.Diagnostics.CodeAnalysis;
using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow;

/// <summary>
///     标签
/// </summary>
public class Tag
{
    /// <summary>
    ///     The keeper org key.
    /// </summary>
    public const string KeeperOrgKey = "查看部门:";

    /// <summary>
    ///     处理人标签
    /// </summary>
    public const string DisposeUserNameKey = "处理人:";

    /// <summary>
    ///     抄送Tag的格式
    /// </summary>
    public const string CCUserNameKey = "抄送:";

    /// <summary>
    ///     用于随机颜色
    /// </summary>
    private static readonly Random _randomColors = new();

    /// <summary>
    /// </summary>
    public static string[] Colors =
    {
        "pink", "red", "green", "orange", "cyan", "blue", "purple"
    };

    /// <summary>
    ///     tag‘s id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     标签名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     引用次数
    /// </summary>
    public int Number { get; set; }

    /// <summary>
    ///     随机的颜色
    /// </summary>
    /// <returns></returns>
    public static string Random()
    {
        var radIndex = _randomColors.Next(0, Colors.Length);
        return Colors[radIndex];
    }

    /// <summary>
    /// </summary>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static string MakeDisposeUser(string currentUser)
    {
        if (currentUser == null) throw new ArgumentNullException(nameof(currentUser));
        return DisposeUserNameKey + currentUser;
    }

    /// <summary>
    /// </summary>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static string MakeKeeperOrg(string currentUser)
    {
        if (currentUser == null) throw new ArgumentNullException(nameof(currentUser));
        return KeeperOrgKey + currentUser;
    }

    /// <summary>
    ///     固定格式标签。
    /// </summary>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    public static TagSubmit MakeDisposeUserTag(string currentUser, string color = null)
    {
        if (currentUser == null) throw new ArgumentNullException(nameof(currentUser));
        return new TagSubmit
        {
            Name = DisposeUserNameKey + currentUser,
            Color = color ?? Random()
        };
    }

    /// <summary>
    /// </summary>
    /// <param name="tagName"></param>
    /// <returns></returns>
    public static string GetDataAfterColon([NotNull] string tagName)
    {
        if (tagName == null) throw new ArgumentNullException(nameof(tagName));
        var indexOf = tagName.IndexOf(":") + 1;
        return tagName.Substring(indexOf);
    }

    /// <summary>
    /// </summary>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static string MakeCCUserTag([NotNull] string currentUser)
    {
        if (currentUser == null) throw new ArgumentNullException(nameof(currentUser));
        return CCUserNameKey + currentUser;
    }

    /// <summary>
    ///     把tag 冒号 后的数据提取出来。
    /// </summary>
    /// <param name="tag"></param>
    /// <param name="userName"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    internal static string ReplaceDataAfterColon(string tag, string userName)
    {
        if (tag == null) throw new ArgumentNullException(nameof(tag));
        if (userName == null) throw new ArgumentNullException(nameof(userName));
        var indexOf = tag.IndexOf(":", StringComparison.Ordinal);
        if (indexOf == -1) return tag;
        return tag.Substring(0, indexOf + 1) + userName;
    }
}