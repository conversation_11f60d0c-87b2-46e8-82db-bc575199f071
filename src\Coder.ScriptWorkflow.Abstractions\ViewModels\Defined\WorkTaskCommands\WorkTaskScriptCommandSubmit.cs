﻿namespace Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;

/// <summary>
/// </summary>
public class WorkTaskScriptCommandSubmit : WorkTaskCommandSubmit
{
    /// <summary>
    /// </summary>

    public string Script { get; set; }


    /// <summary>
    /// </summary>
    /// <param name="errorMessage">错误message</param>
    /// <returns></returns>
    public override bool Validate(out string errorMessage)
    {
        if (!base.Validate(out errorMessage))
            return false;
        if (string.IsNullOrEmpty(Script))
        {
            errorMessage = "命令脚本必须填写";
            return false;
        }

        return true;
    }
}