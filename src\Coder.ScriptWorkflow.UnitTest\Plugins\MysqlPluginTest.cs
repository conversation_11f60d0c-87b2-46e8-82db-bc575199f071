﻿using System.Collections.Generic;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugin.MySql;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Plugins;

public class MysqlPluginTest
{
    private const string connectionString =
        "server=127.0.0.1;database=coder_swf;user=root;password=******;CharSet=utf8;";

    public UserViewModel Creator = new()
    {
        UserName = "creator"
    };

   
    public void Test()
    {
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();

        var plugin = new MysqlPlugin();

        var global = new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>());
        var processInstance = new ProcessInstance(new WorkProcess("name"), "user");
        var task = new WorkTask[0];
        var debugger = new DebuggerManager(new EmptyDebuggerPusher());
        var loggerManager = new WorkflowLogManager(sp);

        var ctx = new WorkflowContext(global, processInstance, task, new List<IPlugin>(), new List<ScriptTagInfo>(),
            loggerManager, debugger, Creator);


        var sql = @"
           CREATE TABLE IF NOT EXISTS tasks (
  task_id INT(11) NOT NULL AUTO_INCREMENT,
  subject VARCHAR(45) DEFAULT NULL,
  start_date DATE DEFAULT NULL,
  PRIMARY KEY (task_id)
) 
";

        ctx.JavascriptGlobalContext.Eval(@$"
var conn='{connectionString}'
var f=mysql.ExecuteNon(conn,`{sql}`)
");
        var jsValue = ctx.JavascriptGlobalContext.GetVariable("f");
        Assert.Equal(0, jsValue.Value);


        sql = "insert tasks (subject,start_date)values(@subject,@date)";
        ctx.JavascriptGlobalContext.Eval(@$"
var conn='{connectionString}'
var f=mysql.ExecuteNon(conn,`{sql}`,{{subject:'subject1','date':new Date() }})
");
        jsValue = ctx.JavascriptGlobalContext.GetVariable("f");
        Assert.Equal(1, jsValue.Value);

        sql = "select * from tasks limit 1";
        ctx.JavascriptGlobalContext.Eval(@$"
var conn='{connectionString}'
var f=mysql.ExecuteScalar(conn,`{sql}`)
");
        jsValue = ctx.JavascriptGlobalContext.GetVariable("f");
        Assert.Equal("subject1", jsValue["subject"].Value);
        Assert.Equal(1, jsValue["task_id"].Value);
    }
}