﻿namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     结束节点。
/// </summary>
public class EndNode : Node
{
    /// <inheritdoc />
    public EndNode()
    {
    }

    /// <inheritdoc />
    public EndNode(WorkProcess wp) : base(wp)
    {
    }


    /// <inheritdoc />
    public override string Name
    {
        get => "结束";
        set { }
    }

    /// <inheritdoc />
    public override bool Auto { get; set; } = false;

    /// <inheritdoc />
    /// <summary>
    ///     获取下一个节点。
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="nextNode"></param>
    /// <returns></returns>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        nextNode = null;
        return false;
    }
}