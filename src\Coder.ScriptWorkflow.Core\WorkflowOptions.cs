﻿using System;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class WorkflowOptions
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    internal WorkflowOptions(IServiceCollection services)
    {
        Services = services ?? throw new ArgumentNullException(nameof(services));
    }

    /// <summary>
    /// </summary>
    public IServiceCollection Services { get; protected set; }

    /// <summary>
    ///     文件系统
    /// </summary>
    public string FileSystemHost { get; set; }
}