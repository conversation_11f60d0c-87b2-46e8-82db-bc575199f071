﻿namespace Coder.ScriptWorkflow.Scripts.Plugins.Console;

/// <summary>
/// </summary>
public class JsConsolePlugin : IPlugin
{
    /// <summary>
    /// </summary>
    public const string PluginName = "Debugger";

    /// <summary>
    /// </summary>
    public JsConsolePlugin()
    {
    }

    /// <summary>
    /// </summary>
    public string Name => PluginName;

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new JsConsole(context);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        return @"
interface DebuggerClass {
        log(eventName:string, ...data: any[]): void;
}
declare var Debugger: DebuggerClass;
    ";
    }
}