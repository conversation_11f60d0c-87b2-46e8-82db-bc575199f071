﻿{
  "remoteCfg": {
    "address": "http://cfg",
    "key": "Coder.ScriptWorkflow",
    "enable": true
  },

  "WorkBentchServer": "http://gateway/workbench",
  "ContractServer": "http://gateway/contract",
  "DbType": "SQLITE",
  "ConnectionStrings": {
    "mysql": "server=mysql;port=3306;database=coder_members;user=root;password=*********;CharSet=utf8;",
    "mssql": "Server=localhost\\SQLEXPRESS;Database=coder_members;Trusted_Connection=True;"
  },
  "gateway": "http://127.0.0.1:30000",

  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Debug",
      "Microsoft.Hosting.Lifetime": "Debug"
    }
  },
  "AllowedHosts": "*",
  "MemberService": {
    "client": "ScriptWorkflow",
    "SecretKey": "316",
    "MemberHost": "http://***********:8081/api/member"
  },

  "ConsulOption": {
    "ConsulServer": "http://consul:8500",
    "ServiceHost": "_HOST_", /* 要注册的服务 url */
    "ServicePort": -1, /* 要注册服务的访问端口 */
    "ServiceName": "Coder.ScriptWorkflow", /* 服务名称，集群的时候采用这个名称来获取服务 */
    "ServiceId": "Coder.ScriptWorkflow-01", /* 服务id 必须唯一，用于记录那个服务提供者出现问题 */
    "Tags": [""], /* 可选 日志*/
    "HealthCheckUrl": "http://_HOST_:_PORT_/Health/Status",
    "Enable": false
  }
}