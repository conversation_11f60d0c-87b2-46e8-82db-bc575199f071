﻿
/**
 * workflowManager
 */
declare var workflowManager: workflowManagerClass;

/**
 * 工作流处理类
 */
declare class workflowManagerClass {
    /**
     获取工作流程信息
     @param all 
     */
    GetFlowItem(all: boolean): Array<FlowViewModel>;

    /**
     * 设置抄送用户    
     * @param user 
     */
    SetCcUser(user: string);

    /**
     * 移除抄送用户
     * @param user
     */
    RemoveCcUser(user: string);

    /**
     * 设置tag
     * @param tag tag名称。
     */
    SetTag(tag: string);
}