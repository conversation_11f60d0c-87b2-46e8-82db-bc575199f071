﻿using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers;

public class UnitTestAppContext : DbContext
{
    /// <summary>
    /// </summary>
    /// <param name="options"></param>
    public UnitTestAppContext(DbContextOptions<UnitTestAppContext> options) : base(options)
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    protected override void OnModelCreating(ModelBuilder builder)
    {
        // reference   f;

        builder.AddEfModels("Test");

        base.OnModelCreating(builder);
    }
}