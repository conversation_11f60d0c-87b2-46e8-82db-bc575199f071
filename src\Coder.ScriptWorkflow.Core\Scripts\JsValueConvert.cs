﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using Coder.ScriptWorkflow.Assigners;
using Newtonsoft.Json;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
/// </summary>
public static class JsValueConvert
{
    private static readonly Regex regex = new("\\d+");

    /// <summary>
    ///     改为对象
    /// </summary>
    /// <param name="v"></param>
    /// <returns></returns>
    public static object ToDynamicObject(this JSValue v)
    {
        if (v.ValueType != JSValueType.Object) return v.Value;

        if (IsArrayIndex(v))
        {
            var resultArray = new List<object>();
            foreach (var keymaps in v) resultArray.Add(ToDynamicObject(keymaps.Value));

            return resultArray;
        }


        var result = new ExpandoObject();
        var resultMap = result as IDictionary<string, object>;

        foreach (var keyPaire in v)
        {
            if (keyPaire.Value == null)
                continue;

            var childValue = keyPaire.Value;
            switch (childValue.ValueType)
            {
                case JSValueType.Object:
                    resultMap.Add(keyPaire.Key, ToDynamicObject(childValue));
                    break;
                case JSValueType.Date:
                {
                    var date = (Date)keyPaire.Value.Value;
                    resultMap.Add(keyPaire.Key, date.ToDateTime());
                    break;
                }
                default:
                    resultMap.Add(keyPaire.Key, childValue.Value);
                    break;
            }
        }

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    private static bool IsArrayIndex(JSValue jsValue)
    {
        foreach (var keyPaire in jsValue)
            if (!regex.IsMatch(keyPaire.Key))
                return false;

        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static JSValue ToJsValue(dynamic data,Context scriptContext)
    {
        var dic = data as IDictionary<string, object>;
        var result = JSObject.CreateObject();
        foreach (var key in dic.Keys) result[key] = scriptContext.GlobalContext.ProxyValue(dic[key]);

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="tojsValue"></param>
    /// <returns></returns>
    public static FormUrlEncodedContent ToUrlForm(JSValue tojsValue)
    {
        try
        {
            var v = (string)tojsValue.Value;
            var form = JsonConvert.DeserializeObject<Dictionary<string, string>>(v);
            var result = new FormUrlEncodedContent(form);
            return result;
        }
        catch (JsonReaderException)
        {
            throw new JsonConvertException("jsObject只能是json，并且只有一层.如{code:1,boolean:true}");
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="toJsValue"></param>
    /// <returns></returns>
    public static List<string> ToStringArray(JSValue toJsValue)
    {
        var list = new List<string>();
        if (toJsValue.ValueType == JSValueType.String)
            list.Add((string)toJsValue.Value);
        else if (toJsValue.ValueType == JSValueType.Object)
            foreach (var em in (IEnumerable<KeyValuePair<string, JSValue>>)toJsValue)
                list.Add((string)em.Value.Value);

        return list;
    }

    /// <summary>
    /// </summary>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static string ToJSON(JSValue jsValue)
    {
        if (jsValue.Value == null) throw new ArgumentNullException(nameof(jsValue));
        var value = "";
        if (jsValue.Value is Date date)
            value = date.ToDateTime().ToString("O");
        else
            value = jsValue.Value.ToString();
        return value;
    }


    /// <summary>
    ///     获取脚本中的返回值，生成分配的用户。
    /// </summary>
    /// <param name="toJsValue"></param>
    /// <param name="assigner"></param>
    /// <returns></returns>
    public static AssignResult ToAssignScope(JSValue toJsValue, Assigner assigner)
    {
        var result = new AssignResult(assigner.AssignScopeType);
        var list = new List<Performer>();
        // 如果返回一个字符串，那么默认这个字符串是userName
        if (toJsValue.ValueType == JSValueType.String)
        {
            var item = MakePerformer(toJsValue);
            if (item != null)
                list.Add(item);
        }
        else if (toJsValue.ValueType == JSValueType.Object)
        {
            var array = (IEnumerable<KeyValuePair<string, JSValue>>)toJsValue;
            var isArray = false;
            if (array != null)
                if (array.Any())
                    isArray = array.First().Key == "0";


            if (isArray) //返回是一个数组
            {
                foreach (var em in (IEnumerable<KeyValuePair<string, JSValue>>)toJsValue)
                {
                    var item = MakePerformer(em.Value);
                    if (item != null)
                        list.Add(item);
                }
            }
            else //返回一个对象。
            {
                var item = MakePerformer(toJsValue);
                if (item != null)
                    list.Add(item);
            }
        }

        result.Performers = list.ToArray();
        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    /// <exception cref="WorkflowDefinedException"></exception>
    private static Performer MakePerformer(JSValue value)
    {
        string name;
        string key;
        var type = PerformerType.User;
        if (value.ValueType == JSValueType.String)
        {
            key = (string)value.Value;
            if (string.IsNullOrWhiteSpace(key))
                return null;
            name = (string)value.Value;

            type = PerformerType.User;
        }
        else if (value.ValueType == JSValueType.Object)
        {
            var jObject = value.Value as JSValue;
            key = (string)jObject["key"].Value;
            if (key == null)
                return null;
            name = (string)jObject["name"].Value;

            var typeNumber = (int)jObject["type"].Value;
            switch (typeNumber)
            {
                case 0:
                    type = PerformerType.User;
                    break;
                case 2:
                    type = PerformerType.Org;
                    break;
                case 1:
                    type = PerformerType.Role;
                    break;
                default:
                    throw new WorkflowDefinedException("返回值中的执行者type=" + type + "没有被识别");
            }
        }
        else
        {
            throw new WorkflowDefinedException(value.ValueType + "类型无法识别。");
        }

        if (string.IsNullOrWhiteSpace(key))
            return null;
        return new Performer
        {
            Name = name,
            Key = key,
            Type = type
        };
    }
}