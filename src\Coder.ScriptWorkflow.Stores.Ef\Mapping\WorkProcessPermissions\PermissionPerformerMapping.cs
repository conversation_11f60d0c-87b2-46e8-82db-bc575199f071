﻿using Coder.ScriptWorkflow.Permissions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.WorkProcessPermissions;

/// <summary>
/// </summary>
internal class PermissionPerformerMapping : IEntityTypeConfiguration<PermissionPerformer>
{
    private readonly string _prefix;

    public PermissionPerformerMapping(string prefix)
    {
        _prefix = prefix;
    }

    public void Configure(EntityTypeBuilder<PermissionPerformer> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.Property(_ => _.Type);
        builder.Property(_ => _.Name);
        builder.HasOne(_ => _.WorkProcessPermission).WithMany(_ => _.Performers);
        builder.ToTable($"{_prefix}_PermissionPerformer");
    }
}