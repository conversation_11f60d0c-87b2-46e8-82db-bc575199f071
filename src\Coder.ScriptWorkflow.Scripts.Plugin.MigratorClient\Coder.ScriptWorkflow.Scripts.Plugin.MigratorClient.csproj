﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	  <Version>3.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="defined.ts" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="defined.ts">
      <SubType>Code</SubType>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Coder.Migrator.Abstractions" Version="1.2.2" />
    <PackageReference Include="Coder.Migrator.HttpClients" Version="1.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
  </ItemGroup>

</Project>
