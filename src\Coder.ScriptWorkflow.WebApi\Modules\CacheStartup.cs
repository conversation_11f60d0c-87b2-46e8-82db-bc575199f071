﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Coder.ScriptWorkflow.WebApi.Modules;

/// <summary>
/// 
/// </summary>
public static class CacheStartup
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="services"></param>
    /// <param name="env"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public static IServiceCollection AddMemberCache(this IServiceCollection services, IWebHostEnvironment env,
        IConfiguration configuration)
    {
        if (env.IsDevelopment())
            services.AddDistributedMemoryCache();
        else
            services.AddStackExchangeRedisCache(opt =>
            {
                opt.Configuration = configuration.GetConnectionString("redis");
            });

        services.AddMemoryCache().AddResponseCaching();
        return services;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static WebApplication UseMemberCache(this WebApplication app)
    {
        app.UseResponseCaching();
        return app;
    }
}