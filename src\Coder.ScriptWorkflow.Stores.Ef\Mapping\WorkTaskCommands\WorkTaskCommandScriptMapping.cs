﻿using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.WorkTaskCommands;

internal class WorkTaskCommandScriptMapping : IEntityTypeConfiguration<WorkTaskScriptCommand>
{
    public void Configure(EntityTypeBuilder<WorkTaskScriptCommand> builder)
    {
        builder.HasBaseType<WorkTaskCommand>();
        builder.Property(_ => _.Script).HasColumnType("text");
    }
}