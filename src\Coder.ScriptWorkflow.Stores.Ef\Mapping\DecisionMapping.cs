﻿using Coder.ScriptWorkflow.Decisions;
using Coder.ScriptWorkflow.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

internal class DecisionMapping : IEntityTypeConfiguration<Decision>
{
    public void Configure(EntityTypeBuilder<Decision> builder)
    {
        builder.HasBaseType<Node>();

        builder.Property(_ => _.MatchDescription).HasMaxLength(20);
    }
}