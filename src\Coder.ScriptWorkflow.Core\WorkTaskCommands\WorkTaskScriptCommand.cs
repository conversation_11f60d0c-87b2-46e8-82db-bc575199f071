﻿using System;
using Coder.ScriptWorkflow.Scripts;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.WorkTaskCommands;

/// <summary>
///     执行命令，通过影响processinstance.form 来决定流程流向。
/// </summary>
public class WorkTaskScriptCommand : WorkTaskCommand
{
    /// <summary>
    /// </summary>
    public WorkTaskScriptCommand()
    {
        Script = "//processInstance 是流程实例，workActivity 当前的处理实例。";
    }

    /// <summary>
    /// </summary>
    public string Script { get; set; }


    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="workActivity"></param>
    public override void Invoke(IWorkflowContext workflowContext, WorkActivity workActivity)
    {
        if (workActivity == null) throw new ArgumentNullException(nameof(workActivity));
        if (string.IsNullOrEmpty(Script)) return;


        var context = workflowContext.BuildScriptContext();

        var workActivityScript = workActivity.ToScriptModel();

        context.DefineVariable("workActivity").Assign(
            context.GlobalContext.ProxyValue(workActivityScript)
        );
        try
        {
            context.Eval(Script);
        }
        catch (JSException ex)
        
        {
           
            throw ex.ToJSException(workflowContext, "命令-" + Name, Script, workActivity.WorkTask);
        }

        context.Eval("var __RESULT=JSON.stringify(processInstance.Form)");
        var result = context.GetVariable("__RESULT").Value;
        workflowContext.ProcessInstance.ReplaceForm((string)result);
    }
}