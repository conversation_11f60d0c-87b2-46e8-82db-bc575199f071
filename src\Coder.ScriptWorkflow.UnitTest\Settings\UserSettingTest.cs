﻿using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Settings;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Settings;

public class UserSettingTest
{
    [Fact]
    public void Test()
    {
        var setting = new SwfMakeTagsSetting();
        var user = "userName".ToClaimsPrincipal();


        setting.Script = @"

return [user.name,user.userName]

";

        var s =
                setting.MakeUserTags(user, new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()))
                    .ToArray()
            ;

        Assert.Equal(s.First(), "姓名");
        Assert.Equal(s[1], "userName");
    }
}