﻿using Coder.ScriptWorkflow.Settings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

internal class SwfSettingMapping : IEntityTypeConfiguration<SwfSetting>
{
    private readonly string _prefix;

    public SwfSettingMapping(string prefix)
    {
        _prefix = prefix;
    }

    public void Configure(EntityTypeBuilder<SwfSetting> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.ToTable($"{_prefix}_swf_setting").HasComment("工作流实例"); ;
        builder.Property(_ => _.Id).ValueGeneratedOnAdd().HasComment("id");
        builder.Property(_ => _.Script).HasColumnType("text").HasComment("脚本");

        builder.HasDiscriminator();
    }
}