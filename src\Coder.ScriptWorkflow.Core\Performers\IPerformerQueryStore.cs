﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.Performers;

/// <summary>
/// </summary>
public interface IPerformerQueryStore
{
    /// <summary>
    /// </summary>
    /// <param name="roleNames"></param>
    /// <returns></returns>
    Task<IEnumerable<AssignUser>> GetByRolesAsync(string[] roleNames);

    /// <summary>
    /// </summary>
    /// <param name="orgNames"></param>
    /// <returns></returns>
    Task<IEnumerable<AssignUser>> GetByOrgAsync(string[] orgNames);

    /// <summary>
    /// </summary>
    /// <param name="users"></param>
    /// <returns></returns>
    Task<IEnumerable<AssignUser>> GetByUserNamesAsync(IEnumerable<string> users);


    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<IEnumerable<Performer>> SearchAsync(PerformerSearcher searcher);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<int> CountAsync(PerformerSearcher searcher);
}