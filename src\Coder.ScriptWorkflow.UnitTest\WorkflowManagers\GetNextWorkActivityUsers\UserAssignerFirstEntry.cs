﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using static Xunit.Assert;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers;

/// <summary>
/// </summary>
public class UserAssignerFirstEntry
{
    private static readonly UserViewModel Creator = new()
    {
        Name = "管理员",
        UserName = "creator"
    };
    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options => { options.UseSqlite($"Data Source=UserAssignerFirstEntry{dbFile}.db;"); });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using (var scope = sp.CreateScope())
        {
            var services = scope.ServiceProvider;
            var dbContext = services.GetRequiredService<UnitTestAppContext>();
            // only for unit-tet
            dbContext.Database.EnsureCreated();
        }

        return sp;
    }

    /// <summary>
    /// </summary>
    /// <param name="sp"></param>
    /// <param name="assignScopeType"></param>
    /// <returns></returns>
    private static WorkProcess SimpleAuth(IServiceProvider sp, AssignScopeType assignScopeType)
    {
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();

        var workProcess = new WorkProcess("流程-测试")
        {
            Enable = true
        };
        wpStore.AddOrUpdate(workProcess);
        wpStore.SaveChangesAsync().Wait();

        wpStore.SaveChangesAsync().Wait();

        var draf = new WorkTask("草拟", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = @"logger.Info('分配','内容')
return 'user'"
            }
        };
        draf.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "审批1",
            Script = "processInstance.Form.NextStep='审批1'"
        });

        draf.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "审批2",
            Script = "processInstance.Form.NextStep='审批2'"
        });

        var auth1 = new WorkTask("审批1", workProcess)
        {
            Assigner = new UsersAssigner
            {
                Performers = new List<Performer>
                {
                    new()
                    {
                        Key = "role",
                        Type = PerformerType.Role
                    }
                },
                AssignScopeType = assignScopeType
            }
        };
        auth1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意",
            Script = "processInstance.Form.auth1='同意'"
        });
        auth1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "拒绝",
            Script = "processInstance.Form.auth1='拒绝'"
        });

        var auth2 = new WorkTask("审批2", workProcess)
        {
            Assigner = new UsersAssigner
            {
                Performers = new List<Performer>
                {
                    new()
                    {
                        Key = "org",
                        Type = PerformerType.Org
                    }
                },
                AssignScopeType = assignScopeType
            }
        };
        auth2.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意",
            Script = "processInstance.Form.auth2='同意'"
        });
        auth2.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "拒绝",
            Script = "processInstance.Form.auth2='拒绝'"
        });
        wtStore.AddOrUpdate(draf);
        wtStore.AddOrUpdate(auth1);
        wtStore.AddOrUpdate(auth2);
        wtStore.SaveChangesAsync().Wait();

        var draftDecision = new BoolScriptDecision(auth1, auth2) { Script = "return processInstance.Form.NextStep=='审批1'" };
        draf.NextNode = draftDecision;
        var startNode = new StartNode(draf, workProcess);

        var authDecision = new BoolScriptDecision(new EndNode(), draf) { Script = "return processInstance.Form.auth2!='拒绝' && processInstance.Form.auth1!='拒绝' " };

        auth1.NextNode = authDecision;
        auth2.NextNode = authDecision;

        wtStore.AddOrUpdate(startNode);
        wtStore.AddOrUpdate(draf);
        wtStore.AddOrUpdate(auth1);
        wtStore.AddOrUpdate(auth2);
        wtStore.SaveChangesAsync().Wait();

        return workProcess;
    }

    /// <summary>
    ///     通过不同的命令行获取下一个workTask的角色/org。
    /// </summary>
    /// <param name="commandName"></param>
    /// <param name="userNamePrefix"></param>
    [Theory]
    [InlineData("审批1", "role")]
    [InlineData("审批2", "org")]
    public void Test_Auth1_NextUserInRole_SomeOfThen(string commandName, string userNamePrefix)
    {
        var sp = Sp();
        var wp = SimpleAuth(sp, AssignScopeType.SomeOfThem);
        var workflowManager = sp.GetRequiredService<WorkflowManager>();
        var startResult = workflowManager.Start(new ProcessInstance(wp, Creator.UserName), Creator);
        var workActivityId = startResult.WorkActivities.First().WorkActivityId;
        var resolveResult = workflowManager.Resolve(workActivityId, new WorkflowResolveSubmit

        {
            Command = commandName
        }, Creator);

        True(resolveResult.Success, resolveResult.Message);

        Equal(2, resolveResult.WorkActivities.Count());

        var user1 = resolveResult.WorkActivities.First();
        var user2 = resolveResult.WorkActivities.Skip(1).First();


        StartsWith(userNamePrefix, user1.DisposeUser);

        StartsWith(userNamePrefix, user2.DisposeUser);
    }

    /// <summary>
    ///     通过不同的命令行获取下一个workTask的角色/org。
    ///     这里设置是AllOFThen
    /// </summary>
    /// <param name="commandName"></param>
    /// <param name="userNamePrefix"></param>
    [Theory]
    [InlineData("审批1", "role")]
    [InlineData("审批2", "org")]
    public void Test_Auth1_NextUserInRole_AllOfThem(string commandName, string userNamePrefix)
    {
        var sp = Sp();
        var wp = SimpleAuth(sp, AssignScopeType.AllOfThem);
        var workflowManager = sp.GetRequiredService<WorkflowManager>();
        var startResult = workflowManager.Start(new ProcessInstance(wp, Creator.UserName), Creator);
        var workActivityId = startResult.WorkActivities.First().WorkActivityId;
        var users = workflowManager.Resolve(workActivityId, new WorkflowResolveSubmit

        {
            Command = commandName
        }, Creator);


        True(users.Success);

        Equal(2, users.WorkActivities.Count());
        var array = users.WorkActivities.ToArray();
        var userName1 = array[0];
        var userName2 = array[1];
        StartsWith(userNamePrefix, userName1.DisposeUser);
        StartsWith(userNamePrefix, userName2.DisposeUser);
    }
}