using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Coder.FileSystem.Clients;
using Coder.FileSystem.ViewModels;
using Coder.ScriptWorkflow.ViewModels;
using Coder.Utility;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.Services;

/// <summary>
/// 文件附件服务
/// </summary>
public class FileAttachmentService
{
    private readonly IHttpFileManager _fileManager;
    private readonly ILogger<FileAttachmentService> _logger;

    public FileAttachmentService(IHttpFileManager fileManager, ILogger<FileAttachmentService> logger)
    {
        _fileManager = fileManager ?? throw new ArgumentNullException(nameof(fileManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 获取流程实例表单中的附件数组
    /// </summary>
    /// <param name="formString">表单JSON字符串</param>
    /// <returns>附件数组，如果不存在则创建新数组</returns>
    public JArray GetOrCreateAttachments(string formString)
    {
        if (string.IsNullOrWhiteSpace(formString))
            throw new ArgumentException("表单数据不能为空", nameof(formString));

        var formObject = JObject.Parse(formString);
        var attachments = formObject["attachments"] as JArray;

        if (attachments == null)
        {
            attachments = new JArray();
            formObject["attachments"] = attachments;
        }

        return formObject["attachments"] as JArray;
    }

    /// <summary>
    /// 检查文件是否存在于附件列表中
    /// </summary>
    /// <param name="attachments">附件数组</param>
    /// <param name="fileId">文件ID</param>
    /// <returns>如果文件存在返回true</returns>
    public bool IsFileInAttachments(JArray attachments, string fileId)
    {
        if (attachments == null || string.IsNullOrWhiteSpace(fileId))
            return false;

        return attachments.Any(attachment =>
            attachment["fileId"]?.ToString() == fileId);
    }

    /// <summary>
    /// 从附件列表中删除指定文件
    /// </summary>
    /// <param name="attachments">附件数组</param>
    /// <param name="fileId">要删除的文件ID</param>
    /// <returns>是否成功删除</returns>
    public bool RemoveFileFromAttachments(JArray attachments, string fileId)
    {
        if (attachments == null || string.IsNullOrWhiteSpace(fileId))
            return false;

        var itemsToRemove = attachments
            .Where(attachment => attachment["fileId"]?.ToString() == fileId)
            .ToList();

        foreach (var item in itemsToRemove)
        {
            attachments.Remove(item);
        }

        return itemsToRemove.Any();
    }

    /// <summary>
    /// 获取文件信息
    /// </summary>
    /// <param name="fileId">文件ID</param>
    /// <returns>文件信息</returns>
    public async Task<SFileViewModel> GetFileInfoAsync(string fileId)
    {
        if (string.IsNullOrWhiteSpace(fileId))
            throw new ArgumentException("文件ID不能为空", nameof(fileId));

        try
        {
            return await _fileManager.Get(fileId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件信息失败，文件ID: {FileId}", fileId);
            throw;
        }
    }

    /// <summary>
    /// 获取文件流
    /// </summary>
    /// <param name="fileId">文件ID</param>
    /// <returns>文件流</returns>
    public Stream GetFileStream(string fileId)
    {
        if (string.IsNullOrWhiteSpace(fileId))
            throw new ArgumentException("文件ID不能为空", nameof(fileId));

        try
        {
            return _fileManager.GetStream(fileId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件流失败，文件ID: {FileId}", fileId);
            throw;
        }
    }

    /// <summary>
    /// 上传文件
    /// </summary>
    /// <param name="stream">文件流</param>
    /// <param name="fileName">文件名</param>
    /// <param name="createUser">创建用户</param>
    /// <param name="refId">关联ID</param>
    /// <returns>上传结果</returns>
    public async Task<SFileViewModel> UploadFileAsync(Stream stream, string fileName, string createUser, string refId)
    {
        if (stream == null)
            throw new ArgumentNullException(nameof(stream));
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("文件名不能为空", nameof(fileName));

        try
        {
            var submit = new SFileSubmit
            {
                CreateUser = createUser,
                Name = fileName,
                RefId = refId
            };

            return await _fileManager.PostFile(stream, submit);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传文件失败，文件名: {FileName}", fileName);
            throw;
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="fileId">文件ID</param>
    /// <returns>是否成功删除</returns>
    public bool DeleteFile(string fileId)
    {
        if (string.IsNullOrWhiteSpace(fileId))
            throw new ArgumentException("文件ID不能为空", nameof(fileId));

        try
        {
            _fileManager.Delete(fileId);
            return true; ;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件失败，文件ID: {FileId}", fileId);
            throw;
        }
    }

    /// <summary>
    /// 创建附件对象
    /// </summary>
    /// <param name="fileId">文件ID</param>
    /// <param name="fileName">文件名</param>
    /// <param name="fileSize">文件大小</param>
    /// <param name="claims">文件声明</param>
    /// <returns>附件对象</returns>
    public object CreateAttachmentObject(string fileId, string fileName, long fileSize,
        IEnumerable<UploadFileAttachment.FileClaim> claims = null)
    {

        var result = JObject.FromObject(new
        {
            fileId,
            name = fileName,
            size = fileSize,

        });
        //claims = claims ?? new List<UploadFileAttachment.FileClaim>()
        if (claims != null)
            foreach (var item in claims)
            {
                result[item.Name] = item.Value;
            }

        return result;
    }

    /// <summary>
    /// 替换特殊中文符号为对应的半角符号
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>转换后的字符串</returns>
    public static string ConvertToHalfWidth(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var characters = input.ToCharArray();
        for (var i = 0; i < characters.Length; i++)
        {
            // 全角空格转半角空格
            if (characters[i] == 12288)
            {
                characters[i] = (char)32;
                continue;
            }

            // 全角字符转半角字符
            if (characters[i] > 65280 && characters[i] < 65375)
                characters[i] = (char)(characters[i] - 65248);
        }

        return new string(characters);
    }
}