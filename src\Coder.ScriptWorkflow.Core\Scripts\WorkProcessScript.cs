﻿using System;
using Coder.ScriptWorkflow.Logger;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
/// </summary>
public class WorkProcessScript : ScriptDefined
{
    /// <summary>
    /// </summary>
    public WorkProcessScript()
    {
        Script = "//processInstance 流程实例";
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="workProcessEvents"></param>
    public void Invoke(IWorkflowContext workflowContext, string workProcessEvents)
    {
        if (workflowContext == null) throw new ArgumentNullException(nameof(workflowContext));
        if (workProcessEvents == null) throw new ArgumentNullException(nameof(workProcessEvents));

        if (string.IsNullOrEmpty(Script)) return;
        var scriptContext = workflowContext.BuildScriptContext();
        //一下分段 eval 是为了报错的时候，line指向是用户输入的部分。
        try
        {
            scriptContext.Eval(Script);
        }
        catch (JSException ex)
        {

            var runTimeException = ex.ToJSException(workflowContext, workProcessEvents, Script);
            workflowContext.SendJsExceptionDebuggerInfo(runTimeException);

            workflowContext.Logger.LogError(workflowContext,
                runTimeException.IsPluginError ? WorkflowLogType.Script : WorkflowLogType.Plugin, runTimeException);
            throw runTimeException;
        }
        catch (Exception ex)
        {
            var exce = new WorkflowDefinedException(
                $"脚本调用的插件出错(500),原因:{ex.Message}", ex);
            exce.Module = WorkProcess.ModuleOnStart;
            throw exce;
        }

        scriptContext.Eval("var __RESULT=JSON.stringify(processInstance.Form)");
        var result = scriptContext.GetVariable("__RESULT").Value;
        workflowContext.ProcessInstance.ReplaceForm((string)result);
    }
}