﻿using System.Linq;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
///     WorkTask 脚本
/// </summary>
public class WorkTaskCompleteScript : ScriptDefined
{
    public const string EventName = "工作任务结束";

    /// <summary>
    /// </summary>
    public WorkTaskCompleteScript()
    {
        Script = @"//processInstance 流程实例,对携带form的数据都会保存。
//workActivities 所有工作做活动
";
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    public void Invoke(IWorkflowContext workflowContext)
    {
        if (string.IsNullOrEmpty(Script)) return;


        var context = workflowContext.BuildScriptContext();


        context.DefineVariable("workActivities").Assign(context.GlobalContext.ProxyValue(new ScriptWorkActivityCollection(workflowContext.AllWorkActivities)));
        try
        {
            context.Eval(Script);
        }
        catch (JSException ex)
        {
            var workTask = workflowContext.AllWorkActivities.First().WorkTask;
            var runTimeException = ex.ToJSException(workflowContext, EventName, Script, workTask);
            workflowContext.SendJsExceptionDebuggerInfo(runTimeException);
            throw runTimeException;
        }

        context.Eval("var __RESULT=JSON.stringify(processInstance.Form)");
        var result = context.GetVariable("__RESULT").Value;
        workflowContext.ProcessInstance.ReplaceForm((string)result);

        workflowContext.Debugger?
            .SendMessage(workflowContext, EventName, EventName + "执行完成。");
    }
}