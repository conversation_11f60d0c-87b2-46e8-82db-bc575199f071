﻿using System.Collections.Generic;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest;

public class ScriptDecisionTest
{
    public UserViewModel Creator = new()
    {
        UserName = "creator"
    };

    /// <summary>
    ///     no deined -1 errorline
    /// </summary>
    [Fact]
    public void ScriptError()
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();

        var workflowContext = new WorkflowContext(
            new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            new ProcessInstance(wp, "user"), new List<WorkTask>
            {
                (WorkTask)connectionAction.NextNode,
                (WorkTask)connectionAction.ElseNode
            },
            new IPlugin[0],
            new List<ScriptTagInfo>(), new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()),
            Creator);


        connectionAction.Script = connectionAction.Script = @"var a=1;
var f=a1;
";
        ;

        var workActivity = new List<WorkActivity>();

        var wa = new WorkActivity(new ProcessInstance(new WorkProcess("workflow"), "user"),
            workflowContext.WorkTasks.ToArray()[1], Priority.Normal);
        wa.ProcessInstance.ReplaceForm(JsonConvert.SerializeObject(new
        {
            name = "NextNode"
        }));
        workActivity.Add(wa);
        wa.AssignTo("user");

        var ex = Assert.Throws<JsRuntimeException>(() => { connectionAction.TryNextNode(workflowContext, out _); });
        Assert.Equal(ex.Coordinates.Line, 0);
        Assert.Equal(ex.Coordinates.Column, 0);
        Assert.Equal(ex.Coordinates.Length, 0);

        Assert.Equal("判定-0", ex.NodeName);
        Assert.Equal("ReferenceError: Variable \"a1\" is not defined", ex.Message);
    }

    /// <summary>
    /// </summary>
    /// <param name="script"></param>
    /// <param name="workTaskName"></param>
    [Theory]
    [InlineData("return true", "NextNode")]
    [InlineData("return false", "otherNode")]
    [InlineData("var a=1", "NextNode")]
    [InlineData("return ''", "otherNode")]
    [InlineData("return 0", "otherNode")]
    [InlineData("return 1", "NextNode")]
    public void MatchTest(string script, string workTaskName)
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();

        var workflowContext = new WorkflowContext(new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            new ProcessInstance(wp, "user"), new List<WorkTask>
            {
                (WorkTask)connectionAction.NextNode,
                (WorkTask)connectionAction.ElseNode
            }, new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp),
            new DebuggerManager(new EmptyDebuggerPusher()), Creator);


        connectionAction.Script = script;

        var workActivity = new List<WorkActivity>();

        var wa = new WorkActivity(new ProcessInstance(new WorkProcess("workflow"), "user"),
            workflowContext.WorkTasks.ToArray()[1], Priority.Normal);

        wa.ProcessInstance.ReplaceForm(JsonConvert.SerializeObject(new
        {
            name = "NextNode"
        }));
        workActivity.Add(wa);
        wa.AssignTo("user");
        //wa.Resolve(workflowContext, "同意", "同意，没有任何问题。");

        var targets = connectionAction.TryNextNode(workflowContext, out var node);
        Assert.True(targets);
        Assert.Equal(workTaskName, ((WorkTask)node).Name);
    }

    /// <summary>
    ///     测试 多次链接多个connector-node得问题
    /// </summary>
    [Fact]
    public void MultiMatcher()
    {
        /*
         * connection1 -> Worktask1(Matcher)
         *             else-> connection2 -> worktask2(matcher)
         *                               else worktask3
         *
         */
        var wp = new WorkProcess("wp");
        var worktask1 = new WorkTask("1", wp);
        var worktask2 = new WorkTask("2", wp);
        var worktask3 = new WorkTask("3", wp);

        var taskList = new List<WorkTask>
        {
            worktask1, worktask2, worktask3
        };

        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            new ProcessInstance(wp, "user"), taskList, new IPlugin[0], new List<ScriptTagInfo>(),
            new WorkflowLogManager(sp)
            , new DebuggerManager(new EmptyDebuggerPusher()), Creator
        );


        var connectionAction1 = new BoolScriptDecision
        {
            NextNode = worktask2,
            ElseNode = new BoolScriptDecision
            {
                NextNode = worktask2,
                ElseNode = worktask3,
                Script = "return false;"
            },
            Script = "return false"
        };

        var matcher = connectionAction1.TryNextNode(workflowContext, out var nextNode);

        Assert.True(matcher);
        Assert.Equal("判定-0", ((BoolScriptDecision)nextNode).Name);

        ((BoolScriptDecision)nextNode).TryNextNode(workflowContext, out nextNode);
        Assert.Equal("3", nextNode.Name);
    }
}