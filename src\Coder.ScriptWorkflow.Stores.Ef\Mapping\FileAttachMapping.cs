﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class FileAttachMapping : IEntityTypeConfiguration<ProcessInstance.FileAttach>
{
    /// <summary>
    /// </summary>
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public FileAttachMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ProcessInstance.FileAttach> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.ToTable($"{_prefix}_files");
        builder.Property(_ => _.FileName).HasMaxLength(100);
        builder.Property(_ => _.FileId).HasMaxLength(50);
        builder.Property(_ => _.FileType);
        builder.Property(_ => _.CreateUser).HasMaxLength(100);
        builder.HasOne(_ => _.ProcessInstance);
    }
}