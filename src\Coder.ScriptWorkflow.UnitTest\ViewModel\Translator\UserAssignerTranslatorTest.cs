﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.DtoTranslator.Define.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel.Translator;

public class ScriptAssignerTranslatorTest
{
    [Fact]
    public void ToViewModel()
    {
        var transaltor = new ScriptAssignerTranslator();
        var expect = new ScriptAssigner
        {
            AssignScopeType = AssignScopeType.AllOfThem,
            Id = 1,
            Script = "alert('ok')"
        };
        var viewModel = (ScriptAssignerSubmit)transaltor.ToViewModel(expect);

        Assert.NotNull(viewModel);
        Assert.Equal(expect.AssignScopeType, viewModel.AssignScopeType);
        Assert.Equal(expect.Script, viewModel.Script);
        Assert.Equal(expect.Id, viewModel.Id);
    }
}

public class UserAssingerTranslatorTest
{
    [Fact]
    public void ToViewModel()
    {
        var transaltor = new UserAssignerTranslator();
        var expect = new UsersAssigner
        {
            AssignScopeType = AssignScopeType.AllOfThem,
            Id = 1,

            Performers = new List<Performer>
            {
                new()
                {
                    Key = "1",
                    Name = "会计",
                    Type = PerformerType.Org
                }
            }
        };
        var viewModel = (UsersAssignerSubmit)transaltor.ToViewModel(expect);

        Assert.NotNull(viewModel);
        Assert.Equal(expect.AssignScopeType, viewModel.AssignScopeType);

        Assert.Equal(expect.Id, viewModel.Id);


        var targetPerformer = viewModel.Performers[0];
        Assert.Equal(expect.Performers[0].Key, targetPerformer.Key);
        Assert.Equal(expect.Performers[0].Name, targetPerformer.Name);
        Assert.Equal(expect.Performers[0].Type, targetPerformer.Type);
    }

    [Fact]
    public void Fill()
    {
    }
}