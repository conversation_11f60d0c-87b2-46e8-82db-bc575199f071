﻿using Coder.FileSystem.ViewModels;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Coder.Utility;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Coder.ScriptWorkflow.ViewModels.FormVariableDefines;

// ReSharper disable once CheckNamespace
namespace Coder.ScriptWorkflow;

public static class ProcessInstanceDtoExtensions
{
    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    public static ProcessInstanceViewModel ToViewModel(this ProcessInstance processInstance, IProcessInstanceStore store)
    {
        if (processInstance == null) throw new ArgumentNullException(nameof(processInstance));
        var result = new ProcessInstanceViewModel();
        var tags = store.GetTagsAsync(processInstance).Result.Select(_ => new TagSubmit { Color = _.Color, Name = _.Tag.Name }).ToArray();
        result.Subject = processInstance.Subject;
        result.Priority = processInstance.Priority;
        result.Creator = processInstance.Creator;
        result.Id = processInstance.Id;
        result.WorkProcessCreator = processInstance.WorkProcess.Creator;
        result.Status = processInstance.Status;
        result.CreateTime = processInstance.CreateTime;
        result.FinishTime = processInstance.FinishTime;
        result.StartTime = processInstance.StartTime;
        result.Number = processInstance.Number;
        result.SuspendTime = processInstance.SuspendTime;
        result.SuspendComment = processInstance.SuspendComment;
        result.Comment = processInstance.Comment;
        result.WorkProcessName = processInstance.WorkProcess.Name;
        result.Form = MakeWorkProcessData(processInstance);
        result.FormDesign = processInstance.WorkProcess.FormDesign;
        result.FormManageDesign = processInstance.WorkProcess.FormManageDesign;
        result.Tags = tags;
        result.WorkProcessId = processInstance.WorkProcess.Id;
        result.WorkProcessAbbr = processInstance.WorkProcess.Abbr;
        result.WorkActivityCount = processInstance.WorkActivityCount;
        result.CanBeDeleteWorkActivity = processInstance.WorkProcess.CanBeDeleteWorkActivityCount;
        return result;
    }
    public static async Task<ProcessInstanceViewModel> ToViewModelAsync(this ProcessInstance processInstance, IProcessInstanceStore store)
    {
        if (processInstance == null) throw new ArgumentNullException(nameof(processInstance));
        var result = new ProcessInstanceViewModel();
        var tags = (await store.GetTagsAsync(processInstance)).Select(_ => new TagSubmit { Color = _.Color, Name = _.Tag.Name }).ToArray();
        result.Subject = processInstance.Subject;
        result.Priority = processInstance.Priority;
        result.Creator = processInstance.Creator;
        result.Id = processInstance.Id;
        result.WorkProcessCreator = processInstance.WorkProcess.Creator;
        result.Status = processInstance.Status;
        result.CreateTime = processInstance.CreateTime;
        result.FinishTime = processInstance.FinishTime;
        result.StartTime = processInstance.StartTime;
        result.Number = processInstance.Number;
        result.SuspendTime = processInstance.SuspendTime;
        result.SuspendComment = processInstance.SuspendComment;
        result.Comment = processInstance.Comment;
        result.WorkProcessName = processInstance.WorkProcess.Name;
        //result.Form = MakeWorkProcessData(processInstance);
        //result.FormDesign = processInstance.WorkProcess.FormDesign;
        result.FormManageDesign = processInstance.WorkProcess.FormManageDesign;
        result.Tags = tags;
        result.WorkProcessId = processInstance.WorkProcess.Id;
        result.WorkProcessAbbr = processInstance.WorkProcess.Abbr;
        result.WorkActivityCount = processInstance.WorkActivityCount;
        result.CanBeDeleteWorkActivity = processInstance.WorkProcess.CanBeDeleteWorkActivityCount;
        return result;
    }
    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="workActivity"></param>
    /// <returns></returns>
    public static Object MakeWorkProcessData(ProcessInstance processInstance, WorkActivity workActivity = null)
    {
        var form = JsonConvert.DeserializeObject<JObject>(processInstance.Form) ?? new JObject(new object());

        form.Add("WP_name", processInstance.WorkProcess.Name);
        form.Add("WP_version", processInstance.WorkProcess.Version);

        form.Add(FormVariableDefines.ProcessInstance.Number, processInstance.Number);

        form.Add(FormVariableDefines.ProcessInstance.CreateTime, processInstance.CreateTime);
        form.Add(FormVariableDefines.ProcessInstance.Creator, processInstance.Creator);
        form.Add(FormVariableDefines.ProcessInstance.Subject, processInstance.Subject);
        form.Add(FormVariableDefines.ProcessInstance.Comment, processInstance.Comment);
        form.Add("PI_status", processInstance.Status.GetEnumDisplayName());
        form.Add("PI_id", processInstance.Id);
        form.Add(FormVariableDefines.ProcessInstance.CreatorName, processInstance.CreatorName);
        if (workActivity != null)
        {
            form.Add(FormVariableDefines.WorkActivity.DisposeUser, workActivity.DisposeUser);
            form.Add(FormVariableDefines.WorkActivity.DisposeUserName, workActivity.DisposeUserName);
            form.Add(FormVariableDefines.WorkActivity.Status, workActivity.Status.GetEnumDisplayName());
            form.Add(FormVariableDefines.WorkActivity.Comment, workActivity.Comment);
            form.Add(FormVariableDefines.WorkTask.Name, workActivity.WorkTask.Name);
        }

        return form;
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static InstanceListItemViewModel ToListItem(this ProcessInstance processInstance, IProcessInstanceStore store)
    {
        if (processInstance == null) throw new ArgumentNullException(nameof(processInstance));
        var tags = store.GetTagsAsync(processInstance).Result.Select(_ => _.ToViewModel());
        return processInstance.ToListItem(tags);
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="tags"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static InstanceListItemViewModel ToListItem(this ProcessInstance processInstance, IEnumerable<ProcessInstanceTagViewModel> tags)
    {
        if (processInstance == null) throw new ArgumentNullException(nameof(processInstance));
        var result = new InstanceListItemViewModel
        {
            Subject = processInstance.Subject,
            Creator = processInstance.Creator,
            CreatorName = processInstance.CreatorName,
            Id = processInstance.Id,
            Status = processInstance.Status,
            CreateTime = processInstance.CreateTime,
            FinishTime = processInstance.FinishTime,
            Number = processInstance.Number,
            Comment = processInstance.Comment,
            ProcessInstancePriority = processInstance.Priority,
            WorkProcessName = processInstance.WorkProcess?.Name,
            Tags = tags.ToArray().Select(_ => _.ToViewModel()).ToArray(),
            WorkProcessId = processInstance.WorkProcess?.Id,
            WorkActivityCount = processInstance.WorkActivityCount,
            CanDeleteWorkActivityCount = processInstance.WorkProcess?.CanBeDeleteWorkActivityCount ?? 0
        };

        return result;
    }

    /// <summary>
    ///     转换processInstance 为Tag
    /// </summary>
    /// <param name="tag"></param>
    /// <returns></returns>
    public static TagViewModel ToViewModel(this ProcessInstanceTagViewModel tag)
    {
        return new TagViewModel
        {
            Color = tag.Color,
            Name = tag.Name,
            CanDelete = tag.CanDelete
        };
    }

    /// <summary>
    /// </summary>
    /// <param name="tag"></param>
    /// <returns></returns>
    public static ProcessInstanceTagViewModel ToViewModel(this ProcessInstanceTag tag)
    {
        return new ProcessInstanceTagViewModel
        {
            Color = tag.Color,
            Name = tag.Tag.Name,
            ProcessInstanceId = tag.ProcessInstance.Id,
            CanDelete = tag.CanDelete
        };
    }

    public static ProcessInstanceFileViewModel ToViewModel(this ProcessInstance.FileAttach fileAttach, IEnumerable<SimpleSFileViewModel> fsfiles=null)
    {
        SimpleSFileViewModel fsfile = null;
        if (fsfiles!= null)
         fsfile = fsfiles.Where(_ => _.Id == fileAttach.FileId).FirstOrDefault();
        return new ProcessInstanceFileViewModel
        {
            CreateUser = fileAttach.CreateUser,
            FileId = fileAttach.FileId,
            FileName = fileAttach.FileName,
            FileLength = fsfile?.FileSize??0,
        };
    }
}