﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

/// <inheritdoc cref="" />
public class WorkTaskStore<T> : IWorkTaskStore where T : DbContext
{
    private readonly T _dbContext;

    /// <inheritdoc cref="" />
    public WorkTaskStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    public Task<WorkTask> GetByNameAsync(WorkProcess process, string name)
    {
        return _dbContext.Set<WorkTask>().FirstOrDefaultAsync(workTask => workTask.Name == name && workTask.WorkProcess.Id == process.Id);
    }

    /// <inheritdoc cref="" />
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <inheritdoc cref="" />
    public void Delete(Node node)
    {
        switch (node)
        {
            case WorkTask workNode:
                if (workNode.Commands.Any())
                    _dbContext.RemoveRange(workNode.Commands);
                if (workNode.WorkActivityCompleteScript != null)
                    _dbContext.Remove(workNode.WorkActivityCompleteScript);
                if (workNode.WorkTaskCompleteScript != null)
                    _dbContext.Remove(workNode.WorkTaskCompleteScript);
                break;
        }

        _dbContext.Remove(node);
    }

    /// <inheritdoc cref="" />
    public void Delete(int id)
    {
        var t = GetById(id);
        _dbContext.Remove(t);
    }

    /// <inheritdoc cref="" />
    public WorkTask GetById(int id)
    {
        return _dbContext.Set<WorkTask>().FirstOrDefault(workTask => workTask.Id == id);
    }

    public Task<WorkTask> GetByIdAsync(int id)
    {
        return _dbContext.Set<WorkTask>().FirstOrDefaultAsync(workTask => workTask.Id == id);
    }

    /// <inheritdoc cref="" />
    public WorkTask GetByName(WorkProcess process, string name)
    {
        return _dbContext.Set<WorkTask>().FirstOrDefault(workTask => workTask.Name == name && workTask.WorkProcess.Id == process.Id);
    }

    /// <inheritdoc cref="" />
    public IEnumerable<WorkTask> GetWorkTasks(ProcessInstance processInstance)
    {
        return _dbContext.Set<WorkTask>().Where(workTask => workTask.WorkProcess.Id == processInstance.WorkProcess.Id).ToList();
    }

    /// <inheritdoc cref="" />
    public void AddOrUpdate(WorkTask workTask)
    {
        if (workTask.Id != 0)
            _dbContext.Update(workTask);
        else
            _dbContext.Add(workTask);
    }

    /// <inheritdoc cref="" />
    public void AddOrUpdate(StartNode startNode)
    {
        if (startNode.Id != 0)
            _dbContext.Update(startNode);
        else
            _dbContext.Add(startNode);
    }

    /// <inheritdoc cref="" />
    public bool Exist(int workProcessId, string name, int? notIncludeId)
    {
        var id = notIncludeId ?? 0;
        return _dbContext.Set<WorkTask>()
            .Any(workTask => workTask.WorkProcess.Id == workProcessId && workTask.Name == name && workTask.Id != id);
    }

    /// <inheritdoc cref="" />
    public Task<WorkTask> GetFirstWorkTaskAsync(WorkProcess wp)
    {
        if (wp == null) throw new ArgumentNullException(nameof(wp));
        return _dbContext.Set<WorkTask>()
            .FirstOrDefaultAsync(workTask => workTask.WorkProcess == wp);
    }

    /// <inheritdoc cref="" />
    public Task<TScript> GetScriptAsync<TScript>(int id) where TScript : ScriptDefined
    {
        return _dbContext.Set<TScript>().FirstOrDefaultAsync(scriptDefined => scriptDefined.Id == id);
    }

    /// <inheritdoc cref="" />
    public void SaveScript<TScript>(TScript script) where TScript : ScriptDefined
    {
        _dbContext.Update(script);
    }

    /// <inheritdoc cref="" />
    public Task<WorkTaskScriptCommand> GetScriptCommandAsync(int submitId)
    {
        return _dbContext.Set<WorkTaskScriptCommand>().FirstOrDefaultAsync(command => command.Id == submitId);
    }

    /// <inheritdoc cref="" />
    public void SaveCommand(WorkTaskScriptCommand command)
    {
        _dbContext.Update(command);
    }
}