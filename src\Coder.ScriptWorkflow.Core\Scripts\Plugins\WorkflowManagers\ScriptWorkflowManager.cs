﻿using System;
using Coder.ScriptWorkflow.ViewModels;
using NiL.JS.Core;
using Array = NiL.JS.BaseLibrary.Array;

namespace Coder.ScriptWorkflow.Scripts.Plugins.WorkflowManagers;

/// <summary>
/// </summary>
public class ScriptWorkflowManager
{
    private readonly IWorkflowContext _context;
    private readonly WorkflowManager _manager;

    /// <summary>
    /// </summary>
    /// <param name="manager"></param>
    /// <param name="context"></param>
    /// <exception cref="ArgumentNullException">manager is null</exception>
    /// <exception cref="ArgumentNullException">context is null</exception>
    public ScriptWorkflowManager(WorkflowManager manager, IWorkflowContext context)
    {
        _manager = manager ?? throw new ArgumentNullException(nameof(manager));
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    ///     设置CC-user
    /// </summary>
    /// <param name="user"></param>
    /// <exception cref="ArgumentNullException">user 为空</exception>
    // ReSharper disable once InconsistentNaming
    public void SetCCUser(string user)
    {
        if (user == null || user.Trim() == string.Empty) throw new ArgumentNullException(nameof(user));
        _context.DistributionUsers.Add(user);
    }


    /// <summary>
    /// </summary>
    /// <param name="user"></param>
    /// <exception cref="ArgumentNullException">user 为空</exception>
    public void RemoveCcUser(string user)
    {
        if (user == null || user.Trim() == string.Empty) throw new ArgumentNullException(nameof(user));
        _context.DistributionUsers.Remove(user);
    }

    /// <summary>
    ///     设置tag
    /// </summary>
    /// <param name="tagName"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void SetTag(string tagName)
    {
        if (string.IsNullOrEmpty(tagName))
            throw new ArgumentNullException(nameof(tagName));
        _context.AddTag(tagName, null, false);
    }

    /// <summary>
    /// </summary>
    /// <param name="all"></param>
    /// <returns></returns>
    public Array GetFlowItem(bool all)
    {
        var a = _manager.GetFlow(_context.ProcessInstance.Id, all);

        var ary = new Array();
        foreach (var item in a) ary.Add(ToJsObject(item));

        return ary;
    }

    /// <summary>
    /// </summary>
    /// <param name="v"></param>
    /// <returns></returns>
    private static JSObject ToJsObject(FlowViewModel v)
    {
        var result = JSObject.CreateObject();
        result[nameof(v.TaskName)] = v.TaskName;
        result[nameof(v.Id)] = v.Id;
        result[nameof(v.DisposeUserName)] = v.DisposeUserName;

        result[nameof(v.WorkActivityDescription)] = v.WorkActivityDescription;
        result[nameof(v.Command)] = v.Command;
        result[nameof(v.Comment)] = v.Comment;
        result[nameof(v.CreateTime)] = v.CreateTime.ToString("yyyy-MM-dd HH:mm:ss");


        result[nameof(v.AssignTime)] = v.AssignTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? JSValue.Null;


        if (v.DisposeTime == null)
            result[nameof(v.DisposeTime)] = JSValue.Null;
        else
            result[nameof(v.DisposeTime)] = v.DisposeTime.Value.ToString("yyyy-MM-dd HH:mm:ss");

        return result;
    }
}