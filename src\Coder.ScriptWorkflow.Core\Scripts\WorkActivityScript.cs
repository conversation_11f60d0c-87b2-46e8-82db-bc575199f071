﻿using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using NiL.JS.Extensions;

namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
/// </summary>
public class WorkActivityScript : ScriptDefined
{
    public const string WorkActivityScriptEventName = "工作活动结束事件";

    /// <summary>
    /// </summary>
    public WorkActivityScript()
    {
        Script = "//processInstance 流程实例";
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <returns>true 所属的工作任务已经全部结束，进行下一步。 返回 false，那么当前工作活动不能结束。</returns>
    public bool Invoke(IWorkflowContext workflowContext)
    {
        if (string.IsNullOrEmpty(Script)) return true;
        var scriptContext = workflowContext.BuildScriptContext();

        var script = $@"
var __CALLER = () =>{{
            {Script}
            }}";
       

        scriptContext.DefineVariable("workActivities")
            .Assign(scriptContext.GlobalContext.ProxyValue(new ScriptWorkActivityCollection(workflowContext.RelativeWorkActivities)));

        scriptContext.DefineVariable("workActivity").Assign(scriptContext.GlobalContext.ProxyValue(workflowContext.CurrentWorkActivity.ToScriptModel()));
        try
        {
            scriptContext.Eval(script);
        }
        catch (JSException ex)
        {
            var runTimeException = ex.ToJSException(workflowContext, WorkActivityScriptEventName,
                script, workflowContext.CurrentWorkActivity.WorkTask);

            workflowContext.SendJsExceptionDebuggerInfo(runTimeException);

            workflowContext.Logger.LogError(workflowContext,
                runTimeException.IsPluginError ? WorkflowLogType.Plugin : WorkflowLogType.Script,
                runTimeException);

            throw runTimeException;
        }

        var concatFunction = scriptContext.GetVariable("__CALLER").As<Function>();
        var result = concatFunction.Call(new Arguments());

        //如果脚本 有返回值，并且返回的值是 bool 型，那么就由脚本判定是否结束当前的工作任务。
        if (result.ValueType == JSValueType.Boolean)
            return (bool)result.Value;
        //否则就默认结束当前工作任务。
        return true;
    }
}