﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net9.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="nlog.config" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="nlog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Coder.Orgs.Clients.Http" Version="6.1.1" />
    <PackageReference Include="FluentAssertions" Version="8.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.7" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="JunitXml.TestLogger" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Coder.ScriptWorkflow.Abstractions\Coder.ScriptWorkflow.Abstractions.csproj" />
    <ProjectReference Include="..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
    <ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.Mssql\Coder.ScriptWorkflow.Migrations.Mssql.csproj" />
    <ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.Mysql\Coder.ScriptWorkflow.Migrations.Mysql.csproj" />
    <ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.Sqlite\Coder.ScriptWorkflow.Migrations.Sqlite.csproj" />
    <ProjectReference Include="..\Coder.ScriptWorkflow.Stores.Ef\Coder.ScriptWorkflow.Stores.Ef.csproj" />
    <ProjectReference Include="..\Coder.ScriptWorkflow.WebApi\Coder.ScriptWorkflow.WebApi.csproj" />
    <ProjectReference Include="..\Plugins\ScriptWorkflow.Scripts.Plugin.JsHttpClient\Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient.csproj" />
    <ProjectReference Include="..\Plugins\ScriptWorkflow.Scripts.Plugin.Member\Coder.ScriptWorkflow.Scripts.Plugin.Member.csproj" />
    <ProjectReference Include="..\Plugins\ScriptWorkflow.Scripts.Plugin.MySql\Coder.ScriptWorkflow.Scripts.Plugin.MySql.csproj" />
  </ItemGroup>

</Project>
