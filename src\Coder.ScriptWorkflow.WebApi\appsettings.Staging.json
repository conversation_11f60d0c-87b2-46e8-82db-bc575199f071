﻿{
  "ConnectionStrings": {
    "dm": "server=************:30236;database=coder_swf;user=STORAGE;password=*********;",
    "mysql": "server=localhost;port=3306;database=coder_swf;user=root;password=*********;CharSet=utf8;",
    "mssql": "Server=localhost;database=coder_swf;user=sa;password=**********;",
    "redis": "************:6379,defaultDatabase=9"
  },

  "DbType": "MYSQL",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.*": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "NLog": {

    "LokiHost": "http://************:3100" //docker 的名字
  },

  "gateway": "http://localhost:801/api",

  "ContractServer": "http://localhost:5200",

  "TokenService": {
    "client": "ScriptWorkflow",
    "SecretKey": "316"
  },

  "ConsulOption": {
    "ConsulServer": "http://************:8500", /* consul server */
    "ServiceHost": "_HOST_", /* 要注册的服务 url */
    "ServicePort": -1, /* 要注册服务的访问端口 */
    "ServiceName": "Coder.ScriptWorkflow", /* 服务名称，集群的时候采用这个名称来获取服务 */
    "ServiceId": "Coder.ScriptWorkflow-dev", /* 服务id 必须唯一，用于记录那个服务提供者出现问题 */
    "Tags": [ "" ], /* 可选 日志*/
    "HealthCheckUrl": "http://_HOST_:_PORT_/Health/Status",
    "Enable": false
  }
}