﻿using Coder.ScriptWorkflow.WebApi;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow.UnitTest.Http.Helper;

public class TestStartup : Startup
{
    public TestStartup(IConfiguration configuration, IWebHostEnvironment env) : base(configuration, env)
    {
    }


    protected override void ConsulSetup(IServiceCollection services)
    {
    }
}