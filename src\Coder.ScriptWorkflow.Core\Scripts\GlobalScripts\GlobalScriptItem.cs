﻿namespace Coder.ScriptWorkflow.Scripts.GlobalScripts;

/// <summary>
/// 全局脚本
/// </summary>
public class GlobalScriptItem
{
    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     文件名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     脚本
    /// </summary>
    public string Script { get; set; }
}

public enum GlobalVariableEnv
{
    Client,
    Server,
    Both
}


public class GlobalVariable
{
    public int Id { get; set; }
    public string Name { get; set; }
    /// <summary>
    ///  值
    /// </summary>
    public string Variable { get; set; }

    /// <summary>
    /// 变量类型
    /// </summary>
    public GlobalVariableEnv Env { get; set; }


}