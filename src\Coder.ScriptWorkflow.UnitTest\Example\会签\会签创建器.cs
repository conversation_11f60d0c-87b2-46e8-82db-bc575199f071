﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;

namespace Coder.ScriptWorkflow.UnitTest.Example.会签;

internal class FlowCreator
{
    public static WorkProcessSubmit 会签创建器()
    {
        var submi = new WorkProcessSubmit
        {
            Name = "demo-会签",
            Prefix = "Demo",
            Id = 5,
            Version = 1,
            Enable = true,
            OnCompleteScript = null,
            OnStartScript = "processInstance.Form.会签同意=false"
        };
        submi.Nodes = new List<NodeSubmit>();
        var startNodeSubmit = new StartNodeSubmit
        {
            NextNodeName = "草稿"
        };
        submi.Nodes.Add(startNodeSubmit);

        var 草稿 = new WorkTaskSubmit
        {
            Name = "草稿",
            NextNodeName = "会签审批",
            Commands = new List<WorkTaskCommandSubmit>
            {
                new WorkTaskScriptCommandSubmit
                {
                    Name = "提交",
                    Script = "processInstance.Form.会签同意=false"
                }
            },
            Assigner = new ScriptAssignerSubmit
            {
                Script = "return processInstance.Creator"
            }
        };
        submi.Nodes.Add(草稿);
        var 会签审批 = new WorkTaskSubmit
        {
            Name = "会签审批",
            NextNodeName = "会签检查",
            Commands = new List<WorkTaskCommandSubmit>
            {
                new WorkTaskScriptCommandSubmit
                {
                    Name = "拒绝",
                    Script = "processInstance.Form.会签同意=false"
                },
                new WorkTaskScriptCommandSubmit
                {
                    Name = "同意",
                    Script = "processInstance.Form.会签同意=true"
                }
            },
            Assigner = new ScriptAssignerSubmit
            {
                Script = "return ['manager1','manager2','manager3']"
            },
            WorkActivityCompleteScript = new WorkTaskScriptSubmit
            {
                Script = "var wa=workActivities.Get(0);\r\nreturn workActivities.AllDone || !processInstance.Form.会签同意"
            }
        };
        submi.Nodes.Add(会签审批);


        var 会签检查 = new BooleanScriptDecisionSubmit
        {
            MatchDescription = "全部同意",
            ElseNodeName = "草稿",
            Name = "会签检查",
            NextNodeName = "结束",
            Script = "return processInstance.Form.会签同意"
        };

        submi.Nodes.Add(会签检查);
        submi.Nodes.Add(new EndNodeSubmit());
        return submi;
    }
}