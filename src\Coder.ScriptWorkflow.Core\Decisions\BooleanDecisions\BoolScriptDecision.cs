﻿using System;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using NiL.JS.Extensions;

namespace Coder.ScriptWorkflow.Decisions.BooleanDecisions;

/// <summary>
///     脚本判定器
/// </summary>
public class BoolScriptDecision : BooleanDecision
{
    /// <inheritdoc />
    public BoolScriptDecision()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="nextNode"></param>
    /// <param name="elseNode"></param>
    public BoolScriptDecision(Node nextNode, Node elseNode)
    {
        ElseNode = elseNode ?? throw new ArgumentNullException(nameof(elseNode));
        NextNode = nextNode ?? throw new ArgumentNullException(nameof(nextNode));
    }

    /// <inheritdoc />
    public override bool Auto
    {
        get => true;

        set { }
    }

    /// <summary>
    /// </summary>
    public string Script { get; set; } = @"//processInstance 流程实例，workActivities 当前工作任务的对应的workActivity。";

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="nextNode"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    /// <exception cref="WorkflowDefinedException"></exception>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        if (NextNode == null)
            throw new ArgumentNullException(nameof(NextNode), "NextNode没有设定。");
        if (ElseNode == null)
            throw new ArgumentNullException(nameof(NextNode), "ElseNode没有设定。");
        if (string.IsNullOrEmpty(Script))
            throw new WorkflowDefinedException("判断器 script没有定义。", workflowContext.CurrentNode.Name);


        nextNode = ExecuteScript(workflowContext);
        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <returns></returns>
    /// <exception cref="JsRuntimeException"></exception>
    private Node ExecuteScript(IWorkflowContext workflowContext)
    {
        var context = workflowContext.BuildScriptContext();
        var script = @$"
var __CALLER = () =>{{
            {Script}
            }}";

        var previous = workflowContext.AllWorkActivities;

        var was = new ScriptWorkActivityCollection(previous ?? new WorkActivity[0]);
        context.DefineVariable("workActivities").Assign(context.GlobalContext.ProxyValue(was));
        try
        {
            context.Eval(script);
            var concatFunction = context.GetVariable("__CALLER").As<Function>();
            var jsValue = concatFunction.Call(new Arguments());

            var match = JudgeBoolean(jsValue);
            workflowContext.SendMessageDebugInfo("判断器 jsValue = " + jsValue.Value + ";,判断为:" + match);
            var result = match ? NextNode : ElseNode;
            return result;
        }
        catch (JSException ex)
        {
            throw ex.ToJSException(workflowContext, null, script, this);
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    private bool JudgeBoolean(JSValue jsValue)
    {
        if (jsValue.IsNull)
            return false;
        switch (jsValue.ValueType)
        {
            case JSValueType.Boolean:
                return (bool)jsValue.Value;
            case JSValueType.Double:
                return (double)jsValue.Value != 0;
            case JSValueType.Integer:
                return (int)jsValue.Value != 0;
            case JSValueType.String:
                return !string.IsNullOrEmpty((string)jsValue.Value);
            case JSValueType.NotExistsInObject:
            case JSValueType.NotExists:
                return true;
            default:
                return false;
        }
    }
}