﻿declare var jsHttpClient: jsHttpClientClass;

/**
 * jsHttpClient
 */
interface jsHttpClientClass {
    /**
     * 通过Get
     * @param url url
     * @queryStringObject js 对象，自动拼接为
     */
    GetJson(url: string, queryStringObject: Object): any;
    /**
     * 
     * @param url 访问的url
     * @param jObject 提交的json
     */
    PostJson(url: string, jObject: Object): any;
    /**
     * 
     * @param url
     * @param jObject
     */
    PutJson(url: string, jObject: Object): any;

    /**
     * 调用Delte 方法
     * @param url
     */
    Delete(url: string): any;

}