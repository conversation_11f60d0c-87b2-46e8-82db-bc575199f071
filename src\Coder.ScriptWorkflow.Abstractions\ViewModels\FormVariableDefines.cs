﻿namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     表单变量定义类，用于统一管理JSON表单中使用的内置变量名称和前缀。
///     此类定义了工作流中各种实体（如工作活动、流程实例、工作任务）的表单变量。
/// </summary>
public static class FormVariableDefines
{
    /// <summary>
    ///     工作活动相关的表单变量定义
    /// </summary>
    public static WorkActivityDefined WorkActivity = new();
    /// <summary>
    ///     流程实例相关的表单变量定义
    /// </summary>
    public static ProcessInstanceDefine ProcessInstance = new();
    /// <summary>
    ///     工作任务相关的表单变量定义
    /// </summary>
    public static WorkTaskDefine WorkTask = new();

    /// <summary>
    ///     表单自动注入的变量前缀数组。
    ///     当表单数据回填时，需要排除以这些前缀开头的字段。
    ///     这些前缀分别对应：工作任务(WT_)、流程实例(PI_)、工作活动(WA_)、工作流程(WP_)
    /// </summary>
    public static string[] FormInjectVariablePrefixNames =
    {
        "WT_", "PI_", "WA_", "WP_"
    };

    /// <summary>
    ///     工作活动相关的表单变量定义类
    ///     包含工作活动在表单中使用的各种变量名称
    /// </summary>
    public class WorkActivityDefined
    {
        /// <summary>
        ///     工作活动评论字段名
        ///     值：WA_comment，用于存储工作活动的处理建议或评论
        /// </summary>
        public readonly string Comment = "WA_comment";

        /// <summary>
        ///     工作活动处理时间字段名
        ///     值：WA_disposeTime，用于存储工作活动的处理时间
        /// </summary>
        public readonly string DisposeTime = "WA_disposeTime";

        /// <summary>
        ///     工作活动处理人字段名
        ///     值：WA_disposeUser，用于在表单中显示当前处理人
        /// </summary>
        public readonly string DisposeUser = "WA_disposeUser";
        /// <summary>
        ///     工作活动下一步处理人字段名
        ///     值：WA_nextDisposeUser，从客户端获取，用于指定下一步处理人
        /// </summary>
        public readonly string NextDisposeUser = "WA_nextDisposeUser";

        /// <summary>
        ///     工作活动处理人姓名字段名
        ///     值：WA_disposeUserName，用于存储处理人的姓名
        /// </summary>
        public readonly string DisposeUserName = "WA_disposeUserName";

        /// <summary>
        ///     工作活动状态字段名
        ///     值：WA_status，用于存储工作活动的当前状态
        /// </summary>
        public readonly string Status = "WA_status";
    }

    /// <summary>
    ///     流程实例相关的表单变量定义类
    ///     包含流程实例在表单中使用的各种变量名称
    /// </summary>
    public class ProcessInstanceDefine
    {
        /// <summary>
        ///     流程实例创建时间字段名
        ///     值：PI_createTime，用于存储流程实例的创建时间
        /// </summary>
        public readonly string CreateTime = "PI_createTime";

        /// <summary>
        ///     流程实例创建人字段名
        ///     值：PI_creator，用于存储流程实例的创建人ID
        /// </summary>
        public readonly string Creator = "PI_creator";
        /// <summary>
        ///     流程实例创建人姓名字段名
        ///     值：PI_creator_name，用于存储流程实例创建人的姓名
        /// </summary>
        public readonly string CreatorName = "PI_creator_name";
        /// <summary>
        ///     流程实例抄送人字段名
        ///     值：PI_cc，用于存储流程实例的抄送人
        /// </summary>
        public readonly string Cc = "PI_cc";
        /// <summary>
        ///     流程实例编号字段名
        ///     值：PI_number，用于存储流程实例的编号
        /// </summary>
        public readonly string Number = "PI_number";
        /// <summary>
        ///     流程实例评论字段名
        ///     值：PI_comment，用于存储流程实例的评论信息
        /// </summary>
        public readonly string Comment = "PI_comment";

        /// <summary>
        ///     流程实例优先级字段名
        ///     值：PI_priority，用于存储流程实例的优先级
        /// </summary>
        public readonly string Priority = "PI_priority";
        /// <summary>
        ///     流程实例主题字段名
        ///     值：PI_subject，用于存储流程实例的主题
        /// </summary>
        public readonly string Subject = "PI_subject";
        /// <summary>
        ///     流程实例标签字段名
        ///     值：PI_tags，用于存储流程实例的标签
        /// </summary>
        public readonly string Tags = "PI_tags";
      
    }

    /// <summary>
    ///     工作任务相关的表单变量定义类
    ///     包含工作任务在表单中使用的各种变量名称
    /// </summary>
    public class WorkTaskDefine
    {
        /// <summary>
        ///     工作任务名称字段名
        ///     值：WA_name，用于存储工作任务的名称
        /// </summary>
        public readonly string Name = "WA_name";
    }
}