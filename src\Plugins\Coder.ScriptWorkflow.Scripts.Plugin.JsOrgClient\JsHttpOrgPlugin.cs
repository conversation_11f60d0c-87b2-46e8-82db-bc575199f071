﻿
using Coder.Orgs.Clients;
using Coder.ScriptWorkflow.Scripts.Plugins;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient;

/// <summary>
/// </summary>
public class JsHttpOrgPlugin : IPlugin
{
    private readonly IOrgClient _client;

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    public JsHttpOrgPlugin(IOrgClient client)
    {
        _client = client;
    }

    /// <summary>
    /// </summary>
    public string Name { get; set; } = "jsOrg";

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new OrgHttpClient(_client);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        var stream = typeof(JsHttpOrgPlugin).Assembly
            .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient.defined.d.ts");
        if (stream == null)
            throw new ArgumentNullException("内置文件不存在");

        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}