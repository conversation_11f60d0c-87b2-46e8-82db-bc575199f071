﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.Object2Report;
using Coder.Utility;

namespace Coder.ScriptWorkflow.Exporters;

/// <summary>
/// </summary>
public class ProcessInstanceDetailExporter : Report<ProcessInstance>
{
    /// <summary>
    /// </summary>
    /// <param name="users"></param>
    /// <param name="workActivities"></param>
    public ProcessInstanceDetailExporter(List<UserViewModel> users, List<WorkActivity> workActivities)
    {
        this.Column("工单号", i => i.Number);
        this.Column("工单主题", i => i.Subject);
        this.Column("工单状态", i => i.Status.GetEnumDisplayName());
        this.Column("创建时间", i => i.CreateTime.ToString("yyyy-MM-dd"));
        this.Column("完成时间", i => i.FinishTime == null ? "未完成" : ((DateTimeOffset)i.FinishTime).ToString("yyyy-MM-dd"));
        this.Column("提单人", i => i.Creator != null ? users.FirstOrDefault(u => u.UserName == i.Creator)?.Name : null);
        this.Column("工单流程", i => getWorkFlow(i, workActivities, users));
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="workActivities"></param>
    /// <param name="users"></param>
    /// <returns></returns>
    public string getWorkFlow(ProcessInstance processInstance, List<WorkActivity> workActivities,
        List<UserViewModel> users)
    {
        var workflows = workActivities.Where(_ => _.ProcessInstance == processInstance);

        var workflowParam = string.Empty;
        foreach (var workflow in workflows)
        {
            workflowParam += workflow.WorkTask?.Name + "\n";
            if (workflow.DisposeTime == null)
            {
                workflowParam += "状态：处理中\n" + "由（" +
                                 users.FirstOrDefault(u => u.UserName == workflow.DisposeUser)?.Name + "）处理";
            }
            else
            {
                workflowParam += "状态：已处理，处理时间：" + workflow.DisposeTime + "\n";
                workflowParam += "由（" + users.FirstOrDefault(u => u.UserName == workflow.DisposeUser)?.Name + "）处理，" +
                                 "处理建议：" + workflow.Comment + "\n";
            }
        }

        return workflowParam;
    }

    /// <summary>
    /// </summary>
    /// <param name="data"></param>
    /// <param name="file"></param>
    public void Export(IEnumerable<ProcessInstance> data, string file)
    {
        this.WriteToXlsx(data, file);
    }
}