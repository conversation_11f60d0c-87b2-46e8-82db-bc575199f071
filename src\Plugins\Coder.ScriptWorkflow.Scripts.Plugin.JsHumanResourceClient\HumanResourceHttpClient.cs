﻿using Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient.HumanResource;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.OpenXmlFormats.Dml.Diagram;
using NPOI.SS.Formula.Functions;
using NPOI.XSSF.Streaming.Values;
using Org.BouncyCastle.Utilities;
using System;
using System.Collections;
using System.Drawing;
using System.Net.Http;
using System.Security.Cryptography;
using System.Security.Policy;
using System.Text;
namespace Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient
{

    public class HumanResourceHttpClient
    {
        private readonly IWorkflowContext _context;
        private readonly HumanResourceHttpFactory _humanResourceHttpFactory;


        public HumanResourceHttpClient(IWorkflowContext context, HumanResourceHttpFactory humanResourceHttpFactory)
        {
            _context = context;
            _humanResourceHttpFactory = humanResourceHttpFactory;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="submitList"></param>
        public HumanResourceVO GetHumanResource(String userName)
        {
            var response = _humanResourceHttpFactory.GetAsync(userName, "human/resource/get_human_resource_by_user_name").Result;
            var humanResource = new HumanResourceVO
            {
                userName = userName
            };
            if (!"".Equals(response))
            {
                var json = JsonConvert.DeserializeObject<JObject>(response);
                if (json.TryGetValue("buXiuJiaRemainingDays", out var buXiuJiaRemainingDays))
                {
                    var buXiuJiaRemainingDaysStr = buXiuJiaRemainingDays.Value<string>();

                    if (buXiuJiaRemainingDaysStr == null || "null".Equals(buXiuJiaRemainingDaysStr))
                        humanResource.buXiuJiaRemainingDays = 0;
                    else
                        humanResource.buXiuJiaRemainingDays = buXiuJiaRemainingDays.Value<double>();
                }
                if (json.TryGetValue("remainingDays", out var remainingDays))
                {
                    var ss = remainingDays.Value<string>();
                    if (ss == null || "null".Equals(ss))
                        humanResource.remainingDays = 0;
                    else
                        humanResource.remainingDays = remainingDays.Value<double>();
                }

            }
            else
            {
                humanResource.buXiuJiaRemainingDays = 0;
                humanResource.remainingDays = 0;
            }
            return humanResource;

        }

        /// <summary>
        /// 人事管理部分用到的流程，包括加班、请假、出差流程。
        /// </summary>
        /// <param name="submit"></param>
        /// <returns></returns>
        public int SaveHumanResource()
        {
            var json = JsonConvert.DeserializeObject
                <JObject>(_context.ProcessInstance.Form);
            var type = "出差";
            if (json.TryGetValue("humanResourceType", out var humanResourceType))
                type = humanResourceType.Value<string>();
            List<HumanResourceSubmit> submitList;
            switch (type)
            {
                case "出差":
                    GetSubmitFromChuChai(json, out submitList);
                    break;
                case "加班":
                    GetSubmitFromJiaBan(json, out submitList);
                    break;
                case "请假":
                    GetSubmitFromQingJia(json, out submitList);
                    break;
                default:
                    throw new WorkflowDefinedException("返回值中的业务类型type=" + type + "没有被识别");
            }
            SaveHumanResource(submitList);
            return submitList.Count;
        }

        /// <summary>
        /// 出差
        /// </summary>
        /// <param name="json"></param>
        /// <param name="submitList"></param>
        private void GetSubmitFromChuChai(JObject json, out List<HumanResourceSubmit> submitList)
        {
            submitList = new List<HumanResourceSubmit>();
            var renyuans = new ArrayList();
            if (json.TryGetValue("renyuan", out var renyuanStr))
            {
                foreach (var renyuan in renyuanStr)
                {
                    renyuans.Add(renyuan.Value<string>());
                }
            }

            var times = new ArrayList();
            if (json.TryGetValue("time", out var timeStr))
            {
                foreach (var time in timeStr)
                {
                    times.Add(time.Value<string>());
                }
            }
            var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
            var startDateUtc = Convert.ToDateTime(times[0]);
            var endDateUtc = Convert.ToDateTime(times[0]);
            var startDate = TimeZoneInfo.ConvertTimeFromUtc(startDateUtc, chinaTimeZone);
            var endDate = TimeZoneInfo.ConvertTimeFromUtc(endDateUtc, chinaTimeZone);
            //var startDate = Convert.ToDateTime(times[0]);
            //var endDate = Convert.ToDateTime(times[1]);

            foreach (var renyuan in renyuans)
            {
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var submit = new HumanResourceSubmit
                    {
                        humanResourceType = HumanResourceType.出差,
                        orderType = "出差",
                        orderId = _context.ProcessInstance.Id,
                        orderNo = _context.ProcessInstance.Number,
                        humanResourceuration = 1,
                        userName = (string)renyuan,
                        humanResourceDate = date,
                        timeDescription = "全天"
                    };
                    submitList.Add(submit);
                }

            }

        }
        /// <summary>
        /// 请假
        /// </summary>
        /// <param name="json"></param>
        /// <param name="submitList"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void GetSubmitFromQingJia(JObject json, out List<HumanResourceSubmit> submitList)
        {
            submitList = new List<HumanResourceSubmit>();
            HumanResourceType humanResourceType = HumanResourceType.其他;
            if (json.TryGetValue("afType", out var afTypeToken))
            {
                var humanResourceTypeStr = afTypeToken.Value<string>();
                var ishave = Enum.TryParse(humanResourceTypeStr, true, out humanResourceType);
                if (!ishave)
                {
                    humanResourceType = HumanResourceType.其他;
                }
            }

            var times = new ArrayList();
            if (json.TryGetValue("afDate", out var afDateStr))//请假时段
            {
                foreach (var time in afDateStr)
                {
                    times.Add(time.Value<string>());
                }
            }
            var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
            var startDateUtc = Convert.ToDateTime(times[0]);
            var endDateUtc = Convert.ToDateTime(times[0]);
            var startDate = TimeZoneInfo.ConvertTimeFromUtc(startDateUtc, chinaTimeZone);
            var endDate = TimeZoneInfo.ConvertTimeFromUtc(endDateUtc, chinaTimeZone);
            //var startDate = Convert.ToDateTime(times[0]);
            //var endDate = Convert.ToDateTime(times[1]);
            var i = 0;
            for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
            {
                if (i > 0)
                {
                    date = (DateTime)DateHelp.GetDateWorkS(date);
                }
                var humanResourceuration = GetHumanResourceurationQingJia(date, endDate, out string timeDescription);
                var submit = new HumanResourceSubmit
                {
                    humanResourceType = humanResourceType,
                    orderType = "请假",
                    orderId = _context.ProcessInstance.Id,
                    orderNo = _context.ProcessInstance.Number,
                    humanResourceuration = humanResourceuration,
                    userName = _context.ProcessInstance.Creator,
                    humanResourceDate = date,
                    timeDescription = timeDescription
                };
                submitList.Add(submit);
                i++;
            }
        }

        private double GetHumanResourceurationQingJia(DateTime date, DateTime endDate, out string timeDescription)
        {
            var hours = 0.0;
            var endTime = endDate;
            if (date.Date != endDate.Date)
                endTime = (DateTime)DateHelp.GetDateWorkE(date);
            if (date.Hour < 12)//开始是上午
            {
                if (endTime.Hour <= 12)//结束时间是上午
                {
                    hours = 4.0;
                    timeDescription = "上午";
                }
                else
                {
                    hours = 8.0;
                    timeDescription = "全天";
                }
            }
            else
            {
                hours = 4.0;
                timeDescription = "下午";
            }
            return Math.Round((hours / 8.0), 2);
        }

        /// <summary>
        /// 加班
        /// </summary>
        /// <param name="json"></param>
        /// <param name="submitList"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void GetSubmitFromJiaBan(JObject json, out List<HumanResourceSubmit> submitList)
        {
            submitList = new List<HumanResourceSubmit>();

            HumanResourceType humanResourceType = HumanResourceType.工作和公休日加班;
            var humanResourceTypeStr = "公休日";
            if (json.TryGetValue("woType", out var woTypeToken))
            {
                humanResourceTypeStr = woTypeToken.Value<string>();
                if ("法定休假日".Equals(humanResourceTypeStr))
                {
                    humanResourceType = HumanResourceType.法定节假日加班;
                }
            }
            var duoshijian = 1;//多时间段
            if (json.TryGetValue("duoshijian", out var duoshijianToken))
                duoshijian = duoshijianToken.Value<int>();
            for (var tmp = 1; tmp <= duoshijian; tmp++)
            {
                var woTime = tmp == 1 ? "woTime" : "woTime" + tmp;
                var times = new ArrayList();
                var humanResourceuration = 0.0;
                if (json.TryGetValue(woTime, out var woTimeStr))
                {
                    foreach (var time in woTimeStr)
                    {
                        times.Add(time.Value<string>());
                    }
                }
                var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                var startDateUtc = Convert.ToDateTime(times[0]);
                var endDateUtc = Convert.ToDateTime(times[0]);
                var startDate = TimeZoneInfo.ConvertTimeFromUtc(startDateUtc, chinaTimeZone);
                var endDate = TimeZoneInfo.ConvertTimeFromUtc(endDateUtc, chinaTimeZone);

                var i = 0;
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    if (i > 0)
                    {
                        date = (DateTime)DateHelp.GetDateWorkS(date);
                    }
                    //计算时间
                    humanResourceuration = GetHumanResourceurationJiaBan(date, endDate, humanResourceTypeStr, out string timeDescription);
                    var submit = new HumanResourceSubmit
                    {
                        humanResourceType = humanResourceType,
                        orderType = "加班",
                        orderId = _context.ProcessInstance.Id,
                        orderNo = _context.ProcessInstance.Number,
                        humanResourceuration = humanResourceuration,
                        userName = _context.ProcessInstance.Creator,
                        humanResourceDate = date,
                        timeDescription = timeDescription
                    };
                    submitList.Add(submit);
                    i++;
                }
            }


        }
        /// <summary>
        /// 获取加班时长
        /// </summary>
        /// <param name="date"></param>
        /// <param name="endDate"></param>
        /// <param name="i"></param>
        /// <returns></returns>
        private static double GetHumanResourceurationJiaBan(DateTime date, DateTime endDate, string humanResourceTypeStr, out string timeDescription)
        {

            var endTime = endDate;
            if (date.Date != endDate.Date)
                endTime = (DateTime)DateHelp.GetDateWorkE(date);
            timeDescription = date.ToString("HH:mm:ss") + "-" + endTime.ToString("HH:mm:ss");
            double hours = (endTime - date).TotalHours;

            double result = 0.0;
            if ("公休日".Equals(humanResourceTypeStr))
            {
                if (hours <= 4) result = 0.5;
                else if (hours <= 8) result = 1;
                else result = 1.5;
            }
            else
            {
                if (hours <= 4) result = 0.5;
                else result = 1;
            }
            return result;
        }

        private void SaveHumanResource(List<HumanResourceSubmit> submitList)
        {
            long unixTimeStamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
            submitList.ForEach((_) =>
            {
                _.batchNo = _.orderNo + "_" + unixTimeStamp;
                _humanResourceHttpFactory.PostAsync(_, "human/resource/log/addAndUpdateMain").Wait();
            });
        }


    }
}
