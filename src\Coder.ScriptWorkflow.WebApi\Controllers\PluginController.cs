﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize]
public class PluginController : Controller
{
    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="sp"></param>
    /// <returns></returns>
    [HttpGet("js-define")]
    public IActionResult JsDefine([FromQuery] string[] name, [FromServices] IServiceProvider sp)
    {
        var s = sp.GetServices<IPlugin>().Where(_ => name.Contains(_.Name))
            .Select(_ => new { name = _.Name, define = _.GetJsDefined() });
        return Ok(s.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="sp"></param>
    /// <returns></returns>
    [HttpGet("list-plugins")]
    public IActionResult GetPlugins([FromServices] IServiceProvider sp)
    {
        var a = sp.GetServices<IPlugin>().Select(_ => _.Name);
        return Ok(a.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="workProcessStore"></param>
    /// <param name="sp"></param>
    /// <returns></returns>
    [HttpGet("work-process-js-defined/{id}")]
    [ProducesDefaultResponseType(typeof(SwfResult<string>))]
    public SwfResult<string> GetWorkProcess([FromRoute] int id, [FromServices] IWorkProcessStore workProcessStore,
        [FromServices] IServiceProvider sp)
    {
        var workprcess = workProcessStore.Get(id);
        var plugins = workprcess.Plugins;
        IEnumerable<string> script = null;
        if (plugins == null || plugins.Count == 0)
            script = sp.GetServices<IPlugin>().Select(_ => _.GetJsDefined());
        else
            script = sp.GetServices<IPlugin>().Where(_ => plugins.Contains(_.Name)).Select(_ => _.GetJsDefined());
        return string.Join("\r\n", script).ToSuccess();
    }
}