﻿using Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;
using Newtonsoft.Json;

namespace Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;

/// <summary>
/// </summary>
[JsonConverter(typeof(WorkTaskCommandSubmitJsonConverter))]
public class WorkTaskCommandSubmit
{
    /// <summary>
    /// </summary>
    public WorkTaskCommandSubmit()
    {
        Type = $"{GetType().FullName}, Coder.ScriptWorkflow.Abstractions";
    }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>

    public string Name { get; set; }

    /// <summary>
    ///     排序规则
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// </summary>
    [JsonProperty("$type")]
    public string Type { get; set; }

    public virtual bool Validate(out string errorMessage)
    {
        errorMessage = null;
        if (string.IsNullOrEmpty(Name))
        {
            errorMessage = "命令名称必须填写";
            return false;
        }

        return true;
    }
}