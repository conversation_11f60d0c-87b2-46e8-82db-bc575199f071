﻿using Coder.ScriptWorkflow.Scripts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ScriptDefinedMapping : IEntityTypeConfiguration<ScriptDefined>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public ScriptDefinedMapping(string prefix)
    {
        _prefix = prefix;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ScriptDefined> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.HasDiscriminator()
            .HasValue<WorkProcessScript>("WorkProcessScript")
            .HasValue<WorkActivityScript>("WorkTaskScript")
            .HasValue<WorkTaskStartScript>("WorkTaskStartScript")
            .HasValue<WorkTaskCompleteScript>("WorkTaskCompleteScript")
            ;
        builder.Property(_ => _.Id);
        builder.Property(_ => _.Script).HasMaxLength(6000);
        builder.ToTable($"{_prefix}_scripts");
    }
}