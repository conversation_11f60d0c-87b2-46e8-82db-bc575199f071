﻿namespace Coder.VBenNotify.Interceptor;

/// <summary>
///     vben 客户端 通知的 格式
///     其中 title 都可以应用于 通知/消息/工作
///     description，只能用于 消息/工作
/// </summary>
public class VBenMessageBody
{
    /// <summary>
    /// </summary>
    public string? id { get; set; }

    /// <summary>
    /// </summary>
    public string? avatar { get; set; }

    /// <summary>
    /// </summary>
    public string? title { get; set; }

    /// <summary>
    /// </summary>
    public string? description { get; set; }

    /// <summary>
    /// </summary>
    public bool? clickClose { get; set; }

    /// <summary>
    /// </summary>
    public string? extra { get; set; }

    public string? type { get; set; }

    /// <summary>
    /// </summary>
    public string? color { get; set; }

    /// <summary>
    ///     VBen ListItem中的 msgType ，用于再次细分类型
    /// </summary>
    public string msgType { get; set; }

    /// <summary>
    /// </summary>
    public string? datetime { get; set; }

    /// <summary>
    ///     通知
    /// </summary>
    /// <param name="id"></param>
    /// <param name="title"></param>
    /// <param name="dateTime"></param>
    /// <returns></returns>
    public static VBenMessageBody Notification(string id, string title, string dateTime, string msgType, string avatar = "Channel16Regular|svg")
    {
        var result = new VBenMessageBody
        {
            id = id,
            avatar = avatar,
            title = title,
            description = "",
            datetime = dateTime,
            msgType = msgType,
            type = "1",
            clickClose = true
        };
        return result;
    }

    public static VBenMessageBody Message(string id, string title, string description, string? datetime, string msgType, string avatar = "Channel16Regular|svg")
    {
        var result = new VBenMessageBody();
        result.id = id;

        result.avatar = avatar;

        result.title = title;
        result.description = description;
        result.datetime = datetime;
        result.type = "2";
        result.clickClose = true;
        result.msgType = msgType;
        return result;
    }

    public static VBenMessageBody Work(string id, string title, string description, string? datetime, string msgType, string avatar = "Channel16Regular|svg")
    {
        var result = new VBenMessageBody();
        result.id = id;

        result.avatar = avatar;

        result.title = title;
        result.description = description;
        result.datetime = datetime;
        result.type = "3";
        result.clickClose = true;
        result.msgType = msgType;
        return result;
    }
}