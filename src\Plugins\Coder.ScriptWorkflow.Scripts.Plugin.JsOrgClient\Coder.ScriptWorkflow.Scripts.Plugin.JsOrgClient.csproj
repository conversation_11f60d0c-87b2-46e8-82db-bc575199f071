﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="defined.d.ts" />
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Include="defined.d.ts">
			<SubType>Code</SubType>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Coder.Member.Clients.Http" Version="9.0.2" />
		<PackageReference Include="Coder.Orgs.Clients.Http" Version="6.1.1" />		
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
	</ItemGroup>

</Project>
