﻿using Coder.ScriptWorkflow;
using Coder.ScriptWorkflow.Interceptors;

namespace Coder.VBenMenu.Interceptor;

/// <summary>
/// </summary>
public class VbenWorkProcessInterceptor : IWorkProcessInterceptor
{
    private readonly MenuManager menuManager;

    public VbenWorkProcessInterceptor(MenuManager menuManager)
    {
        this.menuManager = menuManager;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <exception cref="WorkflowDefinedException"></exception>
    public void OnCreate(WorkProcess workProcess)
    {
        if (menuManager == null) throw new WorkflowDefinedException("MenuManager没有配置，请采用VBenMenuExtendions.AddVBen 。");
        menuManager.ToUpdateMenu(workProcess, false);
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <exception cref="WorkflowDefinedException"></exception>
    public void OnUpdate(WorkProcess workProcess)
    {
        if (menuManager == null) throw new WorkflowDefinedException("MenuManager没有配置，请采用VBenMenuExtendions.AddVBen 。");
        menuManager.ToUpdateMenu(workProcess, false);
    }
}