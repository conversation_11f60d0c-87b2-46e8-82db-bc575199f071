﻿using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.WorkTaskCommands;

/// <summary>
/// </summary>
internal class PreviousWorkTaskCommandMapping : IEntityTypeConfiguration<PreviousWorkTaskCommand>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<PreviousWorkTaskCommand> builder)
    {
        builder.HasBaseType<WorkTaskScriptCommand>();
        builder.Property(_ => _.Script).HasColumnType("text");
    }
}