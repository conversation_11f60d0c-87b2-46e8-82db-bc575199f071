﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.ProcessInstances;

/// <summary>
///     保存form数据
/// </summary>
public class ProcessInstanceFormSubmit : IJsonFormSubmit
{
    /// <summary>
    ///     主题. 客户端提交无需填写，他的值来自于Form的PI_Subject
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    ///    优先级. 客户端提交无需填写，他的值来自于Form的 PI_priority
    /// </summary>
    public Priority? Priority { get; set; }

    /// <summary>
    /// Tags. 客户端提交无需填写，他的值来自于Form的 PI_tags
    /// </summary>
    public IEnumerable<TagSubmit> Tags { get; set; }

    /// <summary>
    ///     数据表
    /// </summary>
    public string Form { get; set; }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public JObject FillSelfByJsonForm()
    {
        if (string.IsNullOrWhiteSpace(Form))
            return new JObject();
        var jsonObject = JObject.Parse(Form);

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Subject, out var jValueSubject)) Subject = jValueSubject.Value<string>();
        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Priority, out var jValuePriority))

        {
            var number = Convert.ToInt32(jValuePriority.Value<int>());
            Priority = (Priority)number;
        }

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Tags, out var tags))
        {
            var submitTags = new List<TagSubmit>();
            for (var i = 0; i < tags.Count(); i++)
            {
                var tag = tags[i].ToString();
                submitTags.Add(JsonConvert.DeserializeObject<TagSubmit>(tag));
            }

            Tags = submitTags;
        }

        return jsonObject;
    }
}