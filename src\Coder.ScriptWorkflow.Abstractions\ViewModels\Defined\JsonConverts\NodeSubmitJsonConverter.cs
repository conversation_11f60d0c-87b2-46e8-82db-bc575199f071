﻿using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;

/// <summary>
/// </summary>
public class NodeSubmitJsonConverter : JsonCreationConverter<NodeSubmit>
{
    /// <inheritdoc />
    protected override NodeSubmit Create(Type objectType, JObject jObject)
    {
        if (jObject == null) throw new ArgumentNullException("jObject");
        var job = jObject["$type"];
        if (job == null)
            throw new ArgumentOutOfRangeException(nameof(jObject),
                JsonConvert.SerializeObject(jObject) + "没有$type");
        var typeName = job.Value<string>();
        switch (typeName)
        {
            case "Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskSubmit, Coder.ScriptWorkflow.Abstractions":
                return new WorkTaskSubmit();

            case
                "Coder.ScriptWorkflow.ViewModels.Defined.BooleanScriptDecisionSubmit, Coder.ScriptWorkflow.Abstractions"
                :
                return new BooleanScriptDecisionSubmit();
            case "Coder.ScriptWorkflow.ViewModels.Defined.ConditionDecisionSubmit, Coder.ScriptWorkflow.Abstractions":
                return new ConditionDecisionSubmit();
         

            case "Coder.ScriptWorkflow.ViewModels.Defined.StartNodeSubmit, Coder.ScriptWorkflow.Abstractions":
                return new StartNodeSubmit();

            case "Coder.ScriptWorkflow.ViewModels.Defined.EndNodeSubmit, Coder.ScriptWorkflow.Abstractions":
                return new EndNodeSubmit();

            default:
                throw new ArgumentOutOfRangeException("$type", jObject["$type"].Value<string>() + " not matcher.");
        }
    }
}