﻿using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Scripts.ViewModels;

/// <summary>
/// </summary>
public class ScriptWorkActivityCollection
{
    private readonly IList<ScriptWorkActivity> _workActivities;

    /// <summary>
    /// </summary>
    public ScriptWorkActivityCollection()
    {
        _workActivities = new List<ScriptWorkActivity>();
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivities"></param>
    public ScriptWorkActivityCollection(IEnumerable<WorkActivity> workActivities)
    {
        _workActivities = workActivities.Select(_ => _.ToScriptModel()).ToList();
    }

    /// <summary>
    /// </summary>
    public int Count => _workActivities.Count;

    /// <summary>
    ///     是否全部完成
    /// </summary>
    /// <returns></returns>
    public bool AllDone
    {
        get
        {
            if (!_workActivities.Any())
                return true;
            int[] done =
            {
                (int)WorkActivityStatus.CloseByAdmin,
                (int)WorkActivityStatus.Complete
            };

            return _workActivities.All(_ => done.Contains(_.Status));
        }
    }

    /// <summary>
    ///     通过处理用户
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public ScriptWorkActivity FindByDisposeUser(string user)
    {
        return _workActivities.FirstOrDefault(_ => _.DisposeUser == user);
    }

    /// <summary>
    /// </summary>
    /// <param name="index"></param>
    /// <returns></returns>
    public ScriptWorkActivity Get(int index)
    {
        if (index >= _workActivities.Count) return null;

        return _workActivities[index];
    }
}