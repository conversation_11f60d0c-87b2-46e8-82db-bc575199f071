﻿using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.WorkTaskCommands;

/// <summary>
/// </summary>
internal class WorkTaskCommandMapping : IEntityTypeConfiguration<WorkTaskCommand>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public WorkTaskCommandMapping(string prefix)
    {
        _prefix = prefix;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<WorkTaskCommand> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.ToTable($"{_prefix}_WorkTaskCommand");
        builder.Property(_ => _.Name).HasMaxLength(100);
        builder.Property(_ => _.Order);
        builder.HasDiscriminator().HasValue<WorkTaskScriptCommand>("WorkTaskCommandScript");
    }
}