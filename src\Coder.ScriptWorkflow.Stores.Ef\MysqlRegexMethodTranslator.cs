﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class RegexE
{
    public static bool IsMatch(IEnumerable<Performer> column, string pattern)
    {
        throw new ArgumentException();
    }
}

/// <summary>
/// </summary>
public static class DbFunctions
{
    [DbFunction("JSON_VALUE", "")]
    public static string JsonValue(string column, [NotParameterized] string path)
    {
        throw new NotSupportedException();
    }
}