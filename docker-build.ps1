﻿$register = "zhcoder-docker-registry.com:8000/coder"
$image="coder-script-workflow-webapi"


cd src
$xml = [Xml] (Get-Content ./Coder.ScriptWorkflow.WebApi/Coder.ScriptWorkflow.WebApi.csproj)
$version = [Version] $xml.Project.PropertyGroup.Version
IF([string]::IsNullOrEmpty($version)) {            
   $version="1.0.0"            
} 
echo "版本:" ${version}

$nugetCachePath = [System.Environment]::GetEnvironmentVariable('USERPROFILE') + '\.nuget\packages'
echo "cache:" $nugetCachePath

docker buildx build --build-arg NUGET_CACHE=$nugetCachePath --push --platform=linux/amd64 -t ${register}/${image}:${version} -f Coder.ScriptWorkflow.WebApi/Dockerfile . 

cd ..
