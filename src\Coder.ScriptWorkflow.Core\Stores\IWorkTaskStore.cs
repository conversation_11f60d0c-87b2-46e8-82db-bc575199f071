﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.WorkTaskCommands;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IWorkTaskStore
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    void Delete(Node node);

    /// <summary>
    ///     删除WorkTask
    /// </summary>
    /// <param name="id"></param>
    void Delete(int id);

    /// <summary>
    ///     通过id获取worktask
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkTask GetById(int id);

    /// <summary>
    /// 获取WorkTaskId
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<WorkTask> GetByIdAsync(int id);

    /// <summary>
    ///     通过Name获取
    /// </summary>
    /// <param name="process"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    WorkTask GetByName(WorkProcess process, string name);
    /// <summary>
    /// 通过Name获取
    /// </summary>
    /// <param name="process"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<WorkTask> GetByNameAsync(WorkProcess process, string name);

    /// <summary>
    ///     保存
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <returns></returns>
    IEnumerable<WorkTask> GetWorkTasks(ProcessInstance processInstance);

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate([NotNull] WorkTask workTask);

    void AddOrUpdate([NotNull] StartNode startNode);

    /// <summary>
    /// </summary>
    /// <param name="workProcessId"></param>
    /// <param name="name"></param>
    /// <param name="notIncludeId"></param>
    /// <returns></returns>
    bool Exist(int workProcessId, string name, int? notIncludeId);

    /// <summary>
    /// </summary>
    /// <param name="wp"></param>
    /// <returns></returns>
    Task<WorkTask> GetFirstWorkTaskAsync(WorkProcess wp);

    /// <summary>
    /// </summary>
    /// <typeparam name="TScript"></typeparam>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<TScript> GetScriptAsync<TScript>(int id) where TScript : ScriptDefined;

    /// <summary>
    /// </summary>
    /// <typeparam name="TScript"></typeparam>
    /// <param name="script"></param>
    /// <returns></returns>
    void SaveScript<TScript>(TScript script) where TScript : ScriptDefined;

    Task<WorkTaskScriptCommand> GetScriptCommandAsync(int submitId);
    void SaveCommand(WorkTaskScriptCommand command);
}