﻿using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
public abstract class JsonCreationConverter<T> : JsonConverter
{
    /// <inheritdoc />
    public override bool CanWrite => false;

    /// <summary>
    /// </summary>
    /// <param name="objectType"></param>
    /// <param name="jObject"></param>
    /// <returns></returns>
    protected abstract T Create(Type objectType, JObject jObject);

    /// <inheritdoc />
    public override bool CanConvert(Type objectType)
    {
        return typeof(T).IsAssignableFrom(objectType);
    }

    /// <inheritdoc />
    public override object ReadJson(JsonReader reader, Type objectType, object existingValue,
        JsonSerializer serializer)
    {
        if (reader == null) throw new ArgumentNullException(nameof(reader));
        if (serializer == null) throw new ArgumentNullException(nameof(serializer));
        if (reader.TokenType == JsonToken.Null)
            return null;

        var jObject = JObject.Load(reader);
        var target = Create(objectType, jObject);
        serializer.Populate(jObject.CreateReader(), target);
        return target;
    }

    /// <inheritdoc />
    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }
}