﻿<?xml version="1.0" encoding="utf-8"?>

<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="/var/log/scriptWorkflow/internal-nlog.txt">

	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore" />
		<add assembly="NLog.Loki" />
	</extensions>

	<!-- the targets to write to -->
	<targets>
		<!-- write logs to file  -->
		<target xsi:type="File" name="allfile" fileName="/var/log/scriptWorkflow/nlog-all-${shortdate}.log"
					maxArchiveDays="30" archiveAboveSize="104857600"
					layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />

		<!-- another file log, only own logs. Uses some ASP.NET core renderers -->
		<target xsi:type="File" name="ownFile-web" fileName="/var/log/scriptWorkflow/nlog-own-${shortdate}.log"
					maxArchiveDays="30" archiveAboveSize="104857600"
					layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />

		<!-- the targets to write to -->


		<target
			name="loki"
			xsi:type="loki"
			batchSize="200"
			taskDelayMilliseconds="500"
			endpoint="${configsetting:item=NLog.LokiHost} "
	 
			orderWrites="true"
			compressionLevel="noCompression"
			layout="${level}|${message}${onexception:|${exception:format=type,message,method:maxInnerExceptionLevel=5:innerFormat=shortType,message,method}}|source=${logger}"
			eventPropertiesAsLabels="false"
	  
	 >

			<!-- Prefer using a JsonLayout as defined below, instead of using a layout as defined above -->
			<layout xsi:type="JsonLayout" includeEventProperties="true">
				<attribute name="user" layout="${aspnet-user-identity:whenEmpty=陌生人}"/>

				<attribute name="ng-client-ip" layout="${aspnet-request-headers:HeaderNames=X-Forwarded-For:whenEmpty=没记录}"/>
				<attribute name="asp-client-ip" layout="${aspnet-request-ip:whenEmpty=没记录}"/>
				<attribute name="eventId" layout="${event-properties:item=EventId_Id}"/>
				<attribute name="eventName" layout="${event-properties:item=EventId_Name}"/>
				<attribute name="asp-url" layout="${aspnet-request-url}"/>
				<attribute name="asp-action" layout="${aspnet-mvc-action}"/>


				<attribute name="level" layout="${level} "/>
				<attribute name="source" layout="${logger}" />
				<attribute name="message" layout="${message}" />
				<attribute name="exception" encode="false">
					<layout xsi:type="JsonLayout">
						<attribute name="type" layout="${exception:format=type}" />
						<attribute name="message" layout="${exception:format=message}" />
						<attribute name="stacktrace" layout="${exception:format=tostring}" />
					</layout>
				</attribute>
			</layout>

			<!-- Grafana Loki requires at least one label associated with the log stream. 
      Make sure you specify at least one label here. -->
			<label name="app" layout="ScriptWorkflow-2.4" />
			<label name="server" layout="${hostname:lowercase=true}" />
		</target>

	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<!--All logs, including from Microsoft-->
		<logger name="*" minlevel="Info" writeTo="allfile" />
		<logger name="*" minlevel="Warn" writeTo="loki" />
		<!--Skip non-critical Microsoft logs and so log only own logs-->
		<logger name="Microsoft.*" maxlevel="Warn" final="true" />
		<!-- BlackHole without writeTo -->
		<logger name="*" minlevel="Info" writeTo="ownFile-web" />
	</rules>
</nlog>