dotnet build ./src/Coder.ScriptWorkflow.Abstractions/Coder.ScriptWorkflow.Abstractions.csproj --configuration Release
dotnet pack  ./src/Coder.ScriptWorkflow.Abstractions/Coder.ScriptWorkflow.Abstractions.csproj -o ./nuget -c Release


dotnet build ./src/Coder.ScriptWorkflow.HttpClients/Coder.ScriptWorkflow.HttpClients.csproj --configuration Release
dotnet pack  ./src/Coder.ScriptWorkflow.HttpClients/Coder.ScriptWorkflow.HttpClients.csproj -o ./nuget -c Release


# Navigate to the nuget directory
Set-Location -Path ./nuget

# Push all NuGet packages
Get-ChildItem -Filter *.nupkg | ForEach-Object {
    dotnet nuget push $_.FullName -s "publish" --skip-duplicate
}