﻿using System;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.UnitTest.WorkflowManagers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Example.会签;

public class Test
{
    private static UserViewModel Creator = new UserViewModel()
    {
        UserName = "creator",
    };
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://***********:8081/api/fs";
            options.AddEfStores<UnitTestAppContext>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options => { options.UseSqlite($"Data Source={dbFile}.db;"); });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();
        scope.Dispose();
        return sp;
    }

    [Fact]
    public void Retry()
    {
        var sp = Sp();

        var workSubmit = FlowCreator.会签创建器();

        var workflowDefinedManager = sp.GetRequiredService<WorkflowDefinedManager>();
        workSubmit.OverWrite = false;
        workflowDefinedManager.Save(workSubmit);

        var workflowManager = sp.GetRequiredService<WorkflowManager>();

        var processInstance = workflowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = workSubmit.Name }, "user");
        var startResult = workflowManager.Start(processInstance, Creator);
        Assert.True(startResult.Success, startResult.Message);
        var draftId = startResult.WorkActivities.First().WorkActivityId;

        var draftWorkActivity = workflowManager.GetWorkActivityById(draftId);
        var startNode = (StartNodeSubmit)workSubmit.Nodes.First(_ => _.Name == StartNodeSubmit.StartNodeName);
        Assert.Equal(startNode.NextNodeName, draftWorkActivity.WorkTask.Name);

        var draftResolveResult = workflowManager.Resolve(draftId, new WorkflowResolveSubmit
        {
            Command = "提交",
            Comment = "提交"
        }, Creator);
        Assert.True(draftResolveResult.Success);
        Assert.Equal(3, draftResolveResult.WorkActivities.Count());
        //3个审批人，其中一人不同意，返回。
        var atuhWorkActivityIds = draftResolveResult.WorkActivities.ToArray();
        var authResult1 = workflowManager.Resolve(atuhWorkActivityIds[0].WorkActivityId, new WorkflowResolveSubmit
        {
            Command = "同意",
            Comment = "test"
        }, Creator);
        Assert.True(authResult1.Success);
        Assert.Empty(authResult1.WorkActivities);

        var authResult2 = workflowManager.Resolve(atuhWorkActivityIds[1].WorkActivityId, new WorkflowResolveSubmit
        {
            Command = "拒绝",
            Comment = "test"
        }, Creator);
        Assert.True(authResult2.Success);

        //第三个任务会被取消
        var authResult3 = workflowManager.GetWorkActivityById(atuhWorkActivityIds[2].WorkActivityId);
        Assert.Equal(WorkActivityStatus.CloseByAdmin, authResult3.Status);

        //应该是退回到draft
        draftId = authResult2.WorkActivities.First().WorkActivityId;
        draftWorkActivity = workflowManager.GetWorkActivityById(draftId);
        Assert.Equal(startNode.NextNodeName, draftWorkActivity.WorkTask.Name);
        draftResolveResult = workflowManager.Resolve(draftId, new WorkflowResolveSubmit
        {
            Command = "提交",
            Comment = "提交"
        }, Creator);

        //全部同意，结束
        foreach (var waId in draftResolveResult.WorkActivities)
        {
            var authResult = workflowManager.Resolve(waId.WorkActivityId, new WorkflowResolveSubmit
            {
                Command = "同意",
                Comment = "test"
            }, Creator);
            Assert.True(authResult.Success);
        }

        processInstance = workflowManager.GetById(processInstance.Id);
        Assert.Equal(ProcessInstanceStatus.Completed, processInstance.Status);
    }
}