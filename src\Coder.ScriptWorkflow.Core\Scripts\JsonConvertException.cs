﻿using System;

namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
/// </summary>
public class JsonConvertException : WorkflowException
{
    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    public JsonConvertException(string message) : base(message)
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="innerException"></param>
    public JsonConvertException(string message, Exception innerException) : base(message, innerException)
    {
    }
}