﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.Member;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.Permissions.Cache;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Permissions;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Coder.ScriptWorkflow.ViewModels.WorkActivities;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class WorkflowPermissionManager
{
    private readonly WorkProcessPermissionCacheManager _cacheManager;
    private readonly IWorkProcessPermissionStore _permissionStore;
    private readonly IProcessInstanceDistributionStore _processInstanceDistributionStore;

    private readonly ITagStore _tagStore;


    /// <summary>
    /// </summary>
    /// <param name="permissionStore"></param>
    /// <param name="processInstanceDistributionStore"></param>
    /// <param name="tagStore"></param>
    /// <param name="cacheManager"></param>
    public WorkflowPermissionManager(IWorkProcessPermissionStore permissionStore,
        IProcessInstanceDistributionStore processInstanceDistributionStore,
        ITagStore tagStore,
        WorkProcessPermissionCacheManager cacheManager)
    {
        _permissionStore = permissionStore;
        _processInstanceDistributionStore = processInstanceDistributionStore;
        _tagStore = tagStore;

        _cacheManager = cacheManager;
    }

    public static bool IsAdmin(ClaimsPrincipal user)
    {
        return user.IsInRole(ScriptWorkflowDefined.SystemAdmin) ||
               user.IsInRole(ScriptWorkflowDefined.WorkflowManager);
    }

    /// <summary>
    /// </summary>
    /// <param name="user"></param>
    /// <param name="processInstance"></param>
    /// <returns></returns>
    public bool HasStop(ClaimsPrincipal user, ProcessInstance processInstance)
    {
        //检查
        var permissions =
            _cacheManager.Get(processInstance.WorkProcess.Name,
                _permissionStore); // _permissionStore.GetByProcessNameAsync(processInstance.WorkProcess.Name).Result;
        foreach (var role in permissions.ManageRoles.Split(','))
            if (user.IsInRole(role))
                return true;
        return false;
    }

    /// <summary>
    ///     是否有权限挂起
    /// </summary>
    /// <param name="user"></param>
    /// <param name="processInstance"></param>
    /// <returns></returns>
    public bool HasSuspend(ClaimsPrincipal user, ProcessInstance processInstance)
    {
        //检查
        var permissions = _cacheManager.Get(processInstance.WorkProcess.Name, _permissionStore);
        foreach (var role in permissions.ManageRoles.Split(','))
            if (user.IsInRole(role))
                return true;
        return false;
    }

    public async Task<bool> HasReadPermissionAsync([NotNull] ProcessInstance processInstance,
        [NotNull] ClaimsPrincipal user)
    {
        if (processInstance == null) throw new ArgumentNullException(nameof(processInstance));
        if (user == null) throw new ArgumentNullException(nameof(user));

        if (IsAdmin(user) || IsManage(processInstance.WorkProcess, user))
            return true;


        if (processInstance.Creator == user.Identity.Name)
            return true;

        var tags = await _tagStore.GetProcessInstanceTagsAsync(processInstance.Id);

        var matchTagss = new[]
            { Tag.MakeCCUserTag(user.Identity.Name), Tag.MakeDisposeUser(user.Identity.Name) };

        return tags.Any(processInstanceTag => matchTagss.Contains(processInstanceTag.Tag.Name));
    }


    /// <summary>
    ///     把当前用户的信息设置到
    /// </summary>
    /// <param name="permissionViewModel"></param>
    /// <param name="user"></param>
    public void SetProcessInstance([NotNull] ProcessInstanceViewModel permissionViewModel,
        [NotNull] ClaimsPrincipal user)
    {
        if (permissionViewModel == null) throw new ArgumentNullException(nameof(permissionViewModel));
        if (user == null) throw new ArgumentNullException(nameof(user));
        var permission =
            _cacheManager.Get(permissionViewModel.WorkProcessName,
                _permissionStore); //  _permissionStore.GetByProcessNameAsync(workProcessName).Result;
        var cc = _processInstanceDistributionStore.GetByUser(permissionViewModel.Id, user.Identity.Name);
        var canBeDeleteByWorkActivityCount =
            permissionViewModel.WorkActivityCount < permissionViewModel.CanBeDeleteWorkActivity;
        SetProcessInstance(permissionViewModel, user, canBeDeleteByWorkActivityCount, permission, cc);
    }

    public bool IsManage(WorkProcess wp, ClaimsPrincipal user)
    {
        if (IsAdmin(user))
            return true;

        var permission =
            _cacheManager.Get(wp.Name, _permissionStore); //_permissionStore.GetByProcessNameAsync(wp.Name);
        var allRoles = user.Claims.Where(_ => _.Type == ClaimTypes.Role).Select(_ => _.Value);
        return IsManage(permission, allRoles);
    }

    /// <summary>
    ///     获取user 管理的 workPrcess
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public IEnumerable<string> GetManagerWorkProcess(ClaimsPrincipal user)
    {
        var allRoles = user.Claims.Where(claim => claim.Type == ClaimTypes.Role).Select(_ => _.Value);
        var workProcessNames = new List<string>();
        foreach (var permission in _cacheManager.All(_permissionStore))
            if (IsManage(permission, allRoles))
                workProcessNames.Add(permission.ProcessName);

        return workProcessNames;
    }

    /// <summary>
    ///     是否未 管理员。
    /// </summary>
    /// <param name="permission"></param>
    /// <param name="roleOfUser"></param>
    /// <returns></returns>
    public bool IsManage(WorkProcessPermission permission, IEnumerable<string> roleOfUser)
    {
        var permissions = permission.ManageRoles?.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (permissions == null) return false;

        foreach (var role in roleOfUser)
            if (permissions.Contains(role))
                return true;

        return false;
    }

    /// <summary>
    /// </summary>
    /// <param name="permissionViewModel"></param>
    /// <param name="user"></param>
    /// <param name="permission"></param>
    /// <exception cref="ArgumentNullException"></exception>
    private static void SetWorkProcess([NotNull] IWorkProcessWithPermission permissionViewModel, ClaimsPrincipal user,
        WorkProcessPermission permission)
    {
        if (permissionViewModel == null) throw new ArgumentNullException(nameof(permissionViewModel));
        if (user == null) throw new ArgumentNullException(nameof(user));


        permissionViewModel.IsWorkProcessCreator = user.Identity.Name == permissionViewModel.GetWorkProcessCreator();
        if (permission == null)
            return;
        permissionViewModel.IsManager = permission.IsManager(user);

        if (permissionViewModel.IsManager)
        {
            permissionViewModel.CanCreateProcessInstance = true;
            return;
        }

        foreach (var performer in permission.Performers)
            switch (performer.Type)
            {
                case PerformerType.Org:
                    if (user.HasClaim(_ => _.Type == "org" && _.Value == performer.Name))
                        permissionViewModel.CanCreateProcessInstance = true;
                    break;
                case PerformerType.Role:
                    if (user.IsInRole(performer.Name))
                        permissionViewModel.CanCreateProcessInstance = true;
                    break;
                case PerformerType.User:
                    if (performer.Name == user.Identity.Name)
                        permissionViewModel.CanCreateProcessInstance = true;

                    break;
            }
    }

    /// <summary>
    ///     把当前用户的信息设置到
    /// </summary>
    /// <param name="permissionViewModel"></param>
    /// <param name="user"></param>
    /// <param name="permission"></param>
    /// <param name="workActivityLessThanSetting">是否符合删除的workActivityCount</param>
    private void SetProcessInstance([NotNull] IProcessInstanceWithPermission permissionViewModel,
        [NotNull] ClaimsPrincipal user,
        bool workActivityLessThanSetting,
        [NotNull] WorkProcessPermission permission,
        [AllowNull] ProcessInstanceDistribution processInstanceDistribution)
    {
        SetWorkProcess(permissionViewModel, user, permission);
        permissionViewModel.IsInstanceCreator = user.Identity.Name == permissionViewModel.GetProcessInstanceCreator();
        var processInstanceStatus = permissionViewModel.GetProcessInstanceStatus();

        //是否可以删除
        // 1. 角色是管理员，可以做任何动作
        // 2. 处于执行中的工单，只能由创建者和工作流程管理员可以删除，但是已经执行过的step 要少于设定值。

        var inProcessing = processInstanceStatus == ProcessInstanceStatus.Processing ||
                           processInstanceStatus == ProcessInstanceStatus.Created ||
                           processInstanceStatus == ProcessInstanceStatus.Suspend;

        permissionViewModel.CanDeleteProcessInstance = user.IsInRole("admin")
                                                       ||
                                                       ((permissionViewModel.IsManager ||
                                                         permissionViewModel.IsInstanceCreator) &&
                                                        workActivityLessThanSetting && inProcessing);


        if (processInstanceDistribution != null)
        {
            permissionViewModel.IsCCUser = true;
            permissionViewModel.HasRead = processInstanceDistribution.HasRead;
        }
    }


    /// <summary>
    /// </summary>
    /// <param name="data"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public Task SetWorkProcessListItemPermissionAsync([NotNull] WorkProcessListItem[] data,
        [NotNull] ClaimsPrincipal user)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));

        foreach (var wp in data.ToArray())
        {
            var permission = _cacheManager.Get(wp.Name, _permissionStore);
            SetWorkProcess(wp, user, permission);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    /// <param name="result"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public Task FillProcessInstanceList([NotNull] InstanceListItemViewModel[] result, [NotNull] ClaimsPrincipal user)
    {
        if (result == null) throw new ArgumentNullException(nameof(result));
        if (user == null) throw new ArgumentNullException(nameof(user));

        foreach (var wp in result.ToArray())
        {
            var canBeDeleteByWorkActivityCount = wp.WorkActivityCount < wp.CanDeleteWorkActivityCount;
            wp.IsWorkProcessCreator = user.Identity.Name == wp.Creator;
            var permission = _cacheManager.Get(wp.WorkProcessName, _permissionStore);
            SetProcessInstance(wp, user, canBeDeleteByWorkActivityCount, permission, null);
            if (wp.WorkActivityId != 0)
                FillWorkActivityPermission(wp, permission, user);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    ///     是否由创建的权限；
    /// </summary>
    /// <param name="user"></param>
    /// <param name="workProcessName"></param>
    /// <returns></returns>
    public bool HasCreate([NotNull] ClaimsPrincipal user, [NotNull] string workProcessName)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        if (workProcessName == null) throw new ArgumentNullException(nameof(workProcessName));
        //检查

        if (user.IsInRole("admin"))
            return true;

        var permission = _cacheManager.Get(workProcessName, _permissionStore);
        var hasPermissionCreateInstance = false;
        var userName = user.Identity.Name;
        if (permission != null)
        {
            foreach (var role in permission.ManageRoles.Split(','))
                if (user.IsInRole(role))
                    return true;


            foreach (var performer in permission.Performers)
            {
                switch (performer.Type)
                {
                    case PerformerType.Org:
                        if (user.HasClaim(_ => _.Type == "org" && _.Value == performer.Name))
                            hasPermissionCreateInstance = true;
                        break;
                    case PerformerType.Role:
                        if (user.IsInRole(performer.Name)) hasPermissionCreateInstance = true;
                        break;
                    case PerformerType.User:
                        if (performer.Name == userName) hasPermissionCreateInstance = true;

                        break;
                }

                if (hasPermissionCreateInstance) break;
            }
        }

        return hasPermissionCreateInstance;
    }

    /// <summary>
    /// </summary>
    /// <param name="user"></param>
    /// <param name="pi"></param>
    /// <returns></returns>
    public bool HasStart(ClaimsPrincipal user, ProcessInstance pi)
    {
        return HasCreate(user, pi.WorkProcess.Name);
    }

    /// <summary>
    ///     检查workactivity 的 权限。
    /// </summary>
    /// <param name="permissionViewModel"></param>
    /// <param name="user"></param>
    public void SetWorkActivitiesPermission(WorkActivityListItemViewModel[] permissionViewModel, ClaimsPrincipal user)
    {
        if (user.Identity == null)
            throw new ArgumentNullException(nameof(user));
        foreach (var item in permissionViewModel)
            FillWorkActivityPermission(item, _cacheManager.Get(item.WorkProcessName, _permissionStore), user);
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivityListItemViewModel"></param>
    /// <param name="user"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void SetWorkActivityPermission(WorkActivityListItemViewModel workActivityListItemViewModel,
        ClaimsPrincipal user)

    {
        if (workActivityListItemViewModel == null)
            throw new ArgumentNullException(nameof(workActivityListItemViewModel));
        if (user == null) throw new ArgumentNullException(nameof(user));

        var permission = _cacheManager.Get(workActivityListItemViewModel.WorkProcessName, _permissionStore);
        FillWorkActivityPermission(workActivityListItemViewModel, permission, user);
    }

    /// <summary>
    /// </summary>
    /// <param name="permissionViewModel"></param>
    /// <param name="permission"></param>
    /// <param name="user"></param>
    private void FillWorkActivityPermission(IWorkActivityWithPermission permissionViewModel,
        [MaybeNull] WorkProcessPermission permission, ClaimsPrincipal user)
    {
        //当前用户是否是流程管理员。
        var isManager = permission?.IsManager(user) ?? false;
        //当前用户是否和workActivity是同一个人
        var sameUser = permissionViewModel.GetDisposeUser() == user.Identity.Name || user.IsInRole("admin") ||
                       user.IsInRole("swf-manager");


        var status = permissionViewModel.GetWorkActivityStatus();

        if (status == WorkActivityStatus.Processing)
        {
            //流程管理员无论wotkatask怎样设置，都可以放弃当前管理员。
            permissionViewModel.CanGiveUpWorkActivity =
                (permissionViewModel.GetWorkTaskGiveUpSetting() && sameUser) || isManager;
            permissionViewModel.CanDisposeWorkActivity = sameUser;
        }


        if (status == WorkActivityStatus.UnAssign)
        {
            if (isManager)
            {
                permissionViewModel.CanAcceptWorkActivity = true;
                return;
            }

            //是否能够接受这个任务，这个要看范围。
            var performers = permissionViewModel.GetPerformers();

            foreach (var performer in performers)
            {
                switch (performer.Type)
                {
                    case PerformerType.Org:
                        permissionViewModel.CanAcceptWorkActivity = user.HasClaim(_ =>
                            _.Type == UserDefined.ClaimOrg && _.Value == performer.Key);
                        break;
                    case PerformerType.User:
                        permissionViewModel.CanAcceptWorkActivity = user.Identity.Name == performer.Key;
                        break;
                    case PerformerType.Role:
                        permissionViewModel.CanAcceptWorkActivity = user.IsInRole(performer.Key);
                        break;
                }

                if (permissionViewModel.CanAcceptWorkActivity)
                    break;
            }
        }
    }

    /// <summary>
    ///     是否未管理员。
    /// </summary>
    /// <param name="workActivity"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public Task<bool> IsManagerAsync(WorkActivity workActivity, ClaimsPrincipal user)
    {
        //var permission = await _permissionStore.GetByProcessNameAsync(workActivity.ProcessInstance.WorkProcess.Name);
        var permission = _cacheManager.Get(workActivity.ProcessInstance.WorkProcess.Name, _permissionStore);
        if (permission == null) return Task.FromResult(false);
        foreach (var str in permission.ManageRoles.Split(','))
            if (user.IsInRole(str))
                return Task.FromResult(true);

        return Task.FromResult(false);
    }

    /// <summary>
    ///     是否能够访问
    /// </summary>
    /// <param name="workActivity"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public async Task<bool> HasReadPermissionAsync(WorkActivity workActivity, ClaimsPrincipal user)
    {
        if (workActivity.DisposeUser == user.Identity.Name) //当前处理人
            return true;
        return await HasReadPermissionAsync(workActivity.ProcessInstance, user);
    }

    public Task<bool> HasCancel(ProcessInstance processInstance, ClaimsPrincipal user)
    {
        return Task.FromResult(IsManage(processInstance.WorkProcess, user));
    }

    public bool CanGiveUp(WorkActivity wa, ClaimsPrincipal user)
    {
        return (user.Identity.Name == wa.DisposeUser && wa.WorkTask.CanGiveUp) ||
               IsManage(wa.ProcessInstance.WorkProcess, user);
    }

    public bool CanResolve(WorkActivity workActivity, ClaimsPrincipal user, out WorkActivityViewModel vm)
    {
        vm = workActivity.ToViewModel();
        SetWorkActivityPermission(vm, user);

        return vm.CanDisposeWorkActivity;
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivities"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public bool CanUploadFiles(IEnumerable<WorkActivity> workActivities, ClaimsPrincipal user)
    {
        if (!workActivities.Any()) return false;

        var isManager = IsManage(workActivities.First().ProcessInstance.WorkProcess, user);

        if (isManager) return true;

        foreach (var wa in workActivities)
            if (CanResolve(wa, user, out _))
                return true;

        return false;
    }
}