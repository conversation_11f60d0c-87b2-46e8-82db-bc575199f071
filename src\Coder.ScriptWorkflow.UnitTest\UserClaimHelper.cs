﻿using System.Collections.Generic;
using System.Security.Claims;

namespace Coder.ScriptWorkflow.UnitTest;

internal static class UserClaimHelper
{
    public static ClaimsPrincipal ToClaimsPrincipal(this string user)
    {
        var cliams = new List<Claim>();

        cliams.Add(new Claim(ClaimTypes.Name, user));
        cliams.Add(new Claim(ClaimTypes.NameIdentifier, user));
        cliams.Add(new Claim("real_name", "姓名"));
        return new ClaimsPrincipal(new ClaimsIdentity(cliams));
    }
}