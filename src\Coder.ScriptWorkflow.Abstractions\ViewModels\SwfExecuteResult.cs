﻿namespace Coder.ScriptWorkflow.ViewModels;


public class ScriptError
{
    /// <summary>
    ///     所有代码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     行数
    /// </summary>
    public ScriptErrorLocation Coordinates { get; set; }

    /// <summary>
    ///     错误原因
    /// </summary>
    public string Reason { get; set; }

    /// <summary>
    /// </summary>
    public string PluginErrorStack { get; set; }
    /// <summary>
    /// </summary>
    public string NodeName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string EventName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string PluginName { get; set; }

}

/// <summary>
/// 工作流执行结果。
///Resolve/Cancel/Start/Create 的执行结果都采用这个基础类
/// </summary>
public class SwfExecuteResult
{
    /// <summary>
    /// </summary>
    public ScriptError ErrorScript { get; set; }

    public bool Success { get; set; }

    public string Message { get; set; }

    public string ModelPath { get; set; }
}


public class SaveFormResult
{
    public string Message { get; set; }
    public bool Success { get; set; }
}