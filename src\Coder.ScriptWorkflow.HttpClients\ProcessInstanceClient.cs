using Coder.ScriptWorkflow.Clients;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Coder.WebHttpClient;
using Newtonsoft.Json;

namespace Coder.ScriptWorkflow.HttpClients;

public class ProcessInstanceClient : HttpStore, IProcessInstanceClient
{
    private readonly HttpClient _client;

    public ProcessInstanceClient(HttpClient httpClient) : base(httpClient)
    {
        _client = httpClient;
    }

    public ProcessInstanceClient(string url, IHttpClientFactory httpClientFactory)
        : base(url, httpClientFactory)
    {
    }

    public async Task<SwfResult<int>> CountByNumbers(string[] numbers)
    {
        if (numbers == null) throw new ArgumentNullException(nameof(numbers));
        var queryQu = new List<string>();

        foreach (var number in numbers) queryQu.Add("numbers=" + number);

        var query = string.Join("&", queryQu);

        var responseMessage = await _client.GetAsync($"ProcessInstance/count-process-instance-numbers/?{query}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<int>>(str);

            return result;
        }

        return new SwfResult<int> { Code = 0, Data = 0, Message = responseMessage.ReasonPhrase };
    }

    public async Task<SwfResult<ProcessInstanceViewModel>> GetProcessInstanceViewModelById(int id)
    {
        var responseMessage = await _client.GetAsync($"ProcessInstance/{id}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<ProcessInstanceViewModel>>(str);

            return result;
        }

        return new SwfResult<ProcessInstanceViewModel>
        { Code = 0, Data = null, Message = responseMessage.ReasonPhrase };
    }

    public async Task<SwfResult<ProcessInstanceViewModel>> GetProcessInstanceViewModelByNumber(string number)
    {
        var responseMessage = await _client.GetAsync($"ProcessInstance/get-by-code/{number}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<ProcessInstanceViewModel>>(str);

            return result;
        }
        return new SwfResult<ProcessInstanceViewModel>
        { Code = 0, Data = null, Message = responseMessage.ReasonPhrase };
    }



    public async Task<SwfResult<IEnumerable<ProcessInstanceViewModel>>> GetProcessInstanceViewModelList(string[] numbers)
    {
        if (numbers == null) throw new ArgumentNullException(nameof(numbers));
        var queryQu = new List<string>();

        foreach (var number in numbers) queryQu.Add("numbers=" + number);

        var query = string.Join("&", queryQu);

        var responseMessage = await _client.GetAsync($"ProcessInstance/list-process-instance-numbers/?{query}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<IEnumerable<ProcessInstanceViewModel>>>(str);

            return result;
        }

        return new SwfResult<IEnumerable<ProcessInstanceViewModel>>
        { Code = 0, Data = null, Message = responseMessage.ReasonPhrase };
        //throw new Exception(responseMessage.RequestMessage);
    }


    public async Task<SwfResult<IEnumerable<ProcessInstanceViewModel>>> GetProcessInstanceViewModelListByIds(int[] ids)
    {
        if (ids == null) throw new ArgumentNullException(nameof(ids));
        var queryQu = new List<string>();

        foreach (var id in ids) queryQu.Add("ids=" + id);

        var query = string.Join("&", queryQu);

        var responseMessage = await _client.GetAsync($"ProcessInstance/list-process-instance-ids/?{query}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<IEnumerable<ProcessInstanceViewModel>>>(str);

            return result;
        }
        return new SwfResult<IEnumerable<ProcessInstanceViewModel>>
        { Code = 0, Data = null, Message = responseMessage.ReasonPhrase };
    }

    public async Task<SwfResult<IEnumerable<ProcessInstanceFileViewModel>>> GetFilesByProcessInstanceId(int processInstanceId)
    {
        var responseMessage = await _client.GetAsync($"ProcessInstance/get-files-by-processInstanceId/{processInstanceId}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<IEnumerable<ProcessInstanceFileViewModel>>>(str);

            return result;
        }

        return new SwfResult<IEnumerable<ProcessInstanceFileViewModel>>
        { Code = 0, Data = null, Message = responseMessage.ReasonPhrase };
    }

    public async Task<SwfResult<object>> DeleteFile(string fileId, int processInstanceId)
    {
        var responseMessage = await _client.DeleteAsync($"ProcessInstance/deleteFile/{fileId}/{processInstanceId}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<object>>(str);

            return result;
        }

        return new SwfResult<object>
        { Code = 0, Data = new { message = "File deleted successfully", sucess = true }, Message = responseMessage.ReasonPhrase };
    }

    public async Task<SwfResult<FileAttachSubmit>> Upload(string fileId, int processInstanceId)
    {
        var responseMessage = await _client.GetAsync($"ProcessInstance/upload/{fileId}/{processInstanceId}");

        if (responseMessage.IsSuccessStatusCode)
        {
            var str = await responseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<SwfResult<FileAttachSubmit>>(str);

            return result;
        }
        return new SwfResult<FileAttachSubmit>
        { Code = 0, Data = new FileAttachSubmit(), Message = responseMessage.ReasonPhrase };
    }
}
