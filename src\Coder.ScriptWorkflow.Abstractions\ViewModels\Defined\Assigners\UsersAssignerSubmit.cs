﻿using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

/// <summary>
/// </summary>
public class UsersAssignerSubmit : AssignSubmit
{
    /// <summary>
    ///     默认构造函数，代码中请勿使用。只用于反序列化
    /// </summary>
    public UsersAssignerSubmit()
    {
    }


    /// <summary>
    /// </summary>
    public IList<PerformerSubmit> Performers { get; set; } = new List<PerformerSubmit>();


    /// <summary>
    /// </summary>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public override bool Validate(out string errorMessage)
    {
        errorMessage = null;

        if (!Performers.Any())
        {
            errorMessage = "执行者不能为空。";
            return false;
        }

        return true;
    }
}