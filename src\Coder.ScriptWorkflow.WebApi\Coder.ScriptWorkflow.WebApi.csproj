﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Authors>珠海酷迪技术有限公司</Authors>
		<Company>珠海酷迪技术有限公司</Company>
		<Copyright>珠海酷迪技术有限公司@2018-2025</Copyright>
		<Title>脚本工作流服务</Title>
		<Version>3.1.1</Version>
		<IncludeBuildOutput>false</IncludeBuildOutput>


		<GeneratePackageOnBuild>false</GeneratePackageOnBuild>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="coder.car.service.Abstractions" Version="2.0.2" />
		<PackageReference Include="Coder.DataInitial" Version="1.1.6" />
		<PackageReference Include="Coder.Orgs.Clients.Http" Version="6.1.1" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.7" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Coder.Authentication" Version="9.1.5" />
		<PackageReference Include="Coder.ConsulHelper" Version="2.6.0" />
	

	
		<PackageReference Include="Coder.HealthChecks" Version="1.0.0" />
		<PackageReference Include="Coder.PreventDuplicateSubmit" Version="1.1.2" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<TreatAsUsed>true</TreatAsUsed>
		</PackageReference>

	
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />

		<PackageReference Include="NLog.Targets.Loki" Version="2.2.0">
		  <TreatAsUsed>true</TreatAsUsed>
		</PackageReference>
	
		<PackageReference Include="NLog.Web.AspNetCore" Version="6.0.2" />

	</ItemGroup>

	<ItemGroup>
		<Folder Include="logs\" />
		<Folder Include="upload\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Coder.ScriptWorkflow.Abstractions\Coder.ScriptWorkflow.Abstractions.csproj" />
		<ProjectReference Include="..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
		<ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.DM\Coder.ScriptWorkflow.Migrations.DM.csproj" />
		<ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.Mssql\Coder.ScriptWorkflow.Migrations.Mssql.csproj">
			<TreatAsUsed>true</TreatAsUsed>
		</ProjectReference>
		<ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.Mysql\Coder.ScriptWorkflow.Migrations.Mysql.csproj">
			<TreatAsUsed>true</TreatAsUsed>
		</ProjectReference>
		<ProjectReference Include="..\Coder.ScriptWorkflow.Migrations.Sqlite\Coder.ScriptWorkflow.Migrations.Sqlite.csproj" />
		<ProjectReference Include="..\Coder.ScriptWorkflow.Performers.CoderMembers\Coder.ScriptWorkflow.Performers.CoderMembers.csproj" />
		<ProjectReference Include="..\Coder.ScriptWorkflow.Scripts.Plugin.JsRepaidCarClient\Coder.ScriptWorkflow.Scripts.Plugin.JsRepaidCarClient.csproj" />
		<ProjectReference Include="..\Coder.ScriptWorkflow.Stores.Ef\Coder.ScriptWorkflow.Stores.Ef.csproj" />
		<ProjectReference Include="..\Coder.VBenMenu.Interceptor\Coder.VBenMenu.Interceptor.csproj" />
		<ProjectReference Include="..\Coder.VBenNotify.Interceptor\Coder.VBenNotify.Interceptor.csproj" />
		<ProjectReference Include="..\Plugins\Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient\Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient.csproj" />
		<ProjectReference Include="..\Plugins\Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient\Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient.csproj" />
		<ProjectReference Include="..\Plugins\Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient\Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient.csproj" />
		<ProjectReference Include="..\Plugins\ScriptWorkflow.Scripts.Plugin.JsHttpClient\Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient.csproj" />
		<ProjectReference Include="..\Plugins\ScriptWorkflow.Scripts.Plugin.Member\Coder.ScriptWorkflow.Scripts.Plugin.Member.csproj" />
		<ProjectReference Include="..\Plugins\ScriptWorkflow.Scripts.Plugin.MySql\Coder.ScriptWorkflow.Scripts.Plugin.MySql.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.Staging.json">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Properties\launchSettings.json">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ProjectExtensions>
		<VisualStudio><UserProperties appsettings_1json__JsonSchema="" properties_4launchsettings_1json__JsonSchema="" /></VisualStudio>
	</ProjectExtensions>


</Project>
