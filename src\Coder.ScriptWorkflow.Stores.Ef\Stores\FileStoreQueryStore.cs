﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.ViewModels;
using LinqKit;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// 文件查询存储。
/// </summary>
internal class FileStoreQueryStore : IFileStoreQuery
{
    private readonly ApplicationDbContext _dbContext;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public FileStoreQueryStore(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    /// <summary>
    /// </summary>
    /// <param name="fileSearcher"></param>
    /// <returns></returns>
    public async Task<IEnumerable<ProcessInstance.FileAttach>> ListAsync(FileSearcher fileSearcher)
    {
        var query = MakeQuery(fileSearcher);
        return await query
            .Skip((fileSearcher.Page - 1) * fileSearcher.PageSize)
            .Take(fileSearcher.PageSize)
            .ToListAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="fileSearcher"></param>
    /// <returns></returns>
    public async Task<int> CountAsync(FileSearcher fileSearcher)
    {
        var query = MakeQuery(fileSearcher);
        return await query.CountAsync();
    }

    public Task<ProcessInstance.FileAttach> GetByIdAsync(string fsSysFileId)
    {
        return _dbContext.Set<ProcessInstance.FileAttach>().FirstOrDefaultAsync(file => file.FileId == fsSysFileId);
    }

    private IQueryable<ProcessInstance.FileAttach> MakeQuery(FileSearcher fileSearcher)
    {
        var predicate = PredicateBuilder.New<ProcessInstance.FileAttach>(true);
        if (!string.IsNullOrEmpty(fileSearcher.Name))
            predicate.And(_ => _.FileName.Contains(fileSearcher.Name));

        if (!string.IsNullOrEmpty(fileSearcher.ProcessInstanceNumber)) predicate = predicate.And(_ => _.ProcessInstance.Number == fileSearcher.ProcessInstanceNumber);

        var result = _dbContext.Set<ProcessInstance.FileAttach>()
            .Include(_ => _.ProcessInstance).ThenInclude(_ => _.WorkProcess)
            .Where(predicate);
        return result;
    }
}