﻿using System;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class WorkflowException : Exception
{
    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    public WorkflowException(string message) : base(message)
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="innerException"></param>
    public WorkflowException(string message, Exception innerException) : base(message, innerException)
    {
    }
}