﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ProcessInstanceTagMapping : IEntityTypeConfiguration<ProcessInstanceTag>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public ProcessInstanceTagMapping(string prefix)
    {
        _prefix = prefix;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ProcessInstanceTag> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.Property(_ => _.Color).HasMaxLength(20);
        builder.Property(_ => _.CanDelete);
        builder.HasOne(_ => _.ProcessInstance).WithMany().HasForeignKey("ProcessInstanceId");
        builder.HasOne(_ => _.Tag).WithMany().HasForeignKey("TagId");
        builder.ToTable($"{_prefix}_PI_ProcessInstanceTags").HasComment("工作流程标记");
    }
}