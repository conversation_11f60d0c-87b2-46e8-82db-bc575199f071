﻿using System;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

#if DEBUG
/// <summary>
///     Debug才有
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
public class JsHttpClientController : Controller
{
    /// <summary>
    /// </summary>
    /// <param name="form"></param>
    /// <returns></returns>
    [HttpPost("PostForm")]
    public IActionResult PostForm([FromForm] Form form)
    {
        return Ok(JsonConvert.SerializeObject(form));
    }

    /// <summary>
    /// </summary>
    /// <param name="form"></param>
    /// <returns></returns>
    [HttpPost("PostJSON")]
    public IActionResult PostJSON([FromBody] JObject form)
    {
        return Ok(form);
    }

    /// <summary>
    /// </summary>
    /// <param name="form"></param>
    /// <returns></returns>
    [HttpPut("PutJSON")]
    public IActionResult PutJson([FromBody] JObject form)
    {
        return Ok(form);
    }

    /// <summary>
    /// </summary>
    /// <param name="form"></param>
    /// <returns></returns>
    [HttpGet("query")]
    public IActionResult Query([FromQuery] Form form)
    {
        return Ok(JsonConvert.SerializeObject(form));
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public IActionResult JsonDelete([FromRoute] int id)
    {
        return Ok(new { success = true, message = "message" });
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    [HttpGet("list-json")]
    public IActionResult ListJson()
    {
        var a = Request.Query["a"];
        var b = Request.Query["b"];
        var c = Request.Query["c"];
        var d = Request.Query["d"];
        return Ok(new { a, b, c, dateTime = DateTime.Now, d });
    }
}

#endif