﻿using System.Collections.Generic;
using System.Net.Http;
using Coder.ScriptWorkflow;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using Tiny.RestClient;

namespace ScriptWorkflow.Scripts.Plugin.HttpClient;

/// <summary>
/// </summary>
public class JsHttpPlugin : IPlugin
{
    private readonly IHttpClientFactory _httpClientFactory;


    public JsHttpPlugin(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }

    public string Name { get; set; } = "jsHttp";

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return this;
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    public string GetJsDefined()
    {
        #region js Defined

        return @"declare var jsHttp: jsHttpClass

interface jsHttpClass {
    /**
     * 通过Get
     * @param url url
     */
    Get(url: string): ResponseMessage
    /**
     * 
     * @param url 访问的url
     * @param jObject 提交的json
     */
    PostJson(url: string, jObject: Object): ResponseMessage
    PostForm(url: string, jObject: Object): ResponseMessage
    PutForm(url: string, jObject: Object): ResponseMessage
}
/**
 * 返回值
 */
declare class ResponseMessage {
    /**
     * status返回值，如200，404等
     */
    StatusCode: Number
    /**
     * 返回值得格式，body为字符串格式
     */
    Body: String
}";

        #endregion
    }

    /// <summary>
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    public JsResponse Get(string url)
    {
        using var client = _httpClientFactory.CreateClient();
        var r = client.GetAsync(url).Result;
        var result = new JsResponse { StatusCode = (int)r.StatusCode, Body = r.Content.ReadAsStringAsync().Result };

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="url"></param>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    public JsResponse PostForm(string url, JSValue jsValue)
    {
        using var client = _httpClientFactory.CreateClient();
        var dict = new Dictionary<string, string>();
        foreach (var jObj in jsValue)
        {
            if (jObj.Value.Value == null) continue;
            var value = "";
            if (jObj.Value.Value is Date date)
                value = date.ToDateTime().ToString("O");
            else
                value = jObj.Value.Value.ToString();
            dict.Add(jObj.Key, value);
        }

        var httpResponseMessage = client.PostAsync(url, new FormUrlEncodedContent(dict)).Result;
        var result = new JsResponse
        {
            StatusCode = (int)httpResponseMessage.StatusCode,
            Body = httpResponseMessage.Content.ReadAsStringAsync().Result
        };

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="url"></param>
    /// <param name="jsvalue"></param>
    /// <returns></returns>
    public JsResponse PostJson(string url, JSValue jsvalue)
    {
        using var client = _httpClientFactory.CreateClient();
        var resetClient = new TinyRestClient(client, client.BaseAddress.ToString());

        var httpResponseMessage = resetClient.PostRequest(url, jsvalue.ToDynamicObject())
            .ExecuteAsHttpResponseMessageAsync().Result;
        var result = new JsResponse
        {
            StatusCode = (int)httpResponseMessage.StatusCode,
            Body = httpResponseMessage.Content.ReadAsStringAsync().Result
        };

        return result;
    }

    public JsResponse PutJson(string url, JSValue jsvalue)
    {
        using var client = _httpClientFactory.CreateClient();
        var resetClient = new TinyRestClient(client, client.BaseAddress.ToString());

        var httpResponseMessage = resetClient.PutRequest(url, jsvalue.ToDynamicObject())
            .ExecuteAsHttpResponseMessageAsync().Result;
        var result = new JsResponse
        {
            StatusCode = (int)httpResponseMessage.StatusCode,
            Body = httpResponseMessage.Content.ReadAsStringAsync().Result
        };

        return result;
    }
}