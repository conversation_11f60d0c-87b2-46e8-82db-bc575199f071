﻿using System;

namespace Coder.ScriptWorkflow.Scripts.Plugins;

/// <summary>
///     要求插件排异常都要用这个一场包含。
/// </summary>
public class PluginException : WorkflowException
{
    /// <summary>
    /// </summary>
    /// <param name="pluginName"></param>
    /// <param name="message"></param>
    public PluginException(string pluginName, string message) : base(message)
    {
        PluginName = pluginName;
    }

    /// <summary>
    /// </summary>
    /// <param name="pluginName"></param>
    /// <param name="message"></param>
    /// <param name="innerException"></param>
    public PluginException(string pluginName
        , string message, Exception innerException) : base(message, innerException)
    {
        PluginName = pluginName;
    }

    /// <summary>
    /// </summary>
    public string PluginName { get; }
}