﻿using System.Diagnostics.CodeAnalysis;
using Coder.ScriptWorkflow.ViewModels;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class JsRuntimeException : WorkflowException
{
    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="jsException"></param>
    internal JsRuntimeException([AllowNull] IWorkflowContext context, JSException jsException) : base(
        jsException.Message, jsException)
    {
        Context = context;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="message"></param>
    public JsRuntimeException([AllowNull] IWorkflowContext context, string message) : base(message)
    {
        Context = context;
    }

    /// <inheritdoc />
    internal JsRuntimeException(string message, JSException jsException) : base(message, jsException)
    {
    }

    /// <summary>
    ///     工作流上下文
    /// </summary>
    public IWorkflowContext Context { get; }

    /// <summary>
    ///     是否为插件错误。
    /// </summary>
    internal bool IsPluginError => PluginErrorStack == null;

    /// <summary>
    ///     代码
    /// </summary>
    public string Code { get; internal set; }

    /// <summary>
    /// 出错行数
    /// </summary>
    public ScriptErrorLocation Coordinates { get; set; } = new ScriptErrorLocation();

    /// <summary>
    ///     workTask/decision/start/end 节点。
    /// </summary>
    public string NodeName { get; internal set; }

    /// <summary>
    ///     时间名称
    /// </summary>
    public string EventName { get; internal set; }

    /// <summary>
    ///     插件名称
    /// </summary>
    public string PluginName { get; set; }

    /// <summary>
    ///     插件内部Stack
    /// </summary>
    public string PluginErrorStack { get; internal set; }

    /// <summary>
    ///     插件错误小心。
    /// </summary>
    public string PluginErrorMessage { get; internal set; }
}