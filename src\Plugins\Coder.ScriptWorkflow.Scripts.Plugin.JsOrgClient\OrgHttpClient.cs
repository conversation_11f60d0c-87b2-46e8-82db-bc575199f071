﻿using Coder.Orgs.Clients;
using Coder.Orgs.ViewModels.Orgs;
using NiL.JS.Core;
using Array = NiL.JS.BaseLibrary.Array;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient;

public class OrgHttpClient
{
    private readonly IOrgClient _client;

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    public OrgHttpClient(IOrgClient client)
    {
        _client = client;
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public JSObject GetById(int id)
    {
        var r = _client.GetAsync(id).Result.Data;
        return CreateJsObject(r);
    }

    /// <summary>
    /// </summary>
    /// <param name="parentId"></param>
    /// <returns></returns>
    public Array GetOrgByParent(int parentId)
    {
        var items = _client.GetOrgByParentAsync(parentId).Result.Data;
        var array = new Array();
        foreach (var item in items) array.Add(CreateJsObject(item));

        return array;
    }

    public JSObject GetParentByPath(string path)
    {
        var items = _client.GetOrgsByParentPathAsync(path).Result.Data;
        var array = new Array();
        foreach (var item in items) array.Add(CreateJsObject(item));

        return array;
    }

    /// <summary>
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    public JSObject GetByPath(string path)
    {
        var result = _client.GetByPath(path).Result;
        if (result == null) return null;
        var r = result.Data;
        return CreateJsObject(r);
    }

    private JSObject CreateJsObject(OrgViewModel v)
    {
        if (v == null) return null;

        var result = JSObject.CreateObject();
        result[nameof(v.Id)] = v.Id;
        result[nameof(v.Name)] = v.Name;
        result[nameof(v.OrgAll)] = v.OrgAll;
        result[nameof(v.OrgIdPath)] = v.OrgIdPath;
        result[nameof(v.Path)] = v.Path;
        result[nameof(v.OrgType)] = (int)v.OrgType;
        return result;
    }
}