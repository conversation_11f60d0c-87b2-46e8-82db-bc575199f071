﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.Performers;

/// <summary>
/// </summary>
public class DemoPerformerQueryStore : IPerformerQueryStore
{
    /// <summary>
    /// </summary>
    /// <param name="roleNames"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByRolesAsync(string[] roleNames)
    {
        var t = new List<AssignUser>();
        foreach (var roleName in roleNames)
            for (var i = 0; i < 2; i++)
                t.Add(new AssignUser
                {
                    Name = roleName + "名字" + i,
                    UserName = roleName + i
                });

        return Task.FromResult((IEnumerable<AssignUser>)t);
    }

    /// <summary>
    /// </summary>
    /// <param name="orgNames"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByOrgAsync(string[] orgNames)
    {
        var t = new List<AssignUser>();
        foreach (var orgName in orgNames)
            for (var i = 0; i < 2; i++)
                t.Add(new AssignUser
                {
                    Name = orgName + "名字" + i,
                    UserName = orgName + i
                });

        return Task.FromResult((IEnumerable<AssignUser>)t);
    }

    /// <summary>
    /// </summary>
    /// <param name="users"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByUserNamesAsync(IEnumerable<string> users)
    {
        var t = new List<AssignUser>();
        foreach (var userName in users)
            t.Add(new AssignUser
            {
                Name = userName + "的名字",
                UserName = userName
            });
        return Task.FromResult((IEnumerable<AssignUser>)t);
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    public Task<IEnumerable<Performer>> SearchAsync(PerformerSearcher searcher)
    {
        var result = new List<Performer>();
        var start = (searcher.Page - 1) * searcher.PageSize;
        var max = searcher.PageSize + start;
        for (var i = start; i < max; i++)
            result.Add(new Performer
            {
                Key = searcher.Type + "-" + (i + 1),
                Name = searcher.Type.ToString()
            });

        return Task.FromResult((IEnumerable<Performer>)result);
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    public Task<int> CountAsync(PerformerSearcher searcher)
    {
        return Task.FromResult(300);
    }
}