﻿using Coder.ScriptWorkflow.Logger;

namespace Coder.ScriptWorkflow.Scripts.Plugins.Logger;

/// <summary>
///     logger 插件，默认必须采用。
/// </summary>
public class LoggerPlugin : IPlugin
{
    /// <summary>
    /// </summary>
    public const string PluginName = "logger";

    /// <summary>
    /// </summary>
    private readonly WorkflowLogManager _logger;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public LoggerPlugin(WorkflowLogManager logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// </summary>
    public string Name { get; set; } = PluginName;

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new Logger(context, _logger);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        return @"/**
 * 日志独享，把运行日志放入系统中。
 */
declare var logger: loggerClass
/**
 * 日志类
 */
declare class loggerClass {
  Error(content: string)
  Info(content: string)
}
";
    }
}