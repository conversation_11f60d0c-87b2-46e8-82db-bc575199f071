﻿CREATE TABLE IF NOT EXISTS `swf_efmigrationshistory` (
    `MigrationId` varchar(150) CHARACTER SET utf8mb4 NOT NULL,
    `ProductVersion` varchar(32) CHARACTER SET utf8mb4 NOT NULL,
    CONSTRAINT `PK_swf_efmigrationshistory` PRIMARY KEY (`MigrationId`)
) CHARACTER SET=utf8mb4;

START TRANSACTION;

ALTER DATABASE CHARACTER SET utf8mb4;

CREATE TABLE `swf_assigner` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `AssignScopeType` int NOT NULL,
    `Discriminator` varchar(21) CHARACTER SET utf8mb4 NOT NULL,
    `Script` varchar(3000) CHARACTER SET utf8mb4 NULL,
    `Performers` varchar(1000) CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_assigner` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_global_variable` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Name` varchar(100) CHARACTER SET utf8mb4 NULL,
    `Variable` varchar(300) CHARACTER SET utf8mb4 NULL,
    `Env` int NOT NULL,
    CONSTRAINT `PK_swf_global_variable` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_globalScript` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Name` varchar(100) CHARACTER SET utf8mb4 NULL,
    `Script` text CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_globalScript` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_PI_Tags` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Name` longtext CHARACTER SET utf8mb4 NULL,
    `Number` int NOT NULL,
    CONSTRAINT `PK_swf_PI_Tags` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4 COMMENT='工作流程标记';

CREATE TABLE `swf_scripts` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Script` varchar(6000) CHARACTER SET utf8mb4 NULL,
    `Discriminator` varchar(34) CHARACTER SET utf8mb4 NOT NULL,
    CONSTRAINT `PK_swf_scripts` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_swf_setting` (
    `Id` int NOT NULL COMMENT 'id' AUTO_INCREMENT,
    `Script` text CHARACTER SET utf8mb4 NULL COMMENT '脚本',
    `Discriminator` varchar(21) CHARACTER SET utf8mb4 NOT NULL,
    `Plugins` text CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_swf_setting` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4 COMMENT='工作流实例';

CREATE TABLE `swf_workflowLog` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `LogLevel` int NOT NULL,
    `ProcessInstanceId` int NOT NULL COMMENT '工单id',
    `ProcessInstanceNumber` varchar(20) CHARACTER SET utf8mb4 NULL COMMENT '工单号',
    `Version` int NOT NULL COMMENT '工作流定义版本',
    `WorkProcessName` varchar(64) CHARACTER SET utf8mb4 NULL COMMENT '工作流名称',
    `NodeName` varchar(64) CHARACTER SET utf8mb4 NULL COMMENT '节点名称',
    `WorkActivityId` int NULL,
    `Type` varchar(64) CHARACTER SET utf8mb4 NOT NULL,
    `Content` text CHARACTER SET utf8mb4 NULL,
    `PluginName` varchar(30) CHARACTER SET utf8mb4 NULL,
    `CreateTime` datetime(6) NOT NULL,
    CONSTRAINT `PK_swf_workflowLog` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_workProcessPermission` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `ProcessName` varchar(100) CHARACTER SET utf8mb4 NULL,
    `ManageRole` varchar(400) CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_workProcessPermission` PRIMARY KEY (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_workProcess` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Icon` varchar(20) CHARACTER SET utf8mb4 NULL COMMENT '图标',
    `Comment` varchar(400) CHARACTER SET utf8mb4 NULL COMMENT '备注',
    `Abbr` varchar(100) CHARACTER SET utf8mb4 NULL COMMENT '简称',
    `LogLevel` int NOT NULL,
    `FormDesign` text CHARACTER SET utf8mb4 NULL,
    `FormManageDesign` text CHARACTER SET utf8mb4 NULL,
    `Name` varchar(100) CHARACTER SET utf8mb4 NULL,
    `UpdateTimeOffset` datetime(6) NULL,
    `Version` int NOT NULL,
    `OnCompleteId` int NULL,
    `OnCancelId` int NULL,
    `OnStartId` int NULL,
    `Enable` tinyint(1) NOT NULL,
    `Prefix` varchar(10) CHARACTER SET utf8mb4 NULL,
    `Configurations` text CHARACTER SET utf8mb4 NULL,
    `GlobalScript` text CHARACTER SET utf8mb4 NULL,
    `FormTypeScriptDefined` text CHARACTER SET utf8mb4 NULL,
    `Plugins` varchar(500) CHARACTER SET utf8mb4 NULL,
    `Creator` varchar(50) CHARACTER SET utf8mb4 NULL,
    `CanBeDeleteWorkActivityCount` int NOT NULL,
    `Group` varchar(50) CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_workProcess` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_workProcess_swf_scripts_OnCancelId` FOREIGN KEY (`OnCancelId`) REFERENCES `swf_scripts` (`Id`),
    CONSTRAINT `FK_swf_workProcess_swf_scripts_OnCompleteId` FOREIGN KEY (`OnCompleteId`) REFERENCES `swf_scripts` (`Id`),
    CONSTRAINT `FK_swf_workProcess_swf_scripts_OnStartId` FOREIGN KEY (`OnStartId`) REFERENCES `swf_scripts` (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_PermissionPerformer` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Type` int NOT NULL,
    `Name` longtext CHARACTER SET utf8mb4 NULL,
    `WorkProcessPermissionId` int NULL,
    CONSTRAINT `PK_swf_PermissionPerformer` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProces~` FOREIGN KEY (`WorkProcessPermissionId`) REFERENCES `swf_workProcessPermission` (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_node` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Name` varchar(32) CHARACTER SET utf8mb4 NULL,
    `Auto` tinyint(1) NOT NULL,
    `NextNodeId` int NULL,
    `WorkProcessId` int NULL,
    `Position` longtext CHARACTER SET utf8mb4 NULL,
    `Discriminator` varchar(21) CHARACTER SET utf8mb4 NOT NULL,
    `MatchDescription` varchar(20) CHARACTER SET utf8mb4 NULL,
    `Script` text CHARACTER SET utf8mb4 NULL COMMENT '执行脚本',
    `ElseNodeId` int NULL,
    `ElseDescription` varchar(20) CHARACTER SET utf8mb4 NULL,
    `ConditionDecision_Script` text CHARACTER SET utf8mb4 NULL COMMENT '执行脚本',
    `NextTaskPerformers` int NULL,
    `AssignerId` int NULL,
    `CanGiveUp` tinyint(1) NULL,
    `FormDesign` text CHARACTER SET utf8mb4 NULL,
    `WorkActivityCompleteScriptId` int NULL,
    `WorkTaskCompleteScriptId` int NULL,
    `WorkTaskStartScriptId` int NULL,
    `SuggestionComment` varchar(50) CHARACTER SET utf8mb4 NULL,
    `ExtendInfo` text CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_node` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_node_swf_assigner_AssignerId` FOREIGN KEY (`AssignerId`) REFERENCES `swf_assigner` (`Id`),
    CONSTRAINT `FK_swf_node_swf_node_ElseNodeId` FOREIGN KEY (`ElseNodeId`) REFERENCES `swf_node` (`Id`),
    CONSTRAINT `FK_swf_node_swf_node_NextNodeId` FOREIGN KEY (`NextNodeId`) REFERENCES `swf_node` (`Id`),
    CONSTRAINT `FK_swf_node_swf_scripts_WorkActivityCompleteScriptId` FOREIGN KEY (`WorkActivityCompleteScriptId`) REFERENCES `swf_scripts` (`Id`),
    CONSTRAINT `FK_swf_node_swf_scripts_WorkTaskCompleteScriptId` FOREIGN KEY (`WorkTaskCompleteScriptId`) REFERENCES `swf_scripts` (`Id`),
    CONSTRAINT `FK_swf_node_swf_scripts_WorkTaskStartScriptId` FOREIGN KEY (`WorkTaskStartScriptId`) REFERENCES `swf_scripts` (`Id`),
    CONSTRAINT `FK_swf_node_swf_workProcess_WorkProcessId` FOREIGN KEY (`WorkProcessId`) REFERENCES `swf_workProcess` (`Id`) ON DELETE SET NULL
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_processInstance` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `IsDebug` tinyint(1) NOT NULL COMMENT '是否处于debug模式',
    `IsDelete` tinyint(1) NOT NULL COMMENT '是否已经删除',
    `WorkActivityCount` int NOT NULL,
    `WorkProcessId` int NULL,
    `Priority` int NOT NULL DEFAULT 100,
    `Subject` varchar(500) CHARACTER SET utf8mb4 NULL,
    `Creator` varchar(32) CHARACTER SET utf8mb4 NULL,
    `Status` int NOT NULL,
    `FinishTime` datetime(6) NULL,
    `CreateTime` datetime(6) NOT NULL,
    `StartTime` datetime(6) NULL,
    `Number` varchar(20) CHARACTER SET utf8mb4 NOT NULL,
    `SuspendTime` datetime(6) NULL,
    `SuspendComment` varchar(128) CHARACTER SET utf8mb4 NULL,
    `Comment` varchar(256) CHARACTER SET utf8mb4 NULL,
    `Form` json NULL,
    `CreatorName` varchar(50) CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_processInstance` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_processInstance_swf_workProcess_WorkProcessId` FOREIGN KEY (`WorkProcessId`) REFERENCES `swf_workProcess` (`Id`)
) CHARACTER SET=utf8mb4 COMMENT='工作流实例';

CREATE TABLE `swf_condition_decision` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `NodeId` int NULL,
    `MatchValue` varchar(100) CHARACTER SET utf8mb4 NULL COMMENT '匹配的字符串',
    `Description` varchar(300) CHARACTER SET utf8mb4 NULL,
    `ConditionDecisionId` int NULL,
    CONSTRAINT `PK_swf_condition_decision` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_condition_decision_swf_node_ConditionDecisionId` FOREIGN KEY (`ConditionDecisionId`) REFERENCES `swf_node` (`Id`),
    CONSTRAINT `FK_swf_condition_decision_swf_node_NodeId` FOREIGN KEY (`NodeId`) REFERENCES `swf_node` (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_WorkTaskCommand` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Name` varchar(100) CHARACTER SET utf8mb4 NULL,
    `Order` int NOT NULL,
    `Discriminator` varchar(34) CHARACTER SET utf8mb4 NOT NULL,
    `WorkTaskId` int NULL,
    `Script` text CHARACTER SET utf8mb4 NULL,
    CONSTRAINT `PK_swf_WorkTaskCommand` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_WorkTaskCommand_swf_node_WorkTaskId` FOREIGN KEY (`WorkTaskId`) REFERENCES `swf_node` (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_files` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `FileName` varchar(100) CHARACTER SET utf8mb4 NULL,
    `FileId` varchar(50) CHARACTER SET utf8mb4 NULL,
    `FileType` int NOT NULL,
    `CreateUser` varchar(100) CHARACTER SET utf8mb4 NULL,
    `ProcessInstanceId` int NULL,
    CONSTRAINT `PK_swf_files` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_files_swf_processInstance_ProcessInstanceId` FOREIGN KEY (`ProcessInstanceId`) REFERENCES `swf_processInstance` (`Id`)
) CHARACTER SET=utf8mb4;

CREATE TABLE `swf_PI_Distribution` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `ReadTime` datetime(6) NULL,
    `CreateTime` datetime(6) NOT NULL,
    `UserName` varchar(20) CHARACTER SET utf8mb4 NULL,
    `UserRealName` varchar(20) CHARACTER SET utf8mb4 NULL,
    `ProcessInstanceId` int NULL,
    `HasRead` tinyint(1) NOT NULL,
    CONSTRAINT `PK_swf_PI_Distribution` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId` FOREIGN KEY (`ProcessInstanceId`) REFERENCES `swf_processInstance` (`Id`)
) CHARACTER SET=utf8mb4 COMMENT='工作流程分发记录';

CREATE TABLE `swf_PI_ProcessInstanceTags` (
    `Id` bigint NOT NULL AUTO_INCREMENT,
    `TagId` int NULL,
    `Color` varchar(20) CHARACTER SET utf8mb4 NULL,
    `ProcessInstanceId` int NULL,
    `CanDelete` tinyint(1) NOT NULL,
    CONSTRAINT `PK_swf_PI_ProcessInstanceTags` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId` FOREIGN KEY (`TagId`) REFERENCES `swf_PI_Tags` (`Id`),
    CONSTRAINT `FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstan~` FOREIGN KEY (`ProcessInstanceId`) REFERENCES `swf_processInstance` (`Id`)
) CHARACTER SET=utf8mb4 COMMENT='工作流程标记';

CREATE TABLE `swf_WorkActivity` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `Priority` int NOT NULL DEFAULT 100,
    `WorkTaskId` int NULL,
    `AssignPerformers` varchar(1000) CHARACTER SET utf8mb4 NULL,
    `Status` int NOT NULL,
    `AssignTime` datetime(6) NULL,
    `TaskCreatingGroup` varchar(32) CHARACTER SET utf8mb4 NULL,
    `DisposeUser` varchar(50) CHARACTER SET utf8mb4 NULL,
    `DisposeUserName` varchar(50) CHARACTER SET utf8mb4 NULL,
    `DisposeTime` datetime(6) NULL,
    `CreateTime` datetime(6) NOT NULL,
    `Command` longtext CHARACTER SET utf8mb4 NULL,
    `Comment` varchar(1000) CHARACTER SET utf8mb4 NULL,
    `TimeSpan` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `ProcessInstanceId` int NULL,
    CONSTRAINT `PK_swf_WorkActivity` PRIMARY KEY (`Id`),
    CONSTRAINT `FK_swf_WorkActivity_swf_node_WorkTaskId` FOREIGN KEY (`WorkTaskId`) REFERENCES `swf_node` (`Id`),
    CONSTRAINT `FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId` FOREIGN KEY (`ProcessInstanceId`) REFERENCES `swf_processInstance` (`Id`)
) CHARACTER SET=utf8mb4;

CREATE INDEX `IX_swf_condition_decision_ConditionDecisionId` ON `swf_condition_decision` (`ConditionDecisionId`);

CREATE INDEX `IX_swf_condition_decision_NodeId` ON `swf_condition_decision` (`NodeId`);

CREATE INDEX `IX_swf_files_ProcessInstanceId` ON `swf_files` (`ProcessInstanceId`);

CREATE INDEX `IX_swf_node_AssignerId` ON `swf_node` (`AssignerId`);

CREATE INDEX `IX_swf_node_ElseNodeId` ON `swf_node` (`ElseNodeId`);

CREATE INDEX `IX_swf_node_NextNodeId` ON `swf_node` (`NextNodeId`);

CREATE INDEX `IX_swf_node_WorkActivityCompleteScriptId` ON `swf_node` (`WorkActivityCompleteScriptId`);

CREATE INDEX `IX_swf_node_WorkProcessId` ON `swf_node` (`WorkProcessId`);

CREATE INDEX `IX_swf_node_WorkTaskCompleteScriptId` ON `swf_node` (`WorkTaskCompleteScriptId`);

CREATE INDEX `IX_swf_node_WorkTaskStartScriptId` ON `swf_node` (`WorkTaskStartScriptId`);

CREATE INDEX `IX_swf_PermissionPerformer_WorkProcessPermissionId` ON `swf_PermissionPerformer` (`WorkProcessPermissionId`);

CREATE INDEX `IX_swf_PI_Distribution_ProcessInstanceId` ON `swf_PI_Distribution` (`ProcessInstanceId`);

CREATE INDEX `IX_swf_PI_ProcessInstanceTags_ProcessInstanceId` ON `swf_PI_ProcessInstanceTags` (`ProcessInstanceId`);

CREATE INDEX `IX_swf_PI_ProcessInstanceTags_TagId` ON `swf_PI_ProcessInstanceTags` (`TagId`);

CREATE INDEX `IX_swf_processInstance_Number` ON `swf_processInstance` (`Number`);

CREATE INDEX `IX_swf_processInstance_WorkProcessId` ON `swf_processInstance` (`WorkProcessId`);

CREATE INDEX `IX_swf_WorkActivity_ProcessInstanceId` ON `swf_WorkActivity` (`ProcessInstanceId`);

CREATE INDEX `IX_swf_WorkActivity_WorkTaskId` ON `swf_WorkActivity` (`WorkTaskId`);

CREATE INDEX `IX_swf_workflowLog_ProcessInstanceNumber` ON `swf_workflowLog` (`ProcessInstanceNumber`);

CREATE UNIQUE INDEX `IX_swf_workProcess_Name_Version` ON `swf_workProcess` (`Name`, `Version`);

CREATE INDEX `IX_swf_workProcess_OnCancelId` ON `swf_workProcess` (`OnCancelId`);

CREATE INDEX `IX_swf_workProcess_OnCompleteId` ON `swf_workProcess` (`OnCompleteId`);

CREATE INDEX `IX_swf_workProcess_OnStartId` ON `swf_workProcess` (`OnStartId`);

CREATE INDEX `IX_swf_workProcessPermission_ProcessName` ON `swf_workProcessPermission` (`ProcessName`);

CREATE INDEX `IX_swf_WorkTaskCommand_WorkTaskId` ON `swf_WorkTaskCommand` (`WorkTaskId`);

INSERT INTO `swf_efmigrationshistory` (`MigrationId`, `ProductVersion`)
VALUES ('20250616060725_init3', '8.0.10');

COMMIT;

