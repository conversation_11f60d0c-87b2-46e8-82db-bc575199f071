﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.ViewModels.Permissions;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
public class WorkProcessPermissionStore<T> : IWorkProcessPermissionStore where T : DbContext
{
    private readonly T _dbContext;
    private readonly ILogger<WorkProcessPermissionStore<T>> _logger;


    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    /// <param name="userClient"></param>
    public WorkProcessPermissionStore(T dbContext, ILogger<WorkProcessPermissionStore<T>> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    public void Delete(int id)
    {
        var entity = GetById(id);
        _dbContext.Remove(entity);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="search"></param>
    /// <param name="currentName"></param>
    /// <returns></returns>
    public Task<int> Count(WorkProcessPermissionSearch search, PermissionPerformer[] performers, string currentName)
    {
        var entity = MakeQuery(search, performers, currentName);
        return entity.CountAsync();
    }

    public IQueryable<WorkProcessPermission> Permissions => _dbContext.Set<WorkProcessPermission>();

    /// <summary>
    /// </summary>
    /// <param name="workflowName"></param>
    /// <returns></returns>
    public async Task<WorkProcessPermission> GetByProcessNameAsync(string workflowName)
    {
        return await Permissions
            .Where(_ => _.ProcessName == workflowName).FirstOrDefaultAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="removePerformers"></param>
    public void Remove(IEnumerable<PermissionPerformer> removePerformers)
    {
        _dbContext.RemoveRange(removePerformers);
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcessName"></param>
    /// <returns></returns>
    public async Task<IEnumerable<WorkProcessPermission>> GetByNamesAsync(IEnumerable<string> workProcessName)
    {
        return await Permissions
            .Include(_ => _.Performers)
            .Where(_ => workProcessName.Contains(_.ProcessName)).ToListAsync();
    }

    public IEnumerable<WorkProcessPermission> AllPermissions()
    {
        return _dbContext.Set<WorkProcessPermission>()
            .Include(_ => _.Performers).ToList();
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public WorkProcessPermission GetById(int id)
    {
        var entity = Permissions.FirstOrDefault(_ => _.Id == id);
        return entity;
    }

    /// <summary>
    /// </summary>
    /// <param name="workTask"></param>
    /// <exception cref="workTask">workTask is null</exception>
    public void AddOrUpdate(WorkProcessPermission workTask)
    {
        if (workTask == null) throw new ArgumentNullException(nameof(workTask));
        _dbContext.Update(workTask);
    }

    /// <summary>
    /// </summary>
    /// <param name="search"></param>
    /// <param name="performers"></param>
    /// <param name="currentName"></param>
    /// <returns></returns>
    public IEnumerable<WorkProcess> List(WorkProcessPermissionSearch search, PermissionPerformer[] performers, string currentName)
    {
        var stopwatch = new Stopwatch();
        var query = MakeQuery(search, performers, currentName);
        stopwatch.Start();
        var result = query
            .Skip((search.Page - 1) * search.PageSize)
            .Take(search.PageSize).ToList();
        stopwatch.Stop();
        _logger.LogInformation("执行获取工作流程定义，耗时:{0}", stopwatch.Elapsed.TotalSeconds);
        return result;
    }

    private IQueryable<WorkProcess> MakeQuery(WorkProcessPermissionSearch search, PermissionPerformer[] performers, string currentName)
    {
        /**
         * 显示结果逻辑
         * 1. 用户属于 流程管理员： 有WorkPrcessPermission.ManagerRole 中的角色
         * 2. 用户是 可以执行这些流程的人，属于 PermissionPerformer 人员之一。
           3. 用户是流程的创建人。
         */

        var permissionBuilder = PredicateBuilder.New<WorkProcessPermission>(true);
        if (search.ProcessName != null) permissionBuilder = permissionBuilder.And(_ => _.ProcessName.Contains(search.ProcessName));

        var roles = performers.Where(_ => _.Type == PerformerType.Role).Select(_ => _.Name);

        //如果查看有没有管理角色，
        Expression<Func<WorkProcessPermission, bool>> performerAndManagerBuilder = PredicateBuilder.New<WorkProcessPermission>(false);
        ;
        foreach (var role in roles)
            performerAndManagerBuilder = performerAndManagerBuilder.Or(_ => _.ManageRoles.Contains(role));


        if (performers.Any())
        {
            var performerBuilder = PredicateBuilder.New<PermissionPerformer>(false);
            foreach (var performer in performers) performerBuilder = performerBuilder.Or(_ => _.Name == performer.Name && _.Type == performer.Type);

            var performerQuery = _dbContext.Set<PermissionPerformer>().Where(performerBuilder).Select(_ => _.WorkProcessPermission.Id).ToList();

            performerAndManagerBuilder = performerAndManagerBuilder.Or(_ => performerQuery.Contains(_.Id));
        }

        permissionBuilder = permissionBuilder.And(performerAndManagerBuilder);
        var permissionQuery = Permissions.Where(permissionBuilder).Select(_ => _.ProcessName).Distinct();


        var query = _dbContext.Set<WorkProcess>().Where(_ =>
                (permissionQuery.Contains(_.Name) || _.Creator == currentName) &&
                (string.IsNullOrEmpty(search.ProcessName) || _.Name.Contains(search.ProcessName))
            )
            .OrderByDescending(_ => _.Enable)
            .ThenByDescending(_ => _.Version);

        return query;
    }

    /// <summary>
    /// </summary>
    /// <param name="performers"></param>
    /// <returns></returns>
    public IEnumerable<string> WorkProcessNames(PermissionPerformer[] performers)
    {
        var roles = performers.Where(_ => _.Type == PerformerType.Role).Select(_ => _.Name);
        var performerQuery = PredicateBuilder.New<PermissionPerformer>()
            .And(_ => roles.Contains(_.WorkProcessPermission.ManageRoles));


        foreach (var performer in performers) performerQuery.Or(_ => _.Name == performer.Name && _.Type == performer.Type);

        var commandQuery = PredicateBuilder.New<WorkProcessPermission>()
            .And(_ => performers.Select(_ => _.Name).Contains(_.ManageRoles));


        return Permissions.Where(commandQuery.Expand()).Select(_ => _.ProcessName).ToList();
    }
}