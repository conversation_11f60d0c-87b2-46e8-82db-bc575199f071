﻿using System.Threading.Tasks;
using Coder.ScriptWorkflow.Debugger;
using Microsoft.AspNetCore.SignalR;

namespace Coder.ScriptWorkflow.WebApi.Modules;

public class DebuggerSignalRContext : IDebuggerPusher
{
    private readonly IHubContext<DebuggerHub> _hubContext;

    public DebuggerSignalRContext(IHubContext<DebuggerHub> hubContext)
    {
        _hubContext = hubContext;
    }


    public Task SendMessageAsync(int processInstance, DebuggerInfo message)
    {
        return _hubContext.Clients.Group("pi_" + processInstance).SendCoreAsync("onDebugger", new object
            [] { message });
    }
}