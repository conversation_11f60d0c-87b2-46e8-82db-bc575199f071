﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;


namespace Coder.ScriptWorkflow.Interceptors;

/// <summary>
///     拦截器设置
/// </summary>
public interface IWorkflowInterceptor
{
    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="processInstance"></param>
    /// <param name="provider"></param>
    void OnProcessChanged([NotNull] IWorkflowContext topContext, [NotNull] ProcessInstance processInstance, [NotNull] IServiceProvider provider);

    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="workActivity"></param>
    /// <param name="provider"></param>
    void OnWorkActivityChanged([NotNull] IWorkflowContext topContext, [NotNull] WorkActivity workActivity, [NotNull] IServiceProvider provider);

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="userName"></param>
    Task NotifyDistributionUserAsync(IWorkflowContext workflowContext, [NotNull] IEnumerable<string> userName);
}