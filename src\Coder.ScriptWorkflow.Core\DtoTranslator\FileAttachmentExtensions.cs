﻿using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow.DtoTranslator;

public static class FileAttachmentExtensions
{
    public static FileAttachmentVieModel ToViewModel(this ProcessInstance.FileAttach fileAttach)
    {
        return new FileAttachmentVieModel
        {
            FileName = fileAttach.FileName,
            Id = fileAttach.Id,
            ProcessInstanceId = fileAttach.ProcessInstance.Id,
            ProcessInstanceName = fileAttach.ProcessInstance.WorkProcess.Name,
            ProcessInstanceNumber = fileAttach.ProcessInstance.Number,
            ProcessInstanceSubject = fileAttach.ProcessInstance.Subject
        };
    }
}