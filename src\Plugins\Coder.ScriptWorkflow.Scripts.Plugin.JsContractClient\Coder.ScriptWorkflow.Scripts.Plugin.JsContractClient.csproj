﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

	</PropertyGroup>

	<ItemGroup>
		<None Remove="defined.d.ts" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="defined.d.ts">
			<SubType>Code</SubType>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Coder.Orgs.Clients.Http" Version="6.1.1" />
		<PackageReference Include="oa.contract.service.HttpClients" Version="2.2.5" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Coder.ScriptWorkflow.Abstractions\Coder.ScriptWorkflow.Abstractions.csproj" />
		<ProjectReference Include="..\..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
	</ItemGroup>
</Project>
