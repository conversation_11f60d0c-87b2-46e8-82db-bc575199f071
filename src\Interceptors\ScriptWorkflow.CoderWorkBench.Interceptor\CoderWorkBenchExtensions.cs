﻿using Coder.ScriptWorkflow.Interceptors;
using Microsoft.Extensions.DependencyInjection;
using oa.workbench.service.HttpClients;

namespace Coder.ScriptWorkflow.CoderWorkBench.Interceptor;

public static class CoderWorkBenchExtensions
{
    public static WorkflowOptions AddCoderWorkBench(this WorkflowOptions options, string host,
        string prefix = "workbench")
    {
        if (!host.EndsWith('/')) host += "/";

        var url = host + prefix;
        options.Services.AddWorkbenchHttpService(url);
        options.Services.AddTransient<IWorkflowInterceptor, WorkflowInterceptor>();

        return options;
    }
}