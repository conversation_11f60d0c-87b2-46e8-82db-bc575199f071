﻿using System;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface ITransactionManage:IDisposable
{
    /// <summary>
    /// </summary>
    void Rollback();

    /// <summary>
    /// </summary>
    void Commit();
}

/// <summary>
/// </summary>
public interface ITransactionFactory
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public ITransactionManage BeginTransactions();
}