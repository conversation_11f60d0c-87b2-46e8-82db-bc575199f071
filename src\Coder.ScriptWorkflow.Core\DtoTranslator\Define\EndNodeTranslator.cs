﻿using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

internal class EndNodeTranslator : NodeTranslator<EndNodeSubmit, EndNode>
{
    protected override void FillToEntity(EndNode node, EndNodeSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore)
    {
    }

    protected override void FillToViewModel(EndNode src, EndNodeSubmit target)
    {
    }
}