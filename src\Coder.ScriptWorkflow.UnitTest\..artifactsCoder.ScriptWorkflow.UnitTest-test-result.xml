<testsuites>
  <testsuite name="Coder.ScriptWorkflow.UnitTest.dll" tests="90" skipped="0" failures="6" errors="0" time="23.3355052" timestamp="2025-07-05T09:12:33" hostname="LEO-PC-2" id="0" package="Coder.ScriptWorkflow.UnitTest.dll">
    <properties />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.UserAssingerTranslatorTest" name="Fill" time="0.0029483" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest" name="TestCancel" time="0.0030797" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Node.WorkProcessTest" name="TestAdd" time="0.0037912" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.ProcessInstanceFormTest" name="Test" time="0.0221736" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.NodeSubmitTest" name="Test_StartNode" time="0.0280810" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkActivityTest" name="Resolve" time="0.0287460" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.ScriptAssignerTranslatorTest" name="ToViewModel" time="0.0031245" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.NodeSubmitTest" name="Test_EndNode" time="0.0019145" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.WorkActivityFormTest" name="Test" time="0.0026519" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.UserAssingerTranslatorTest" name="ToViewModel" time="0.0265887" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.WorkTaskSubmitTest" name="WorkTaskFillToWorkTask(workTaskId: 1)" time="0.0375019" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.WorkTaskSubmitTest" name="WorkTaskFillToWorkTask(workTaskId: 0)" time="0.0002863" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="Equals_ShouldReturnFalse_WhenObjIsNotAssignUser" time="0.0275051">
      <failure type="failure" message="System.InvalidCastException : Unable to cast object of type 'System.String' to type 'Coder.ScriptWorkflow.Assigners.AssignUser'.">at Coder.ScriptWorkflow.Assigners.AssignUser.Equals(Object obj) in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Abstractions\Assigners\AssignUser.cs:line 37
   at Coder.ScriptWorkflow.UnitTest.AssignUserTests.Equals_ShouldReturnFalse_WhenObjIsNotAssignUser() in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\AssignUserTests.cs:line 73
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)</failure>
    </testcase>
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.WorkProcessSubmitTest" name="SetConstructions" time="0.0083411" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.NodeSubmitTest" name="AllTest" time="0.0114039" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.ScriptDescitionSubmitTest" name="FllByScriptDecision" time="0.0017652" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.Translator.WorkTaskSubmitTest" name="WorkTaskSubmitConstruction" time="0.0068660" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ViewModel.WorkflowResolveSubmitTest" name="Test" time="0.0219022" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.TagTest" name="TestTagGetDisposeName" time="0.0144625" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.TagTest" name="TestTagSetDisposeName" time="0.0001309" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="GetHashCode_ShouldReturnDifferentValue_ForDifferentUserName" time="0.0199485" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="Equals_ShouldReturnFalse_ForDifferentUserName" time="0.0011114" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="Equals_ShouldReturnTrue_ForSameReference" time="0.0002493" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="GetHashCode_ShouldReturnSameValue_ForSameUserName" time="0.0004888" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="Equals_ShouldReturnFalse_WhenObjIsNull" time="0.0001090" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="GetHashCode_ShouldFallbackToBase_WhenUserNameIsNull" time="0.0016222" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.AssignUserTests" name="Equals_ShouldReturnTrue_ForSameUserName" time="0.0001271" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Scripts.ScriptProcessInstanceTest" name="TestDateTimeType" time="0.1736498" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Scripts.ScriptProcessInstanceTest" name="TestOnStart" time="0.0024282" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Settings.UserSettingTest" name="Test" time="0.1880178" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_PerformerType(key: &quot;org&quot;, performerType: Org)" time="0.2305609" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MultiMatcher" time="0.2336890" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= {type:1,key:'role1'}&quot;, scope: Role, performers: [&quot;role1&quot;])" time="0.2000642" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkTaskScriptTest" name="Test(script: &quot;return true&quot;, result: True)" time="0.2355130" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkTaskCommandScriptTest" name="Test" time="0.2394960" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_PerformerType(key: &quot;role&quot;, performerType: Role)" time="0.0105518" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MatchTest(script: &quot;return false&quot;, workTaskName: &quot;otherNode&quot;)" time="0.0097605" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= [{type:1,key:'role1'},{type:1,key:'role2'}]&quot;, scope: Role, performers: [&quot;role1&quot;, &quot;role2&quot;])" time="0.0099545" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkTaskScriptTest" name="Test(script: &quot;return false&quot;, result: False)" time="0.0091146" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_PerformerType(key: &quot;user&quot;, performerType: User)" time="0.0092059" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkTaskCommandScriptTest" name="TestNoneExecute" time="0.0128894" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MatchTest(script: &quot;return true&quot;, workTaskName: &quot;NextNode&quot;)" time="0.0224808" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkTaskScriptTest" name="Test(script: &quot;&quot;, result: True)" time="0.0222024" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= [{type:2,key:'dept1'},{type:2,key:'dept2'}]&quot;, scope: Org, performers: [&quot;dept1&quot;, &quot;dept2&quot;])" time="0.0226019" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_WorkActivityStatus(key: &quot;取消&quot;, status: CloseByAdmin)" time="0.0219764" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MatchTest(script: &quot;return 0&quot;, workTaskName: &quot;otherNode&quot;)" time="0.0106012" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= [{type:0,key:'user1',name:'user1-name'},{ty&quot;···, scope: User, performers: [&quot;user1&quot;, &quot;user2&quot;])" time="0.0095619" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_WorkActivityStatus(key: &quot;处理中&quot;, status: Processing)" time="0.0082120" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MatchTest(script: &quot;return ''&quot;, workTaskName: &quot;otherNode&quot;)" time="0.0079401" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= {type:0,key:'user1',name:'管理员'}&quot;, scope: User, performers: [&quot;user1&quot;])" time="0.0077318" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_WorkActivityStatus(key: &quot;完成&quot;, status: Complete)" time="0.0100024" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= {type:2,key:'dept1'}&quot;, scope: Org, performers: [&quot;dept1&quot;])" time="0.0083160" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MatchTest(script: &quot;var a=1&quot;, workTaskName: &quot;NextNode&quot;)" time="0.0112021" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_WorkActivityStatus(key: &quot;等待处理&quot;, status: UnAssign)" time="0.0083049" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= ['user1','user2']&quot;, scope: User, performers: [&quot;user1&quot;, &quot;user2&quot;])" time="0.0227784" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="MatchTest(script: &quot;return 1&quot;, workTaskName: &quot;NextNode&quot;)" time="0.0224630" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_ProcessInstanceStatus(key: &quot;已完成&quot;, status: Completed)" time="0.0090568" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="TestAssignResult(script: &quot;var f= 'user1'&quot;, scope: User, performers: [&quot;user1&quot;])" time="0.0110555" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_ProcessInstanceStatus(key: &quot;草拟中&quot;, status: Created)" time="0.0078111" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="ErrorOnAssignResult(script: &quot;f = ''&quot;)" time="0.0112227" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_ProcessInstanceStatus(key: &quot;挂起&quot;, status: Suspend)" time="0.0079344" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest" name="ScriptError" time="0.0192893">
      <failure type="failure" message="Assert.Null() Failure: Value is not null&#xD;&#xA;Expected: null&#xD;&#xA;Actual:   0/0/0">at Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest.ScriptError() in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\ScriptDecisionTest.cs:line 68
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)</failure>
    </testcase>
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_ProcessInstanceStatus(key: &quot;取消&quot;, status: Cancel)" time="0.0071985" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="ErrorOnAssignResult(script: &quot;f = []&quot;)" time="0.0080788" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestGlobalSettings_ProcessInstanceStatus(key: &quot;审批中&quot;, status: Processing)" time="0.0073894" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="ToDynamicObjectTest" time="0.0112176" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="ErrorTest" time="0.0132064" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestJsonDateFormat" time="0.0236298">
      <failure type="failure" message="Assert.Equal() Failure: Strings differ&#xD;&#xA;                    ↓ (pos 9)&#xD;&#xA;Expected: &quot;2025-07-05T17:12:44&quot;&#xD;&#xA;Actual:   &quot;2025-07-06T01:12:44&quot;&#xD;&#xA;                    ↑ (pos 9)">at Coder.ScriptWorkflow.UnitTest.WorkflowContextTest.TestJsonDateFormat() in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowContextTest.cs:line 176
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)</failure>
    </testcase>
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="Test" time="0.0118463" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.JsValueConvertTest" name="ArrayString" time="0.0003831" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowContextTest" name="TestAddTag" time="0.0142877" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Plugins.JsHttpClientTest" name="TestGet" time="1.5388848" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Plugins.JsHttpClientTest" name="TestPostJson" time="0.0477611" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Plugins.JsHttpClientTest" name="CallDeleteJson" time="0.0103956" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Plugins.JsHttpClientTest" name="TestPutJson" time="0.0051829" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.PresistentTest.DefinedTest" name="MultiEntryPoint" time="2.0692200" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest" name="OnStartServerError" time="2.2129087">
      <failure type="failure" message="System.ArgumentException : 抛出服务器错误哦.1">at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.ErrorPlugin.Execute(Int32 a) in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs:line 489
   at Execute(Closure, Object, Context, Expression[], Arguments)
   at NiL.JS.Core.Functions.MethodProxy.invokeMethod(JSValue targetValue, Expression[] argumentsSource, Arguments argumentsObject, Context initiator)
   at NiL.JS.Core.Functions.MethodProxy.InternalInvoke(JSValue targetValue, Expression[] argumentsSource, Context initiator, Boolean withSpread, Boolean withNew)
   at NiL.JS.Expressions.Call.Evaluate(Context context)
   at NiL.JS.Statements.CodeBlock.evaluateLines(Context context, Int32 i, Boolean clearSuspendData)
   at NiL.JS.Statements.CodeBlock.Evaluate(Context context)
   at NiL.JS.Core.Context.doEval(CodeBlock body, Context context)
   at NiL.JS.Core.Context.Eval(String sourceCode, JSValue thisBind, Boolean suppressScopeCreation)
   at NiL.JS.Core.Context.Eval(String code, Boolean suppressScopeCreation)
   at Coder.ScriptWorkflow.Scripts.WorkProcessScript.Invoke(IWorkflowContext workflowContext, String workProcessEvents) in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Core\Scripts\WorkProcessScript.cs:line 32
   at Coder.ScriptWorkflow.WorkflowManager.Start(ProcessInstance instance, UserViewModel currentUser) in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Core\WorkflowManager.cs:line 432
   at Coder.ScriptWorkflow.WorkflowManager.Start(Int32 processInstanceId, UserViewModel currentUser) in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Core\WorkflowManager.cs:line 400
   at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.OnStartServerError() in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs:line 230
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)</failure>
    </testcase>
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.ScriptAssignerFirstEntry" name="TestNextUsers" time="2.3046222" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.UserAssignerFirstEntry" name="Test_Auth1_NextUserInRole_AllOfThem(commandName: &quot;审批1&quot;, userNamePrefix: &quot;role&quot;)" time="2.3218546" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Example" name="请假.Test.Retry" time="2.3439760" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.ResolveAssigner.ResolveScriptAssigner" name="ResolveCommandScriptError" time="2.3456232" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.UserAssignerRentrySameWorkTask" name="TestRetry(assignScopeType: SomeOfThem)" time="2.3529914" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.Example" name="会签.Test.Retry" time="2.3919595" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.UserAssignerFirstEntry" name="Test_Auth1_NextUserInRole_AllOfThem(commandName: &quot;审批2&quot;, userNamePrefix: &quot;org&quot;)" time="0.1513006" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.UserAssignerRentrySameWorkTask" name="TestRetry(assignScopeType: AllOfThem)" time="0.1797089" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest" name="Normal" time="0.3253905" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.UserAssignerFirstEntry" name="Test_Auth1_NextUserInRole_SomeOfThen(commandName: &quot;审批1&quot;, userNamePrefix: &quot;role&quot;)" time="0.1095365" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest" name="OnStartError" time="0.0747203">
      <failure type="failure" message="Assert.Equal() Failure: Strings differ&#xD;&#xA;Expected: ···&quot;Variable &quot;Json&quot; is not defined at (1:1*4)&quot;&#xD;&#xA;Actual:   ···&quot; is not defined at (1:1*4),codeLine:1/1/4&quot;&#xD;&#xA;                                        ↑ (pos 84)">at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.OnStartError() in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs:line 192
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)</failure>
    </testcase>
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers.UserAssignerFirstEntry" name="Test_Auth1_NextUserInRole_SomeOfThen(commandName: &quot;审批2&quot;, userNamePrefix: &quot;org&quot;)" time="0.0900768" />
    <testcase classname="Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest" name="TestPerformerSearcherInTags" time="0.2375925">
      <failure type="failure" message="Assert.Single() Failure: The collection was empty">at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.TestPerformerSearcherInTags() in D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs:line 424
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)</failure>
    </testcase>
    <system-out>
Test Framework Informational Messages:
[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.8.2+699d445a1a (64-bit .NET 8.0.17)
[xUnit.net 00:00:00.09]   Discovering: Coder.ScriptWorkflow.UnitTest
[xUnit.net 00:00:00.16]   Discovered:  Coder.ScriptWorkflow.UnitTest
[xUnit.net 00:00:00.16]   Starting:    Coder.ScriptWorkflow.UnitTest
     Warning:
     The component "Fluent Assertions" is governed by the rules defined in the Xceed License Agreement and
     the Xceed Fluent Assertions Community License. You may use Fluent Assertions free of charge for
     non-commercial use only. An active subscription is required to use Fluent Assertions for commercial use.
     Please contact Xceed Sales mailto:<EMAIL> to acquire a subscription at a very low cost.
     A paid commercial license supports the development and continued increasing support of
     Fluent Assertions users under both commercial and community licenses. Help us
     keep Fluent Assertions at the forefront of unit testing.
     For more information, visit https://xceed.com/products/unit-testing/fluent-assertions/
[xUnit.net 00:00:00.27]       System.InvalidCastException : Unable to cast object of type 'System.String' to type 'Coder.ScriptWorkflow.Assigners.AssignUser'.
[xUnit.net 00:00:00.27]       Stack Trace:
[xUnit.net 00:00:00.27]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Abstractions\Assigners\AssignUser.cs(37,0): at Coder.ScriptWorkflow.Assigners.AssignUser.Equals(Object obj)
[xUnit.net 00:00:00.27]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\AssignUserTests.cs(73,0): at Coder.ScriptWorkflow.UnitTest.AssignUserTests.Equals_ShouldReturnFalse_WhenObjIsNotAssignUser()
[xUnit.net 00:00:00.27]            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
[xUnit.net 00:00:00.27]            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
dbug: Microsoft.AspNetCore.Hosting.Diagnostics[3]
      Hosting starting
[xUnit.net 00:00:00.54]       Assert.Null() Failure: Value is not null
[xUnit.net 00:00:00.54]       Expected: null
[xUnit.net 00:00:00.54]       Actual:   0/0/0
[xUnit.net 00:00:00.54]       Stack Trace:
[xUnit.net 00:00:00.54]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\ScriptDecisionTest.cs(68,0): at Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest.ScriptError()
[xUnit.net 00:00:00.54]            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
[xUnit.net 00:00:00.54]            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
[xUnit.net 00:00:00.58]       Assert.Equal() Failure: Strings differ
[xUnit.net 00:00:00.58]                           ↓ (pos 9)
[xUnit.net 00:00:00.58]       Expected: "2025-07-05T17:12:44"
[xUnit.net 00:00:00.58]       Actual:   "2025-07-06T01:12:44"
[xUnit.net 00:00:00.58]                           ↑ (pos 9)
[xUnit.net 00:00:00.58]       Stack Trace:
[xUnit.net 00:00:00.58]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowContextTest.cs(176,0): at Coder.ScriptWorkflow.UnitTest.WorkflowContextTest.TestJsonDateFormat()
[xUnit.net 00:00:00.58]            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
[xUnit.net 00:00:00.58]            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderFactory[12]
      Registered model binder providers, in the following order: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider, Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider
dbug: Microsoft.AspNetCore.SignalR.Internal.DefaultHubProtocolResolver[1]
      Registered SignalR Protocol: json, implemented by Microsoft.AspNetCore.SignalR.Protocol.JsonHubProtocol.
info: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[63]
      User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-01ed8e0a-94c8-4552-ae6a-9e3d6edcc5d2.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-10a6738c-9d7a-499d-bb71-617b7fda7cea.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-5462ba92-6fb1-4935-8845-6a86e15d340d.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-5590e58a-429e-486b-8b73-b10c90ce4b25.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-7162cab1-d4bd-4e48-b90f-b9d6d18b0056.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-96670502-143a-449d-a561-d33fb1f2f5e0.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-bcc89e2d-8b46-4331-b0dd-19d447990ce4.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-e002cb41-70df-4a3d-9d8e-032fb992e2f6.xml'.
dbug: Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository[37]
      Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-e5d2ccbf-2d7a-4f78-b522-ef0baf6565d8.xml'.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {01ed8e0a-94c8-4552-ae6a-9e3d6edcc5d2}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {10a6738c-9d7a-499d-bb71-617b7fda7cea}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {5462ba92-6fb1-4935-8845-6a86e15d340d}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {5590e58a-429e-486b-8b73-b10c90ce4b25}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {7162cab1-d4bd-4e48-b90f-b9d6d18b0056}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {96670502-143a-449d-a561-d33fb1f2f5e0}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {bcc89e2d-8b46-4331-b0dd-19d447990ce4}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {e002cb41-70df-4a3d-9d8e-032fb992e2f6}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[18]
      Found key {e5d2ccbf-2d7a-4f78-b522-ef0baf6565d8}.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.DefaultKeyResolver[13]
      Considering key {7162cab1-d4bd-4e48-b90f-b9d6d18b0056} with expiration date 2025-08-08 08:51:51Z as default key.
dbug: Microsoft.AspNetCore.DataProtection.TypeForwardingActivator[0]
      Forwarded activator type request from Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
dbug: Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor[51]
      Decrypting secret element using Windows DPAPI.
dbug: Microsoft.AspNetCore.DataProtection.TypeForwardingActivator[0]
      Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
dbug: Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.CngCbcAuthenticatedEncryptorFactory[4]
      Opening CNG algorithm 'AES' from provider '(null)' with chaining mode CBC.
dbug: Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.CngCbcAuthenticatedEncryptorFactory[3]
      Opening CNG algorithm 'SHA256' from provider '(null)' with HMAC.
dbug: Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingProvider[2]
      Using key {7162cab1-d4bd-4e48-b90f-b9d6d18b0056} as the default key.
dbug: Microsoft.AspNetCore.DataProtection.Internal.DataProtectionHostedService[65]
      Key ring with default key {7162cab1-d4bd-4e48-b90f-b9d6d18b0056} was loaded during application startup.
dbug: Microsoft.AspNetCore.Hosting.Diagnostics[4]
      Hosting started
dbug: Microsoft.AspNetCore.Hosting.Diagnostics[13]
      Loaded hosting startup assembly Coder.ScriptWorkflow.UnitTest
dbug: Microsoft.EntityFrameworkCore.Infrastructure[10409]
      An additional 'IServiceProvider' was created for internal use by Entity Framework. An existing service provider was not used due to the following configuration changes: configuration changed for 'Core:EnableSensitiveDataLogging'.
warn: Microsoft.EntityFrameworkCore.Model.Validation[10400]
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
warn: Microsoft.EntityFrameworkCore.Model.Validation[10620]
      The property 'WorkActivity.AssignPerformers' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Assigner.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'BoolScriptDecision.ElseNodeId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ConditionSetting.ConditionDecisionId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ConditionSetting.NodeId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Node.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Node.NextNodeId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Node.WorkProcessId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'PermissionPerformer.WorkProcessPermissionId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ProcessInstance.WorkProcessId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'FileAttach.ProcessInstanceId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ScriptDefined.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'SwfSetting.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkActivity.ProcessInstanceId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkActivity.WorkTaskId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkProcess.OnCancelId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkProcess.OnCompleteId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkProcess.OnStartId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.AssignerId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.WorkActivityCompleteScriptId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.WorkTaskCompleteScriptId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.WorkTaskStartScriptId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTaskCommand.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTaskCommand.WorkTaskId' was created in shadow state because there are no eligible CLR members with a matching name.
warn: Microsoft.EntityFrameworkCore.Model.Validation[20601]
      The 'Priority' property 'Priority' on entity type 'ProcessInstance' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'Priority' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
warn: Microsoft.EntityFrameworkCore.Model.Validation[20601]
      The 'Priority' property 'Priority' on entity type 'WorkActivity' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'Priority' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
dbug: Microsoft.EntityFrameworkCore.Infrastructure[10403]
      Entity Framework Core 8.0.10 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.Sqlite:8.0.10' with options: SensitiveDataLoggingEnabled using lazy loading proxies CommandTimeout=300 MigrationsAssembly=Coder.ScriptWorkflow.Migrations.Sqlite MigrationsHistoryTable=swf_efmigrationshistory 
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20005]
      Creating DbConnection.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20006]
      Created DbConnection. (5ms).
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20000]
      Opening connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20001]
      Opened connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20007]
      Disposing connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20008]
      Disposed connection to database 'main' on server '_data/database.db' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20005]
      Creating DbConnection.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20006]
      Created DbConnection. (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20000]
      Opening connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20001]
      Opened connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20002]
      Closing connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20003]
      Closed connection to database 'main' on server '_data/database.db' (1ms).
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20005]
      Creating DbConnection.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20006]
      Created DbConnection. (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20000]
      Opening connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20004]
      An error occurred using the connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20007]
      Disposing connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20008]
      Disposed connection to database 'main' on server '_data/database.db' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20000]
      Opening connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20001]
      Opened connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (3ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      PRAGMA journal_mode = 'wal';
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (23ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      PRAGMA journal_mode = 'wal';
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20002]
      Closing connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20003]
      Closed connection to database 'main' on server '_data/database.db' (0ms).
warn: Microsoft.EntityFrameworkCore.Model.Validation[10620]
      The property 'WorkActivity.AssignPerformers' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Assigner.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'BoolScriptDecision.ElseNodeId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ConditionSetting.ConditionDecisionId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ConditionSetting.NodeId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Node.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Node.NextNodeId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'Node.WorkProcessId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'PermissionPerformer.WorkProcessPermissionId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ProcessInstance.WorkProcessId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'FileAttach.ProcessInstanceId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'ScriptDefined.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'SwfSetting.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkActivity.ProcessInstanceId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkActivity.WorkTaskId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkProcess.OnCancelId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkProcess.OnCompleteId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkProcess.OnStartId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.AssignerId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.WorkActivityCompleteScriptId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.WorkTaskCompleteScriptId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTask.WorkTaskStartScriptId' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTaskCommand.Discriminator' was created in shadow state because there are no eligible CLR members with a matching name.
dbug: Microsoft.EntityFrameworkCore.Model.Validation[10600]
      The property 'WorkTaskCommand.WorkTaskId' was created in shadow state because there are no eligible CLR members with a matching name.
warn: Microsoft.EntityFrameworkCore.Model.Validation[20601]
      The 'Priority' property 'Priority' on entity type 'ProcessInstance' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'Priority' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
warn: Microsoft.EntityFrameworkCore.Model.Validation[20601]
      The 'Priority' property 'Priority' on entity type 'WorkActivity' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'Priority' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20000]
      Opening connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20001]
      Opened connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Transaction[20209]
      Beginning transaction with isolation level 'Unspecified'.
dbug: Microsoft.EntityFrameworkCore.Database.Transaction[20200]
      Began transaction with isolation level 'Serializable'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_assigner" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_assigner" PRIMARY KEY AUTOINCREMENT,
          "AssignScopeType" INTEGER NOT NULL,
          "Discriminator" TEXT NOT NULL,
          "Script" TEXT NULL,
          "Performers" TEXT NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_assigner" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_assigner" PRIMARY KEY AUTOINCREMENT,
          "AssignScopeType" INTEGER NOT NULL,
          "Discriminator" TEXT NOT NULL,
          "Script" TEXT NULL,
          "Performers" TEXT NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_global_variable" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_global_variable" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Variable" TEXT NULL,
          "Env" INTEGER NOT NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_global_variable" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_global_variable" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Variable" TEXT NULL,
          "Env" INTEGER NOT NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_globalScript" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_globalScript" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Script" text NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_globalScript" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_globalScript" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Script" text NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PI_Tags" (
          -- �������̱��
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_Tags" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Number" INTEGER NOT NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PI_Tags" (
          -- �������̱��
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_Tags" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Number" INTEGER NOT NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_scripts" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_scripts" PRIMARY KEY AUTOINCREMENT,
          "Script" TEXT NULL,
          "Discriminator" TEXT NOT NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_scripts" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_scripts" PRIMARY KEY AUTOINCREMENT,
          "Script" TEXT NULL,
          "Discriminator" TEXT NOT NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_swf_setting" (
          -- ������ʵ��
          -- id
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_swf_setting" PRIMARY KEY AUTOINCREMENT,
          -- �ű�
          "Script" text NULL,
          "Discriminator" TEXT NOT NULL,
          "Plugins" text NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_swf_setting" (
          -- ������ʵ��
          -- id
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_swf_setting" PRIMARY KEY AUTOINCREMENT,
          -- �ű�
          "Script" text NULL,
          "Discriminator" TEXT NOT NULL,
          "Plugins" text NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_workflowLog" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workflowLog" PRIMARY KEY AUTOINCREMENT,
          "LogLevel" INTEGER NOT NULL,
          -- ����id
          "ProcessInstanceId" INTEGER NOT NULL,
          -- ������
          "ProcessInstanceNumber" TEXT NULL,
          -- ����������汾
          "Version" INTEGER NOT NULL,
          -- ����������
          "WorkProcessName" TEXT NULL,
          -- �ڵ�����
          "NodeName" TEXT NULL,
          "WorkActivityId" INTEGER NULL,
          "Type" TEXT NOT NULL,
          "Content" text NULL,
          "PluginName" TEXT NULL,
          "CreateTime" TEXT NOT NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_workflowLog" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workflowLog" PRIMARY KEY AUTOINCREMENT,
          "LogLevel" INTEGER NOT NULL,
          -- ����id
          "ProcessInstanceId" INTEGER NOT NULL,
          -- ������
          "ProcessInstanceNumber" TEXT NULL,
          -- ����������汾
          "Version" INTEGER NOT NULL,
          -- ����������
          "WorkProcessName" TEXT NULL,
          -- �ڵ�����
          "NodeName" TEXT NULL,
          "WorkActivityId" INTEGER NULL,
          "Type" TEXT NOT NULL,
          "Content" text NULL,
          "PluginName" TEXT NULL,
          "CreateTime" TEXT NOT NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_workProcessPermission" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workProcessPermission" PRIMARY KEY AUTOINCREMENT,
          "ProcessName" TEXT NULL,
          "ManageRole" TEXT NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_workProcessPermission" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workProcessPermission" PRIMARY KEY AUTOINCREMENT,
          "ProcessName" TEXT NULL,
          "ManageRole" TEXT NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_workProcess" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workProcess" PRIMARY KEY AUTOINCREMENT,
          -- ͼ��
          "Icon" TEXT NULL,
          -- ��ע
          "Comment" TEXT NULL,
          -- ���
          "Abbr" TEXT NULL,
          "LogLevel" INTEGER NOT NULL,
          "FormDesign" text NULL,
          "FormManageDesign" text NULL,
          "Name" TEXT NULL,
          "UpdateTimeOffset" TEXT NULL,
          "Version" INTEGER NOT NULL,
          "OnCompleteId" INTEGER NULL,
          "OnCancelId" INTEGER NULL,
          "OnStartId" INTEGER NULL,
          "Enable" INTEGER NOT NULL,
          "Prefix" TEXT NULL,
          "Configurations" text NULL,
          "GlobalScript" text NULL,
          "FormTypeScriptDefined" text NULL,
          "Plugins" TEXT NULL,
          "Creator" TEXT NULL,
          "CanBeDeleteWorkActivityCount" INTEGER NOT NULL,
          "Group" TEXT NULL,
          CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCancelId" FOREIGN KEY ("OnCancelId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCompleteId" FOREIGN KEY ("OnCompleteId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_workProcess_swf_scripts_OnStartId" FOREIGN KEY ("OnStartId") REFERENCES "swf_scripts" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_workProcess" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_workProcess" PRIMARY KEY AUTOINCREMENT,
          -- ͼ��
          "Icon" TEXT NULL,
          -- ��ע
          "Comment" TEXT NULL,
          -- ���
          "Abbr" TEXT NULL,
          "LogLevel" INTEGER NOT NULL,
          "FormDesign" text NULL,
          "FormManageDesign" text NULL,
          "Name" TEXT NULL,
          "UpdateTimeOffset" TEXT NULL,
          "Version" INTEGER NOT NULL,
          "OnCompleteId" INTEGER NULL,
          "OnCancelId" INTEGER NULL,
          "OnStartId" INTEGER NULL,
          "Enable" INTEGER NOT NULL,
          "Prefix" TEXT NULL,
          "Configurations" text NULL,
          "GlobalScript" text NULL,
          "FormTypeScriptDefined" text NULL,
          "Plugins" TEXT NULL,
          "Creator" TEXT NULL,
          "CanBeDeleteWorkActivityCount" INTEGER NOT NULL,
          "Group" TEXT NULL,
          CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCancelId" FOREIGN KEY ("OnCancelId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCompleteId" FOREIGN KEY ("OnCompleteId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_workProcess_swf_scripts_OnStartId" FOREIGN KEY ("OnStartId") REFERENCES "swf_scripts" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PermissionPerformer" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PermissionPerformer" PRIMARY KEY AUTOINCREMENT,
          "Type" INTEGER NOT NULL,
          "Name" TEXT NULL,
          "WorkProcessPermissionId" INTEGER NULL,
          CONSTRAINT "FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProcessPermissionId" FOREIGN KEY ("WorkProcessPermissionId") REFERENCES "swf_workProcessPermission" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PermissionPerformer" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PermissionPerformer" PRIMARY KEY AUTOINCREMENT,
          "Type" INTEGER NOT NULL,
          "Name" TEXT NULL,
          "WorkProcessPermissionId" INTEGER NULL,
          CONSTRAINT "FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProcessPermissionId" FOREIGN KEY ("WorkProcessPermissionId") REFERENCES "swf_workProcessPermission" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_node" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_node" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Auto" INTEGER NOT NULL,
          "NextNodeId" INTEGER NULL,
          "WorkProcessId" INTEGER NULL,
          "Position" TEXT NULL,
          "Discriminator" TEXT NOT NULL,
          "MatchDescription" TEXT NULL,
          -- ִ�нű�
          "Script" text NULL,
          "ElseNodeId" INTEGER NULL,
          "ElseDescription" TEXT NULL,
          -- ִ�нű�
          "ConditionDecision_Script" text NULL,
          "NextTaskPerformers" INTEGER NULL,
          "AssignerId" INTEGER NULL,
          "CanGiveUp" INTEGER NULL,
          "FormDesign" text NULL,
          "WorkActivityCompleteScriptId" INTEGER NULL,
          "WorkTaskCompleteScriptId" INTEGER NULL,
          "WorkTaskStartScriptId" INTEGER NULL,
          "SuggestionComment" TEXT NULL,
          "ExtendInfo" text NULL,
          CONSTRAINT "FK_swf_node_swf_assigner_AssignerId" FOREIGN KEY ("AssignerId") REFERENCES "swf_assigner" ("Id"),
          CONSTRAINT "FK_swf_node_swf_node_ElseNodeId" FOREIGN KEY ("ElseNodeId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_node_swf_node_NextNodeId" FOREIGN KEY ("NextNodeId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_node_swf_scripts_WorkActivityCompleteScriptId" FOREIGN KEY ("WorkActivityCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskCompleteScriptId" FOREIGN KEY ("WorkTaskCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskStartScriptId" FOREIGN KEY ("WorkTaskStartScriptId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_node_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id") ON DELETE SET NULL
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_node" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_node" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Auto" INTEGER NOT NULL,
          "NextNodeId" INTEGER NULL,
          "WorkProcessId" INTEGER NULL,
          "Position" TEXT NULL,
          "Discriminator" TEXT NOT NULL,
          "MatchDescription" TEXT NULL,
          -- ִ�нű�
          "Script" text NULL,
          "ElseNodeId" INTEGER NULL,
          "ElseDescription" TEXT NULL,
          -- ִ�нű�
          "ConditionDecision_Script" text NULL,
          "NextTaskPerformers" INTEGER NULL,
          "AssignerId" INTEGER NULL,
          "CanGiveUp" INTEGER NULL,
          "FormDesign" text NULL,
          "WorkActivityCompleteScriptId" INTEGER NULL,
          "WorkTaskCompleteScriptId" INTEGER NULL,
          "WorkTaskStartScriptId" INTEGER NULL,
          "SuggestionComment" TEXT NULL,
          "ExtendInfo" text NULL,
          CONSTRAINT "FK_swf_node_swf_assigner_AssignerId" FOREIGN KEY ("AssignerId") REFERENCES "swf_assigner" ("Id"),
          CONSTRAINT "FK_swf_node_swf_node_ElseNodeId" FOREIGN KEY ("ElseNodeId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_node_swf_node_NextNodeId" FOREIGN KEY ("NextNodeId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_node_swf_scripts_WorkActivityCompleteScriptId" FOREIGN KEY ("WorkActivityCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskCompleteScriptId" FOREIGN KEY ("WorkTaskCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskStartScriptId" FOREIGN KEY ("WorkTaskStartScriptId") REFERENCES "swf_scripts" ("Id"),
          CONSTRAINT "FK_swf_node_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id") ON DELETE SET NULL
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_processInstance" (
          -- ������ʵ��
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_processInstance" PRIMARY KEY AUTOINCREMENT,
          -- �Ƿ���debugģʽ
          "IsDebug" INTEGER NOT NULL,
          -- �Ƿ��Ѿ�ɾ��
          "IsDelete" INTEGER NOT NULL,
          "WorkActivityCount" INTEGER NOT NULL,
          "WorkProcessId" INTEGER NULL,
          "Priority" INTEGER NOT NULL DEFAULT 100,
          "Subject" TEXT NULL,
          "Creator" TEXT NULL,
          "Status" INTEGER NOT NULL,
          "FinishTime" TEXT NULL,
          "CreateTime" TEXT NOT NULL,
          "StartTime" TEXT NULL,
          "Number" TEXT NOT NULL,
          "SuspendTime" TEXT NULL,
          "SuspendComment" TEXT NULL,
          "Comment" TEXT NULL,
          "Form" json NULL,
          "CreatorName" TEXT NULL,
          CONSTRAINT "FK_swf_processInstance_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_processInstance" (
          -- ������ʵ��
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_processInstance" PRIMARY KEY AUTOINCREMENT,
          -- �Ƿ���debugģʽ
          "IsDebug" INTEGER NOT NULL,
          -- �Ƿ��Ѿ�ɾ��
          "IsDelete" INTEGER NOT NULL,
          "WorkActivityCount" INTEGER NOT NULL,
          "WorkProcessId" INTEGER NULL,
          "Priority" INTEGER NOT NULL DEFAULT 100,
          "Subject" TEXT NULL,
          "Creator" TEXT NULL,
          "Status" INTEGER NOT NULL,
          "FinishTime" TEXT NULL,
          "CreateTime" TEXT NOT NULL,
          "StartTime" TEXT NULL,
          "Number" TEXT NOT NULL,
          "SuspendTime" TEXT NULL,
          "SuspendComment" TEXT NULL,
          "Comment" TEXT NULL,
          "Form" json NULL,
          "CreatorName" TEXT NULL,
          CONSTRAINT "FK_swf_processInstance_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_condition_decision" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_condition_decision" PRIMARY KEY AUTOINCREMENT,
          "NodeId" INTEGER NULL,
          -- ƥ����ַ���
          "MatchValue" TEXT NULL,
          "Description" TEXT NULL,
          "ConditionDecisionId" INTEGER NULL,
          CONSTRAINT "FK_swf_condition_decision_swf_node_ConditionDecisionId" FOREIGN KEY ("ConditionDecisionId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_condition_decision_swf_node_NodeId" FOREIGN KEY ("NodeId") REFERENCES "swf_node" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_condition_decision" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_condition_decision" PRIMARY KEY AUTOINCREMENT,
          "NodeId" INTEGER NULL,
          -- ƥ����ַ���
          "MatchValue" TEXT NULL,
          "Description" TEXT NULL,
          "ConditionDecisionId" INTEGER NULL,
          CONSTRAINT "FK_swf_condition_decision_swf_node_ConditionDecisionId" FOREIGN KEY ("ConditionDecisionId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_condition_decision_swf_node_NodeId" FOREIGN KEY ("NodeId") REFERENCES "swf_node" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_WorkTaskCommand" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_WorkTaskCommand" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Order" INTEGER NOT NULL,
          "Discriminator" TEXT NOT NULL,
          "WorkTaskId" INTEGER NULL,
          "Script" text NULL,
          CONSTRAINT "FK_swf_WorkTaskCommand_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_WorkTaskCommand" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_WorkTaskCommand" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NULL,
          "Order" INTEGER NOT NULL,
          "Discriminator" TEXT NOT NULL,
          "WorkTaskId" INTEGER NULL,
          "Script" text NULL,
          CONSTRAINT "FK_swf_WorkTaskCommand_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_files" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_files" PRIMARY KEY AUTOINCREMENT,
          "FileName" TEXT NULL,
          "FileId" TEXT NULL,
          "FileType" INTEGER NOT NULL,
          "CreateUser" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          CONSTRAINT "FK_swf_files_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_files" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_files" PRIMARY KEY AUTOINCREMENT,
          "FileName" TEXT NULL,
          "FileId" TEXT NULL,
          "FileType" INTEGER NOT NULL,
          "CreateUser" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          CONSTRAINT "FK_swf_files_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PI_Distribution" (
          -- �������̷ַ���¼
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_Distribution" PRIMARY KEY AUTOINCREMENT,
          "ReadTime" TEXT NULL,
          "CreateTime" TEXT NOT NULL,
          "UserName" TEXT NULL,
          "UserRealName" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          "HasRead" INTEGER NOT NULL,
          CONSTRAINT "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PI_Distribution" (
          -- �������̷ַ���¼
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_Distribution" PRIMARY KEY AUTOINCREMENT,
          "ReadTime" TEXT NULL,
          "CreateTime" TEXT NOT NULL,
          "UserName" TEXT NULL,
          "UserRealName" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          "HasRead" INTEGER NOT NULL,
          CONSTRAINT "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PI_ProcessInstanceTags" (
          -- �������̱��
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_ProcessInstanceTags" PRIMARY KEY AUTOINCREMENT,
          "TagId" INTEGER NULL,
          "Color" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          "CanDelete" INTEGER NOT NULL,
          CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId" FOREIGN KEY ("TagId") REFERENCES "swf_PI_Tags" ("Id"),
          CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_PI_ProcessInstanceTags" (
          -- �������̱��
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_PI_ProcessInstanceTags" PRIMARY KEY AUTOINCREMENT,
          "TagId" INTEGER NULL,
          "Color" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          "CanDelete" INTEGER NOT NULL,
          CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId" FOREIGN KEY ("TagId") REFERENCES "swf_PI_Tags" ("Id"),
          CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_WorkActivity" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_WorkActivity" PRIMARY KEY AUTOINCREMENT,
          "Priority" INTEGER NOT NULL DEFAULT 100,
          "WorkTaskId" INTEGER NULL,
          "AssignPerformers" TEXT NULL,
          "Status" INTEGER NOT NULL,
          "AssignTime" TEXT NULL,
          "TaskCreatingGroup" TEXT NULL,
          "DisposeUser" TEXT NULL,
          "DisposeUserName" TEXT NULL,
          "DisposeTime" TEXT NULL,
          "CreateTime" TEXT NOT NULL,
          "Command" TEXT NULL,
          "Comment" TEXT NULL,
          "TimeSpan" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          CONSTRAINT "FK_swf_WorkActivity_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE TABLE "swf_WorkActivity" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_swf_WorkActivity" PRIMARY KEY AUTOINCREMENT,
          "Priority" INTEGER NOT NULL DEFAULT 100,
          "WorkTaskId" INTEGER NULL,
          "AssignPerformers" TEXT NULL,
          "Status" INTEGER NOT NULL,
          "AssignTime" TEXT NULL,
          "TaskCreatingGroup" TEXT NULL,
          "DisposeUser" TEXT NULL,
          "DisposeUserName" TEXT NULL,
          "DisposeTime" TEXT NULL,
          "CreateTime" TEXT NOT NULL,
          "Command" TEXT NULL,
          "Comment" TEXT NULL,
          "TimeSpan" TEXT NULL,
          "ProcessInstanceId" INTEGER NULL,
          CONSTRAINT "FK_swf_WorkActivity_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id"),
          CONSTRAINT "FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
      );
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_condition_decision_ConditionDecisionId" ON "swf_condition_decision" ("ConditionDecisionId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_condition_decision_ConditionDecisionId" ON "swf_condition_decision" ("ConditionDecisionId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_condition_decision_NodeId" ON "swf_condition_decision" ("NodeId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_condition_decision_NodeId" ON "swf_condition_decision" ("NodeId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_files_ProcessInstanceId" ON "swf_files" ("ProcessInstanceId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_files_ProcessInstanceId" ON "swf_files" ("ProcessInstanceId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_AssignerId" ON "swf_node" ("AssignerId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_AssignerId" ON "swf_node" ("AssignerId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_ElseNodeId" ON "swf_node" ("ElseNodeId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_ElseNodeId" ON "swf_node" ("ElseNodeId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_NextNodeId" ON "swf_node" ("NextNodeId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_NextNodeId" ON "swf_node" ("NextNodeId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkActivityCompleteScriptId" ON "swf_node" ("WorkActivityCompleteScriptId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkActivityCompleteScriptId" ON "swf_node" ("WorkActivityCompleteScriptId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkProcessId" ON "swf_node" ("WorkProcessId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkProcessId" ON "swf_node" ("WorkProcessId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkTaskCompleteScriptId" ON "swf_node" ("WorkTaskCompleteScriptId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkTaskCompleteScriptId" ON "swf_node" ("WorkTaskCompleteScriptId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkTaskStartScriptId" ON "swf_node" ("WorkTaskStartScriptId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_node_WorkTaskStartScriptId" ON "swf_node" ("WorkTaskStartScriptId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PermissionPerformer_WorkProcessPermissionId" ON "swf_PermissionPerformer" ("WorkProcessPermissionId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PermissionPerformer_WorkProcessPermissionId" ON "swf_PermissionPerformer" ("WorkProcessPermissionId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PI_Distribution_ProcessInstanceId" ON "swf_PI_Distribution" ("ProcessInstanceId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PI_Distribution_ProcessInstanceId" ON "swf_PI_Distribution" ("ProcessInstanceId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PI_ProcessInstanceTags_ProcessInstanceId" ON "swf_PI_ProcessInstanceTags" ("ProcessInstanceId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PI_ProcessInstanceTags_ProcessInstanceId" ON "swf_PI_ProcessInstanceTags" ("ProcessInstanceId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PI_ProcessInstanceTags_TagId" ON "swf_PI_ProcessInstanceTags" ("TagId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_PI_ProcessInstanceTags_TagId" ON "swf_PI_ProcessInstanceTags" ("TagId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_processInstance_Number" ON "swf_processInstance" ("Number");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_processInstance_Number" ON "swf_processInstance" ("Number");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_processInstance_WorkProcessId" ON "swf_processInstance" ("WorkProcessId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_processInstance_WorkProcessId" ON "swf_processInstance" ("WorkProcessId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_WorkActivity_ProcessInstanceId" ON "swf_WorkActivity" ("ProcessInstanceId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_WorkActivity_ProcessInstanceId" ON "swf_WorkActivity" ("ProcessInstanceId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_WorkActivity_WorkTaskId" ON "swf_WorkActivity" ("WorkTaskId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_WorkActivity_WorkTaskId" ON "swf_WorkActivity" ("WorkTaskId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workflowLog_ProcessInstanceNumber" ON "swf_workflowLog" ("ProcessInstanceNumber");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workflowLog_ProcessInstanceNumber" ON "swf_workflowLog" ("ProcessInstanceNumber");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE UNIQUE INDEX "IX_swf_workProcess_Name_Version" ON "swf_workProcess" ("Name", "Version");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE UNIQUE INDEX "IX_swf_workProcess_Name_Version" ON "swf_workProcess" ("Name", "Version");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcess_OnCancelId" ON "swf_workProcess" ("OnCancelId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcess_OnCancelId" ON "swf_workProcess" ("OnCancelId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcess_OnCompleteId" ON "swf_workProcess" ("OnCompleteId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcess_OnCompleteId" ON "swf_workProcess" ("OnCompleteId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcess_OnStartId" ON "swf_workProcess" ("OnStartId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcess_OnStartId" ON "swf_workProcess" ("OnStartId");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcessPermission_ProcessName" ON "swf_workProcessPermission" ("ProcessName");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_workProcessPermission_ProcessName" ON "swf_workProcessPermission" ("ProcessName");
dbug: Microsoft.EntityFrameworkCore.Database.Command[20103]
      Creating DbCommand for 'ExecuteNonQuery'.
dbug: Microsoft.EntityFrameworkCore.Database.Command[20104]
      Created DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20106]
      Initialized DbCommand for 'ExecuteNonQuery' (0ms).
dbug: Microsoft.EntityFrameworkCore.Database.Command[20100]
      Executing DbCommand [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_WorkTaskCommand_WorkTaskId" ON "swf_WorkTaskCommand" ("WorkTaskId");
info: Microsoft.EntityFrameworkCore.Database.Command[20101]
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='300']
      CREATE INDEX "IX_swf_WorkTaskCommand_WorkTaskId" ON "swf_WorkTaskCommand" ("WorkTaskId");
dbug: Microsoft.EntityFrameworkCore.Database.Transaction[20210]
      Committing transaction.
dbug: Microsoft.EntityFrameworkCore.Database.Transaction[20202]
      Committed transaction.
dbug: Microsoft.EntityFrameworkCore.Database.Transaction[20204]
      Disposing transaction.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20002]
      Closing connection to database 'main' on server 'D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\bin\Debug\net8.0\_data\database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20003]
      Closed connection to database 'main' on server '_data/database.db' (0ms).
dbug: Microsoft.EntityFrameworkCore.Infrastructure[10407]
      'ApplicationDbContext' disposed.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20007]
      Disposing connection to database 'main' on server '_data/database.db'.
dbug: Microsoft.EntityFrameworkCore.Database.Connection[20008]
      Disposed connection to database 'main' on server '_data/database.db' (0ms).
info: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/1.1 GET http://localhost/JsHttpClient/list-json?a=string&amp;b=11&amp;c=1.2&amp;d=1&amp;d=2&amp;d=3&amp;d=4 - - -
dbug: Microsoft.AspNetCore.HostFiltering.HostFilteringMiddleware[0]
      Wildcard detected, all requests with hosts will be allowed.
dbug: Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware[3]
      The request path  does not match the path filter
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1001]
      1 candidate(s) found for the request path '/JsHttpClient/list-json'
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1005]
      Endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.ListJson (Coder.ScriptWorkflow.WebApi)' with route pattern 'JsHttpClient/list-json' is valid for the request path '/JsHttpClient/list-json'
dbug: Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware[1]
      Request matched endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.ListJson (Coder.ScriptWorkflow.WebApi)'
dbug: Coder.Authentication.CoderAuthenticationHandler[0]
      ��http://localhost/JsHttpClient/list-json?a=string&amp;b=11&amp;c=1.2&amp;d=1&amp;d=2&amp;d=3&amp;d=4query[access_token]��ȡ,ֵ:
dbug: Coder.Authentication.CoderAuthenticationHandler[9]
      AuthenticationScheme: Coder was not authenticated.
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.ListJson (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[102]
      Route matched with {action = "ListJson", controller = "JsHttpClient"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult ListJson() on controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi).
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of authorization filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of resource filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of action filters (in the following order): Microsoft.AspNetCore.Mvc.Filters.ControllerActionFilter (Order: -2147483648), Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000), Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of exception filters (in the following order): Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute (Order: 0)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of result filters (in the following order): Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Executing controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[2]
      Executed controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[11]
      List of registered output formatters, in the following order: Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[4]
      No information found on request to perform content negotiation.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[8]
      Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[10]
      Attempting to select the first formatter in the output formatters list which can write the result.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[2]
      Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter' and content type 'application/json' to write the response.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor[1]
      Executing OkObjectResult, writing value of type '&lt;&gt;f__AnonymousType3`5[[Microsoft.Extensions.Primitives.StringValues, Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60],[Microsoft.Extensions.Primitives.StringValues, Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60],[Microsoft.Extensions.Primitives.StringValues, Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[Microsoft.Extensions.Primitives.StringValues, Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]'.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[105]
      Executed action Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.ListJson (Coder.ScriptWorkflow.WebApi) in 30.4844ms
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.ListJson (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/1.1 GET http://localhost/JsHttpClient/list-json?a=string&amp;b=11&amp;c=1.2&amp;d=1&amp;d=2&amp;d=3&amp;d=4 - 200 108 application/json;+charset=utf-8 159.0779ms
info: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/1.1 POST http://localhost/JsHttpClient/PostJSON - application/json;+charset=utf-8 41
dbug: Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware[1]
      POST requests are not supported
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1001]
      1 candidate(s) found for the request path '/JsHttpClient/PostJSON'
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1005]
      Endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostJSON (Coder.ScriptWorkflow.WebApi)' with route pattern 'JsHttpClient/PostJSON' is valid for the request path '/JsHttpClient/PostJSON'
dbug: Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware[1]
      Request matched endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostJSON (Coder.ScriptWorkflow.WebApi)'
dbug: Coder.Authentication.CoderAuthenticationHandler[0]
      ��http://localhost/JsHttpClient/PostJSONquery[access_token]��ȡ,ֵ:
dbug: Coder.Authentication.CoderAuthenticationHandler[9]
      AuthenticationScheme: Coder was not authenticated.
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostJSON (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[102]
      Route matched with {action = "PostJSON", controller = "JsHttpClient"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PostJSON(Newtonsoft.Json.Linq.JObject) on controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi).
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of authorization filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of resource filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of action filters (in the following order): Microsoft.AspNetCore.Mvc.Filters.ControllerActionFilter (Order: -2147483648), Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000), Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of exception filters (in the following order): Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute (Order: 0)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of result filters (in the following order): Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Executing controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[2]
      Executed controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[22]
      Attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject' ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[44]
      Attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject' using the name '' in request data ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[2]
      Rejected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonPatchInputFormatter' for content type 'application/json; charset=utf-8'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[1]
      Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonInputFormatter' for content type 'application/json; charset=utf-8'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[45]
      Done attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[23]
      Done attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[26]
      Attempting to validate the bound parameter 'form' of type 'Newtonsoft.Json.Linq.JObject' ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[27]
      Done attempting to validate the bound parameter 'form' of type 'Newtonsoft.Json.Linq.JObject'.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[11]
      List of registered output formatters, in the following order: Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[4]
      No information found on request to perform content negotiation.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[8]
      Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[10]
      Attempting to select the first formatter in the output formatters list which can write the result.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[2]
      Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter' and content type 'application/json' to write the response.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor[1]
      Executing OkObjectResult, writing value of type 'Newtonsoft.Json.Linq.JObject'.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[105]
      Executed action Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostJSON (Coder.ScriptWorkflow.WebApi) in 25.9942ms
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostJSON (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/1.1 POST http://localhost/JsHttpClient/PostJSON - 200 41 application/json;+charset=utf-8 38.4777ms
info: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/1.1 DELETE http://localhost/JsHttpClient/1 - - -
dbug: Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware[1]
      DELETE requests are not supported
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1001]
      1 candidate(s) found for the request path '/JsHttpClient/1'
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1005]
      Endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.JsonDelete (Coder.ScriptWorkflow.WebApi)' with route pattern 'JsHttpClient/{id}' is valid for the request path '/JsHttpClient/1'
dbug: Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware[1]
      Request matched endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.JsonDelete (Coder.ScriptWorkflow.WebApi)'
dbug: Coder.Authentication.CoderAuthenticationHandler[0]
      ��http://localhost/JsHttpClient/1query[access_token]��ȡ,ֵ:
dbug: Coder.Authentication.CoderAuthenticationHandler[9]
      AuthenticationScheme: Coder was not authenticated.
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.JsonDelete (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[102]
      Route matched with {action = "JsonDelete", controller = "JsHttpClient"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult JsonDelete(Int32) on controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi).
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of authorization filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of resource filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of action filters (in the following order): Microsoft.AspNetCore.Mvc.Filters.ControllerActionFilter (Order: -2147483648), Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000), Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of exception filters (in the following order): Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute (Order: 0)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of result filters (in the following order): Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Executing controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[2]
      Executed controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[22]
      Attempting to bind parameter 'id' of type 'System.Int32' ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder[44]
      Attempting to bind parameter 'id' of type 'System.Int32' using the name 'id' in request data ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder[45]
      Done attempting to bind parameter 'id' of type 'System.Int32'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[23]
      Done attempting to bind parameter 'id' of type 'System.Int32'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[26]
      Attempting to validate the bound parameter 'id' of type 'System.Int32' ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[27]
      Done attempting to validate the bound parameter 'id' of type 'System.Int32'.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[11]
      List of registered output formatters, in the following order: Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[4]
      No information found on request to perform content negotiation.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[8]
      Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[10]
      Attempting to select the first formatter in the output formatters list which can write the result.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[2]
      Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter' and content type 'application/json' to write the response.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor[1]
      Executing OkObjectResult, writing value of type '&lt;&gt;f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[105]
      Executed action Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.JsonDelete (Coder.ScriptWorkflow.WebApi) in 2.7243ms
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.JsonDelete (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/1.1 DELETE http://localhost/JsHttpClient/1 - 200 36 application/json;+charset=utf-8 7.7176ms
info: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/1.1 PUT http://localhost/JsHttpClient/PutJSON - application/json;+charset=utf-8 29
dbug: Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware[1]
      PUT requests are not supported
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1001]
      1 candidate(s) found for the request path '/JsHttpClient/PutJSON'
dbug: Microsoft.AspNetCore.Routing.Matching.DfaMatcher[1005]
      Endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PutJson (Coder.ScriptWorkflow.WebApi)' with route pattern 'JsHttpClient/PutJSON' is valid for the request path '/JsHttpClient/PutJSON'
dbug: Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware[1]
      Request matched endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PutJson (Coder.ScriptWorkflow.WebApi)'
dbug: Coder.Authentication.CoderAuthenticationHandler[0]
      ��http://localhost/JsHttpClient/PutJSONquery[access_token]��ȡ,ֵ:
dbug: Coder.Authentication.CoderAuthenticationHandler[9]
      AuthenticationScheme: Coder was not authenticated.
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PutJson (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[102]
      Route matched with {action = "PutJson", controller = "JsHttpClient"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PutJson(Newtonsoft.Json.Linq.JObject) on controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi).
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of authorization filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of resource filters (in the following order): None
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of action filters (in the following order): Microsoft.AspNetCore.Mvc.Filters.ControllerActionFilter (Order: -2147483648), Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000), Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of exception filters (in the following order): Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute (Order: 0)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Execution plan of result filters (in the following order): Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[1]
      Executing controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[2]
      Executed controller factory for controller Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController (Coder.ScriptWorkflow.WebApi)
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[22]
      Attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject' ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[44]
      Attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject' using the name '' in request data ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[2]
      Rejected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonPatchInputFormatter' for content type 'application/json; charset=utf-8'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[1]
      Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonInputFormatter' for content type 'application/json; charset=utf-8'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinder[45]
      Done attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[23]
      Done attempting to bind parameter 'form' of type 'Newtonsoft.Json.Linq.JObject'.
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[26]
      Attempting to validate the bound parameter 'form' of type 'Newtonsoft.Json.Linq.JObject' ...
dbug: Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder[27]
      Done attempting to validate the bound parameter 'form' of type 'Newtonsoft.Json.Linq.JObject'.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[11]
      List of registered output formatters, in the following order: Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter, Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[4]
      No information found on request to perform content negotiation.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[8]
      Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[10]
      Attempting to select the first formatter in the output formatters list which can write the result.
dbug: Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector[2]
      Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter' and content type 'application/json' to write the response.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor[1]
      Executing OkObjectResult, writing value of type 'Newtonsoft.Json.Linq.JObject'.
info: Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker[105]
      Executed action Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PutJson (Coder.ScriptWorkflow.WebApi) in 1.3011ms
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'Coder.ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PutJson (Coder.ScriptWorkflow.WebApi)'
info: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/1.1 PUT http://localhost/JsHttpClient/PutJSON - 200 29 application/json;+charset=utf-8 2.5849ms
dbug: Microsoft.AspNetCore.Hosting.Diagnostics[5]
      Hosting shutdown
Info:Script, ����,����
Info:Script, ����,����
[xUnit.net 00:00:02.42]       System.ArgumentException : 抛出服务器错误哦.1
[xUnit.net 00:00:02.42]       Stack Trace:
[xUnit.net 00:00:02.42]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs(489,0): at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.ErrorPlugin.Execute(Int32 a)
[xUnit.net 00:00:02.42]            at Execute(Closure, Object, Context, Expression[], Arguments)
[xUnit.net 00:00:02.42]            at NiL.JS.Core.Functions.MethodProxy.invokeMethod(JSValue targetValue, Expression[] argumentsSource, Arguments argumentsObject, Context initiator)
[xUnit.net 00:00:02.42]            at NiL.JS.Core.Functions.MethodProxy.InternalInvoke(JSValue targetValue, Expression[] argumentsSource, Context initiator, Boolean withSpread, Boolean withNew)
[xUnit.net 00:00:02.42]            at NiL.JS.Expressions.Call.Evaluate(Context context)
[xUnit.net 00:00:02.42]            at NiL.JS.Statements.CodeBlock.evaluateLines(Context context, Int32 i, Boolean clearSuspendData)
[xUnit.net 00:00:02.42]            at NiL.JS.Statements.CodeBlock.Evaluate(Context context)
[xUnit.net 00:00:02.42]            at NiL.JS.Core.Context.doEval(CodeBlock body, Context context)
[xUnit.net 00:00:02.42]            at NiL.JS.Core.Context.Eval(String sourceCode, JSValue thisBind, Boolean suppressScopeCreation)
[xUnit.net 00:00:02.42]            at NiL.JS.Core.Context.Eval(String code, Boolean suppressScopeCreation)
[xUnit.net 00:00:02.42]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Core\Scripts\WorkProcessScript.cs(32,0): at Coder.ScriptWorkflow.Scripts.WorkProcessScript.Invoke(IWorkflowContext workflowContext, String workProcessEvents)
[xUnit.net 00:00:02.42]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Core\WorkflowManager.cs(432,0): at Coder.ScriptWorkflow.WorkflowManager.Start(ProcessInstance instance, UserViewModel currentUser)
[xUnit.net 00:00:02.42]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.Core\WorkflowManager.cs(400,0): at Coder.ScriptWorkflow.WorkflowManager.Start(Int32 processInstanceId, UserViewModel currentUser)
[xUnit.net 00:00:02.42]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs(230,0): at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.OnStartServerError()
[xUnit.net 00:00:02.42]            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
[xUnit.net 00:00:02.42]            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
Info:Script, 345600000
Info:Script, ����,����
Info:Script, ����,����
Info:Script, ���ܲ���true
Info:Script, ����,����
Info:Script, ����,����
Info:Script, ����,����
Info:Script, ����,����
Info:Script, ����,����
[xUnit.net 00:00:02.82]       Assert.Equal() Failure: Strings differ
[xUnit.net 00:00:02.82]       Expected: ···"Variable "Json" is not defined at (1:1*4)"
[xUnit.net 00:00:02.82]       Actual:   ···" is not defined at (1:1*4),codeLine:1/1/4"
[xUnit.net 00:00:02.82]                                               ↑ (pos 84)
[xUnit.net 00:00:02.82]       Stack Trace:
[xUnit.net 00:00:02.82]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs(192,0): at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.OnStartError()
[xUnit.net 00:00:02.82]            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
[xUnit.net 00:00:02.82]            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
Info:Script, ����,����
Info:Script, ����,����
Info:Script, ����,����
Info:Script, ����,����
[xUnit.net 00:00:03.06]       Assert.Single() Failure: The collection was empty
[xUnit.net 00:00:03.06]       Stack Trace:
[xUnit.net 00:00:03.06]         D:\projects\ScriptWorkflow\script-workflow-2.0\src\Coder.ScriptWorkflow.UnitTest\WorkflowManagers\WorkflowManagerTest.cs(424,0): at Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.TestPerformerSearcherInTags()
[xUnit.net 00:00:03.06]            at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
[xUnit.net 00:00:03.06]            at System.Reflection.MethodBaseInvoker.InvokeWithNoArgs(Object obj, BindingFlags invokeAttr)
[xUnit.net 00:00:03.06]   Finished:    Coder.ScriptWorkflow.UnitTest
</system-out>
    <system-err>Error - [xUnit.net 00:00:00.27]     Coder.ScriptWorkflow.UnitTest.AssignUserTests.Equals_ShouldReturnFalse_WhenObjIsNotAssignUser [FAIL]
Error - [xUnit.net 00:00:00.54]     Coder.ScriptWorkflow.UnitTest.ScriptDecisionTest.ScriptError [FAIL]
Error - [xUnit.net 00:00:00.58]     Coder.ScriptWorkflow.UnitTest.WorkflowContextTest.TestJsonDateFormat [FAIL]
Error - [xUnit.net 00:00:02.42]     Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.OnStartServerError [FAIL]
Error - [xUnit.net 00:00:02.82]     ONSwtart js语法错误。 [FAIL]
Error - [xUnit.net 00:00:03.06]     Coder.ScriptWorkflow.UnitTest.WorkflowManagers.WorkflowManagerTest.TestPerformerSearcherInTags [FAIL]
</system-err>
  </testsuite>
</testsuites>