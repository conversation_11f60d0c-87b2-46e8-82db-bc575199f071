﻿using System;


namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
/// </summary>
public class AcceptResult
{
    /// <summary>
    /// </summary>
    public string DisposeUser { get; set; }

    /// <summary>
    /// </summary>
    public DateTime? AssignTime { get; set; }

    /// <summary>
    /// </summary>
    public WorkActivityStatus Status { get; set; }

    public string Message { get; set; }

    public bool Success { get; set; }
}
