﻿using coder.car.service.Clients;
using coder.car.service.ViewModels.Repair;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsRepaidCarClient
{
    public class RepaidCarHttpClient
    {
        private readonly IRepairCarHttpClient _client;
        private readonly IWorkflowContext _context;
        public RepaidCarHttpClient(IRepairCarHttpClient client, IWorkflowContext context)
        {
            _client = client;
            _context = context;

        }

        public int Save() {
            var submit = new RepairSubmit();
            var json = JsonConvert.DeserializeObject
           <JObject>(_context.ProcessInstance.Form);

            if (json.TryGetValue("repairPrice", out var repairPriceToken))//维修报价
                submit.RepairCost = repairPriceToken.Value<decimal>();
            if (json.TryGetValue("repairInfo", out var repairInfoToken))//维修项目
                submit.RepairItem = repairInfoToken.Value<string>();
            if (json.TryGetValue("repairDate", out var repairDateToken))//申请日期
                submit.RepairDate = repairDateToken.Value<DateTime>();
            if (json.TryGetValue("carProblem", out var carProblemToken))//修理厂故障
                submit.RepairDescription = carProblemToken.Value<string>();
            if (json.TryGetValue("carInfo", out var carInfoToken))//车辆信息
                submit.RepairPerson = carInfoToken.Value<string>();
            var r = _client.SaveAsync(submit).Result;
            return r.Id;
        }
    }
}
