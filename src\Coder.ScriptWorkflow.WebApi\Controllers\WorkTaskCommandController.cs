﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.Member.Clients;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     与前端 Command 按钮 交互的时候采用。
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
public class WorkTaskCommandController : Controller
{
    private readonly IWorkActivityStore _activityStore;
    private readonly WorkflowPermissionManager _permissionManager;

    private readonly WorkflowManager _workflowManager;

    /// <summary>
    /// </summary>
    /// <param name="workflowManager"></param>
    /// <param name="activityStore"></param>
    public WorkTaskCommandController(WorkflowManager workflowManager, IWorkActivityStore activityStore, WorkflowPermissionManager permissionManager)
    {
        _workflowManager = workflowManager;
        _activityStore = activityStore;
        _permissionManager = permissionManager;
    }

    /// <summary>
    ///     自由节点命令。
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <param name="command"></param>
    /// <param name="workActivityStore"></param>
    /// <returns></returns>
    [HttpGet("previous-workTask-select/{workActivityId}/{command}")]
    public IActionResult GetPreviousWorkTask([FromRoute] int workActivityId, [FromRoute] string command, [FromServices] IWorkActivityStore workActivityStore)
    {
        var wa = _activityStore.GetById(workActivityId);

        if (!_permissionManager.HasReadPermissionAsync(wa, this.User).Result)
            return Forbid();

        var wtCommand = wa.WorkTask.GetCommand<PreviousWorkTaskCommand>(command);

        var task = wtCommand.GetPreviousWorkTasks(wa, workActivityStore);

        return Ok(task.Select(s => new
        {
            name = s
        }).ToSuccess());
    }

    /// <summary>
    ///     获取下一步的执行人。
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <param name="submit"></param>
    /// <param name="performerManager"></param>
    /// <returns></returns>
    [HttpPost("show-next-users/{workActivityId}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ShowNextWorkTaskResult>))]
    public async Task<IActionResult> TryResolve([FromRoute] int workActivityId,
        [FromBody] WorkflowResolveSubmit submit,
        [FromServices] PerformerManager performerManager, [FromServices] ILogger<WorkTaskCommandController> logger, [FromServices]IUserClient userClient)
    {
        var currentUser = this.User.ToDebuggerUser(userClient, submit.ResolveUser);
        try
        {
            var workActivity = _workflowManager.GetWorkActivityById(workActivityId);
            _permissionManager.CanResolve(workActivity, currentUser, out var vm);

            if (!vm.CanDisposeWorkActivity)
                return Ok(new ResolveResult
                {
                    Success = false,
                    Message = "工作活动已经被处理或你无权处理。"
                });

            var resolveResult = _workflowManager.Resolve(workActivityId, submit, currentUser.ToUserViewModel(), true);
            //执行错误
            if (!resolveResult.Success)
                return Ok(new { success = false, message = "不满足条件，无法获取。原因:" + resolveResult.Message }.ToSuccess());


            var workTaskName = "结束";
            if (!resolveResult.IsEnd && resolveResult.WorkActivities.Any()) workTaskName = resolveResult.WorkActivities.First().WorkTaskName;

         
            var result = new ShowNextWorkTaskResult
            {
                AssignScopeType = resolveResult.AssignScopeType,
                WorkTaskName = workTaskName,
                IsEnd = resolveResult.IsEnd,
                Success = resolveResult.Success,
                WorkTaskIsEnd = resolveResult.WorkActivities.Any() //如果工作活动是否结束。WorkActivities。
            };
             var searchPerformer=new List<Performer>();
            //需按照所有符合要求的人。
            foreach (var wa in resolveResult.WorkActivities)
                if (wa.DisposeUser != null) //工作活动已经具体到某个人，所以就直接选上
                {
                    result.AddWorkActivity(new AssignUser
                    {
                        Name = wa.DisposeUserName,
                        UserName = wa.DisposeUser,
                        Default = true
                    });
                }
                else
                {
                    searchPerformer.AddRange(wa.AssignPerformers);
             
                   
                }

            if (searchPerformer.Any())
            {
                var usersResult = await performerManager.FindByPerformerAsync(searchPerformer);
                result.AddWorkActivity(usersResult.ToArray());
            }

            if (!result.AssignUsers.Any() && resolveResult.WorkActivities.Any()) //工作活动已经产生，但是从用户系统中，没有任何用户
            {
                result.Success = false;
                result.Message = "没有任何执行用户。请确认用户系统是否设置正确.";
            }
            else
            {
                result.Success = true;
                result.Message = resolveResult.Message;
            }

            return Ok(result.ToSuccess());
        }
        catch (WorkflowDefinedException ex)
        {
            var data = new
            {
                success = false,
                message = ex.Message
            };
            return Ok(data.ToError("设置错误:" + ex.Message, 0));
        }
        catch (WorkflowException ex)
        {
            var data = new
            {
                success = false,
                message = ex.Message,
                StackTrace = ex.StackTrace
            };
            return Ok(data.ToError("执行错误:" + ex.Message, 0));
        }
    }
}