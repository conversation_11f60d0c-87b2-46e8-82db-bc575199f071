﻿using System;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts.ViewModels;

/// <summary>
///     用于脚本中胡ProcessInstance
/// </summary>
public class ScriptProcessInstance
{
    private readonly ProcessInstance _pInstance;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="processInstance"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public ScriptProcessInstance(ProcessInstance processInstance)
    {
        _pInstance = processInstance ?? throw new ArgumentNullException(nameof(processInstance));
        ;
    }

    /// <summary>
    /// </summary>

    public JSValue Form { get; set; }

    /// <summary>
    ///     是否只执行不保存
    /// </summary>
    public bool IsDryRun { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    public NiL.JS.BaseLibrary.Date CreateTime { get; set; }

    /// <summary>
    ///     完成时间
    /// </summary>
    public NiL.JS.BaseLibrary.Date? FinishTime { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    /// </summary>
    public ScriptWorkProcess WorkProcess { get; set; }

    /// <summary>
    ///     创建用户
    /// </summary>
    public string Creator { get; set; }

    /// <summary>
    ///     工单主题
    /// </summary>
    public string Subject
    {
        get => _pInstance.Subject;
        set => _pInstance.Subject = value;
    }
}