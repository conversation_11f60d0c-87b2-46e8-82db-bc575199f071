﻿using Coder.Notification.HttpClients;
using Coder.ScriptWorkflow;
using Coder.ScriptWorkflow.Interceptors;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.VBenNotify.Interceptor;

/// <summary>
/// </summary>
public static class CoderVBenNotifyExtensions
{
    /// <summary>
    /// </summary>
    /// <param name="options"></param>
    /// <param name="host"></param>
    /// <param name="prefix"></param>
    /// <returns></returns>
    public static WorkflowOptions AddCoderVBenNotify(this WorkflowOptions options, string host,
        string prefix = "notify")
    {
        options.Services.AddNotifyHttpClient(host, prefix);
        options.Services.AddScoped<IWorkflowInterceptor, WorkflowInterceptor>();

        return options;
    }
}