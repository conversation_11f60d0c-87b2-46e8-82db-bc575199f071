﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Stores;
using Microsoft.EntityFrameworkCore.Storage;

namespace Coder.ScriptWorkflow;

internal class TransactionManage : ITransactionManage, IAsyncDisposable
{
    private readonly IDbContextTransaction _transaction;
    private bool hasDone = false;

    internal TransactionManage([AllowNull] IDbContextTransaction trans)
    {
        _transaction = trans;
    }


    public void Rollback()
    {
        if(hasDone)return;
        hasDone = true;
        _transaction?.Rollback();
    }

    public void Commit()
    {
        if (hasDone) return;
        hasDone = true;
        _transaction?.Commit();
    }

    public void Dispose()
    {
        _transaction?.Dispose();
    }

    public async ValueTask DisposeAsync()
    {
        if (_transaction != null) await _transaction.DisposeAsync();
    }
}