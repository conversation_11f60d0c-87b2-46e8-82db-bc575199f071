﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<RootNamespace>Coder.ScriptWorkflow</RootNamespace>
		<Authors>珠海酷迪技术有限公司</Authors>
		<Company>珠海酷迪技术有限公司</Company>
		<Copyright>珠海酷迪技术有限公司@2018-2022</Copyright>
		<Version>3.1.1</Version>
		<Product>脚本工作流</Product>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Plugins\**" />
		<EmbeddedResource Remove="Plugins\**" />
		<None Remove="Plugins\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Coder.FileSystem.Abstractions" Version="6.3.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>



	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
		<PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
		
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

</Project>
