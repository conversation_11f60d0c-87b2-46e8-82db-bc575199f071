﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.ProcessInstances;



/// <summary>
///     通过workActivity 提交的数据。
/// </summary>
public class WorkActivityForm : IJsonFormSubmit
{
    /// <summary>
    /// </summary>
    public string WorkActivityComment { get; set; }

    /// <summary>
    ///     优先级别
    /// </summary>
    public Priority Priority { get; set; }

    /// <summary>
    ///     主题。
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public IEnumerable<TagSubmit> Tags { set; get; }

    /// <summary>
    /// </summary>
    public string Form { get; set; }
    /// <summary>
    /// 调试用户
    /// </summary>
    public string SubmitFormUser { get; set; }

    /// <summary>
    /// </summary>
    public JObject FillSelfByJsonForm()
    {
        if (string.IsNullOrWhiteSpace(Form))
            return new JObject();
        var jsonObject = JObject.Parse(Form);
        if (jsonObject.TryGetValue(FormVariableDefines.WorkActivity.Comment, out var jObjectComment))
            WorkActivityComment = jObjectComment.Value<string>();

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Priority, out var jValuePriority))

        {
            var number = Convert.ToInt32(jValuePriority.Value<int>());
            Priority = (Priority)number;
        }

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Tags, out var tags))
        {
            var submitTags = new List<TagSubmit>();
            for (var i = 0; i < tags.Count(); i++)
            {
                var tag = tags[i].ToString();
                submitTags.Add(JsonConvert.DeserializeObject<TagSubmit>(tag));
            }

            Tags = submitTags;
        }

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Subject, out var subject))
        {
            Subject = subject.Value<string>();
            ;
        }

        return jsonObject;
    }


}
public class ResetToWorkTaskResult
{
    /// <summary>
    /// </summary>
    public ResetToWorkTaskResult()
    {
        Success = true;
        Message = "成功获取。";
    }

    public bool Success { get; set; }
    public string Message { get; set; }
}
