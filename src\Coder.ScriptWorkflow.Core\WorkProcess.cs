﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Scripts;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow;

/// <summary>
/// 工作流程定义类，包含工作流的所有配置信息和基本属性
/// </summary>
/// <remarks>
/// 这个类是工作流系统的核心类，定义了一个完整的工作流程，包括：
/// 1. 基本信息（名称、描述、图标等）
/// 2. 表单设计和脚本配置
/// 3. 生命周期事件处理（启动、取消、完成）
/// 4. 插件和扩展信息
/// </remarks>
public class WorkProcess
{
    /// <summary>
    /// 启动事件名称常量
    /// </summary>
    /// <remarks>工作流实例启动时触发的事件名称</remarks>
    internal const string ModuleOnStart = "工作流启动事件";

    /// <summary>
    /// 取消事件名称常量
    /// </summary>
    /// <remarks>工作流实例被取消时触发的事件名称</remarks>
    internal const string ModuleOnCancel = "工作流取消事件";

    /// <summary>
    /// 完成事件名称常量
    /// </summary>
    /// <remarks>工作流实例正常完成时触发的事件名称</remarks>
    internal const string ModuleOnComplete = "工作流完成事件";

    /// <summary>
    /// TypeScript 类型声明代码模板
    /// </summary>
    /// <remarks>
    /// 用于为表单提供 TypeScript 类型定义，支持在脚本环境中进行类型检查
    /// 包含了客户表单类和标准表单类的声明
    /// </remarks>
    private const string DeclareCode = @"

declare class customerFormClass {
  // formClass 继承这个类型,由form自定义进行。
}

declare class formClass extends customerFormClass {
  //请在这里定义结构，请用typescript方式定义
}
";

    /// <summary>
    /// 工作流程扩展信息
    /// </summary>
    /// <remarks>存储与工作流相关的额外配置信息，如菜单ID等</remarks>
    private WorkProcessExtendInfo _extendInfo = new();

    /// <summary>
    /// 私有字段：存储 TypeScript 类型定义
    /// </summary>
    private string _formTypeScriptDefined;
    
    /// <summary>
    /// 私有字段：存储工作流名称
    /// </summary>
    private string _name;

    /// <summary>
    /// 受保护的无参数构造函数
    /// </summary>
    /// <remarks>主要用于ORM框架或序列化场景</remarks>
    protected WorkProcess()
    {
    }

    /// <summary>
    /// 构造函数，创建指定名称的工作流程
    /// </summary>
    /// <param name="name">工作流名称，不能为空或仅包含空白字符</param>
    /// <exception cref="ArgumentNullException">当名称为空或仅包含空白字符时抛出</exception>
    public WorkProcess(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
        Name = name;
    }

    /// <summary>
    /// 工作流图标
    /// </summary>
    /// <remarks>用于在界面中显示的图标标识，通常是图标的CSS类名或图片路径</remarks>
    public string Icon { get; set; }

    /// <summary>
    /// 工作流描述信息
    /// </summary>
    /// <remarks>详细描述工作流的用途、功能和使用说明</remarks>
    public string Comment { get; set; }

    /// <summary>
    /// 工作流简称/缩写
    /// </summary>
    /// <remarks>
    /// 工作流的简短标识，通常用于：
    /// 1. 生成工作流实例编号的前缀
    /// 2. 在界面中快速识别工作流类型
    /// </remarks>
    public string Abbr { get; set; }

    /// <summary>
    /// 工作流日志记录等级
    /// </summary>
    /// <remarks>
    /// 控制工作流执行过程中的日志详细程度，默认为 Error 级别
    /// 可设置为 Trace, Debug, Information, Warning, Error, Critical 等
    /// </remarks>
    public LogLevel LogLevel { get; set; } = LogLevel.Error;

    /// <summary>
    /// 默认表单设计配置
    /// </summary>
    /// <remarks>
    /// 当工作任务（WorkTask）中没有定义具体的表单设计时，将使用此默认配置
    /// 通常包含表单的布局、字段定义、验证规则等信息
    /// </remarks>
    public string FormDesign { get; set; }

    /// <summary>
    /// 表单管理设计配置
    /// </summary>
    /// <remarks>
    /// 用于管理界面的完整功能表单配置，提供比默认表单更丰富的功能
    /// 通常包含更多的管理功能和权限控制
    /// </remarks>
    public string FormManageDesign { get; set; }

    /// <summary>
    /// 工作流程唯一标识符
    /// </summary>
    /// <remarks>数据库主键，系统自动生成</remarks>
    public int Id { get; set; }

    /// <summary>
    /// 获取或设置工作流名称
    /// </summary>
    /// <remarks>
    /// 设置时会自动去除前后空白字符
    /// 工作流名称在系统中应该具有唯一性
    /// </remarks>
    public string Name
    {
        get => _name;
        set => _name = value?.Trim();
    }

    /// <summary>
    /// 工作流最后更新时间
    /// </summary>
    /// <remarks>
    /// 记录工作流定义最后一次修改的时间，包含时区信息
    /// 用于版本控制和同步判断
    /// </remarks>
    public DateTimeOffset? UpdateTimeOffset { get; set; }

    /// <summary>
    /// 工作流版本号
    /// </summary>
    /// <remarks>
    /// 用于工作流的版本管理，每次修改工作流定义时版本号递增
    /// 支持多版本并存和版本回退功能
    /// </remarks>
    public int Version { get; set; }

    /// <summary>
    /// 工作流完成时执行的脚本
    /// </summary>
    /// <remarks>
    /// 当工作流实例正常完成所有步骤后自动执行的脚本
    /// 可用于清理资源、发送通知、触发后续流程等
    /// </remarks>
    public virtual WorkProcessScript OnComplete { get; set; }

    /// <summary>
    /// 工作流取消时执行的脚本
    /// </summary>
    /// <remarks>
    /// 当工作流实例被手动取消或因异常终止时执行的脚本
    /// 通常用于资源清理、状态回滚、通知相关人员等
    /// </remarks>
    public virtual WorkProcessScript OnCancel { get; set; }

    /// <summary>
    /// 工作流启动时执行的脚本
    /// </summary>
    /// <remarks>
    /// 当创建新的工作流实例时首先执行的脚本
    /// 可用于初始化数据、设置变量、发送启动通知等
    /// </remarks>
    public virtual WorkProcessScript OnStart { get; set; }

    /// <summary>
    /// 工作流是否已启用
    /// </summary>
    /// <remarks>
    /// true: 工作流已发布，可以创建新实例
    /// false: 工作流处于草稿状态，不能创建新实例
    /// </remarks>
    public bool Enable { get; set; }

    /// <summary>
    /// 工作流实例编号前缀
    /// </summary>
    /// <remarks>
    /// 用于生成工作流实例的唯一编号，默认为 "C"
    /// 实例编号格式通常为：前缀 + 年月日 + 序号
    /// 例如：C20231201001
    /// </remarks>
    public string Prefix { get; set; } = "C";

    /// <summary>
    /// 工作流配置参数集合
    /// </summary>
    /// <remarks>
    /// 存储工作流运行时需要的配置参数，在脚本环境中可以访问这些配置
    /// 常用于存储数据库连接、API地址、系统参数等配置信息
    /// </remarks>
    public virtual IList<WorkflowConfiguration> Configurations { get; set; } = new List<WorkflowConfiguration>();

    /// <summary>
    /// 工作流全局脚本
    /// </summary>
    /// <remarks>
    /// 在工作流的所有环节中都可以访问的公共脚本代码
    /// 通常包含公共函数、常量定义、工具方法等
    /// 每个工作环节都会加载这个全局脚本
    /// </remarks>
    public string GlobalScript { get; set; }

    /// <summary>
    /// 表单的 TypeScript 类型定义
    /// </summary>
    /// <remarks>
    /// 为表单字段提供 TypeScript 类型定义，用于脚本编辑器的智能提示和类型检查
    /// 如果未设置，将使用默认的类型声明模板
    /// 提高脚本开发的效率和准确性
    /// </remarks>
    public string FormTypeScriptDefined
    {
        get => _formTypeScriptDefined ??= DeclareCode;
        set => _formTypeScriptDefined = value;
    }

    /// <summary>
    /// 启用的插件列表
    /// </summary>
    /// <remarks>
    /// 存储当前工作流启用的插件名称列表
    /// 插件可以扩展工作流的功能，如数据库连接、HTTP客户端、第三方服务集成等
    /// 在脚本执行时，这些插件提供的功能将可用
    /// </remarks>
    public IList<string> Plugins { get; set; } = new List<string>();

    /// <summary>
    /// 工作流创建者
    /// </summary>
    /// <remarks>
    /// 记录创建此工作流定义的用户标识
    /// 用于权限控制和审计追踪
    /// </remarks>
    public string Creator { get; set; }

    /// <summary>
    /// 工作活动可删除的最小步骤数阈值。
    /// </summary>
    /// <remarks>
    /// 当工作流实例的步骤数小于此值时，允许ProcessInstance创建人删除工作活动
    /// 默认值为 2，防止误删除已进行多步的工作流实例
    /// 这是一个安全机制，保护重要的工作流数据
    /// </remarks>
    public int CanBeDeleteWorkActivityCount { get; set; } = 2;

    /// <summary>
    /// 工作流分组
    /// </summary>
    /// <remarks>
    /// 用于对工作流进行分类管理，便于组织和查找
    /// 可以按部门、业务类型、重要程度等维度进行分组
    /// 在管理界面中可以按分组显示和筛选工作流
    /// </remarks>
    public string Group { get; set; }
}