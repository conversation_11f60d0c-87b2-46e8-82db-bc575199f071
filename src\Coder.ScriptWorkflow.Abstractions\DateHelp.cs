﻿using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;

namespace Coder.ScriptWorkflow;

/// <summary>
///     上传附件
/// </summary>
public class UploadFileAttachment
{
    /// <summary>
    /// </summary>
    public IFormFile File { get; set; }

    /// <summary>
    /// </summary>
    public List<FileClaim> Claims { get; set; } = new();

    /// <summary>
    /// </summary>
    public class FileClaim
    {
        public string Name { get; set; }

        public string Value { get; set; }
    }
}

/// <summary>
/// </summary>
public static class DateHelp
{
    public static DateTime? GetDateS(DateTime? date)
    {
        if (date != null)
        {
            var nowTime = DateTime.Now;
            var dateTemp = (DateTime)date;
            return new DateTime(dateTemp.Year, dateTemp.Month, dateTemp.Day, 0, 0, 0);
        }

        return date;
    }

    public static DateTime? GetDateE(DateTime? date)
    {
        if (date != null)
        {
            var nowTime = DateTime.Now;
            var dateTemp = (DateTime)date;
            return new DateTime(dateTemp.Year, dateTemp.Month, dateTemp.Day, 23, 59, 59);
        }

        return date;
    }
    public static DateTime? GetDateWorkS(DateTime? date)
    {
        if (date != null)
        {
            var nowTime = DateTime.Now;
            var dateTemp = (DateTime)date;
            return new DateTime(dateTemp.Year, dateTemp.Month, dateTemp.Day, 8, 30, 0);
        }

        return date;
    }

    public static DateTime? GetDateWorkE(DateTime? date)
    {
        if (date != null)
        {
            var nowTime = DateTime.Now;
            var dateTemp = (DateTime)date;
            return new DateTime(dateTemp.Year, dateTemp.Month, dateTemp.Day, 16, 30, 00);
        }

        return date;
    }
}