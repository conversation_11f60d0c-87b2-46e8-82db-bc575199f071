﻿using Coder.ScriptWorkflow.Decisions;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ScriptDecisionMapping : IEntityTypeConfiguration<BoolScriptDecision>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<BoolScriptDecision> builder)
    {
        builder.HasBaseType<Decision>();
        builder.Property(_ => _.Script).HasColumnName("Script").HasColumnType("text").HasComment("执行脚本"); ;
        builder.HasOne(_ => _.ElseNode);
        builder.Property(_ => _.ElseDescription).HasMaxLength(20);
    }
}

internal class ConditionDecisionMapping : IEntityTypeConfiguration<ConditionDecision>
{
    public void Configure(EntityTypeBuilder<ConditionDecision> builder)
    {
        builder.HasBaseType<Decision>();
        builder.Property(_ => _.Script).HasColumnName("ConditionDecision_Script").HasColumnType("text").HasComment("执行脚本"); ;
        builder.HasMany(decision => decision.Settings);
    
    }
}

internal class ConditionSettingMapping : IEntityTypeConfiguration<ConditionSetting>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public ConditionSettingMapping(string prefix)
    {
        _prefix = prefix;
    }

    public void Configure(EntityTypeBuilder<ConditionSetting> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.ToTable($"{_prefix}_condition_decision");
        builder.Property(decision => decision.MatchValue).HasMaxLength(100).HasComment("匹配的字符串");
        builder.Property(decision => decision.Description).HasMaxLength(300);
        builder.HasOne(conditionSetting => conditionSetting.Node);
    }
}