﻿using System;
using Coder.ScriptWorkflow.ViewModels.Permissions;

namespace Coder.ScriptWorkflow.ViewModels.ProcessInstances;

/// <summary>
/// </summary>
public class ProcessInstanceViewModel : IProcessInstanceWithPermission
{
    /// <summary>
    /// </summary>
    public ProcessInstanceViewModel()
    {
    }

    /// <summary>
    ///     优先级
    /// </summary>
    public Priority Priority { get; set; }

    /// <summary>
    ///     工作流实例主题
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    ///     工作流创建人
    /// </summary>
    public string Creator { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     工作流程创建者。
    /// </summary>
    public string WorkProcessCreator { get; set; }

    /// <summary>
    ///     工作流状态
    /// </summary>
    public ProcessInstanceStatus Status { get; set; }

    /// <summary>
    ///     完成时间
    /// </summary>
    public DateTimeOffset? FinishTime { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>

    public DateTimeOffset CreateTime { get; set; }

    /// <summary>
    /// </summary>
    public DateTimeOffset? StartTime { get; set; }

    /// <summary>
    ///     工单编码
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    /// </summary>
    public DateTimeOffset? SuspendTime { get; set; }

    /// <summary>
    ///     挂起原因。
    /// </summary>
    public string SuspendComment { get; set; }

    /// <summary>
    ///     工作流备注
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    ///     工作流进度
    /// </summary>
    public string WorkProcessName { get; set; }

    /// <summary>
    /// </summary>
    public Object Form { get; set; }

    /// <summary>
    /// </summary>
    public string FormDesign { get; set; }

    /// <summary>
    ///     工作流实例的Tag
    /// </summary>
    public TagSubmit[] Tags { get; set; }

    /// <summary>
    ///     表单管理
    /// </summary>
    public string FormManageDesign { get; set; }

    /// <summary>
    /// </summary>
    public int WorkProcessId { get; set; }

    public int WorkActivityCount { get; set; }

    public int CanBeDeleteWorkActivity { get; set; }

    /// <inheritdoc />
    public ProcessInstanceStatus GetProcessInstanceStatus()
    {
        return Status;
    }

    /// <inheritdoc />
    public bool CanDeleteProcessInstance { get; set; }

    /// <inheritdoc />
    public bool IsInstanceCreator { get; set; }

    /// <inheritdoc />
    public string GetProcessInstanceCreator()
    {
        return Creator;
    }

    /// <summary>
    /// </summary>
    public bool IsWorkProcessCreator { get; set; }

    /// <inheritdoc />
    public bool IsManager { get; set; }

    /// <inheritdoc />
    public bool CanCreateProcessInstance { get; set; }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetWorkProcessCreator()
    {
        return WorkProcessCreator;
    }

    /// <summary>
    ///     是否为抄抄送用户
    /// </summary>
    public bool IsCCUser { get; set; }

    /// <summary>
    ///     读
    /// </summary>
    public bool HasRead { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool Success { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string Message { get; set; }
    /// <summary>
    /// 简称
    /// </summary>
    public string WorkProcessAbbr { get; set; }
}