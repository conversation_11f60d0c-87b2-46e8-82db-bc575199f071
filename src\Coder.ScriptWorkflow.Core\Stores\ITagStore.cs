using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// Tag 持久化接口
/// </summary>
public interface ITagStore
{
    /// <summary>
    /// 删除标签
    /// </summary>
    /// <param name="id">标签ID</param>
    void Delete(int id);

    /// <summary>
    /// 通过名称获取标签
    /// </summary>
    /// <param name="name">标签名称</param>
    /// <returns>标签对象，不存在时返回null</returns>
    [return: MaybeNull]
    Tag GetByName(string name);

    /// <summary>
    /// 增加或者更新标签
    /// </summary>
    /// <param name="workTask">标签对象</param>
    void AddOrUpdate(Tag workTask);

    /// <summary>
    /// 保存更改
    /// </summary>
    /// <returns>异步操作结果</returns>
    Task SaveChangesAsync();

    /// <summary>
    /// 获取标签或根据输入创建新标签
    /// </summary>
    /// <param name="tags">标签名称数组</param>
    /// <returns>标签对象集合</returns>
    IEnumerable<Tag> GetOrCreate(string[] tags);

    /// <summary>
    /// 减少标签引用次数
    /// </summary>
    /// <param name="beRemoveItem">要移除引用的标签名称集合</param>
    void UpdateRemoveReference(IEnumerable<string> beRemoveItem);

    /// <summary>
    /// 增加标签引用次数
    /// </summary>
    /// <param name="addedTags">要增加引用的标签名称集合</param>
    void UpdateAddReference(IEnumerable<string> addedTags);

    /// <summary>
    /// 通过标签名称查询标签集合
    /// </summary>
    /// <param name="userTags">用户标签名称集合</param>
    /// <returns>标签对象集合</returns>
    Task<IEnumerable<Tag>> GetByNamesAsync(IEnumerable<string> userTags);

    /// <summary>
    /// 获取流程实例关联的标签
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <returns>流程实例标签集合</returns>
    Task<IEnumerable<ProcessInstanceTag>> GetProcessInstanceTagsAsync(int processInstanceId);
}