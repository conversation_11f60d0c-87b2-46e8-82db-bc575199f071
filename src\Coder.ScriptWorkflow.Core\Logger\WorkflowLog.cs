﻿using System;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.Logger;

/// <summary>
///     workflow logger
/// </summary>
public class WorkflowLog
{
    /// <summary>
    /// </summary>
    public LogLevel LogLevel { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public int ProcessInstanceId { get; protected set; }

    /// <summary>
    ///     工作流Code
    /// </summary>
    public string ProcessInstanceNumber { get; set; }

    /// <summary>
    ///     版本
    /// </summary>
    public int Version { get; protected set; }

    /// <summary>
    /// </summary>
    public string WorkProcessName { get; protected set; }

    /// <summary>
    /// </summary>
    public string NodeName { get; set; }

    /// <summary>
    /// </summary>
    public int? WorkActivityId { get; set; }

    /// <summary>
    /// </summary>
    public WorkflowLogType Type { get; set; }

    /// <summary>
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     plugin名称。
    /// </summary>
    public string PluginName { get; set; }


    /// <summary>
    /// </summary>
    public DateTimeOffset CreateTime { get; protected set; } = DateTimeOffset.Now;

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    internal void SetProcessInstance(ProcessInstance processInstance)
    {
        var wp = processInstance.WorkProcess;
        Version = wp.Version;
        WorkProcessName = wp.Name;
        ProcessInstanceId = processInstance.Id;
        ProcessInstanceNumber = processInstance.Number;
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivity"></param>
    internal void SetWorkActivity(WorkActivity workActivity)
    {
        SetProcessInstance(workActivity.ProcessInstance);
        WorkActivityId = workActivity.Id;
        NodeName = workActivity.WorkTask.Name;
    }
}