﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using NiL.JS.Extensions;

namespace Coder.ScriptWorkflow.Assigners;

/// <summary>
///     通过脚本分配用户
/// </summary>
/// <returns>
/// </returns>
public class ScriptAssigner : Assigner
{
    /// <summary>
    /// </summary>
    public ScriptAssigner()
    {
        AssignScopeType = AssignScopeType.AllOfThem;
    }

    /// <summary>
    ///     派发必须是全部。
    /// </summary>
    public override AssignScopeType AssignScopeType
    {
        get => base.AssignScopeType;
        set { }
    }


    /// <summary>
    ///     脚本
    /// </summary>
    public string Script { get; set; } =
        @"//processInstance 是流程实例，workActivities 是上一次 同一个任务的处理结果。
如果是第一次进入那么是空数组。workActivities.length==0";

    /// <inheritdoc />
    public override AssignResult Assign(IWorkflowContext workflowContext,
        IEnumerable<WorkActivity> previous)
    {
        if (string.IsNullOrEmpty(Script))
        {
            throw new WorkflowDefinedException("Script没有定义返回的用户。", workflowContext.CurrentNode.Name);

        }

        var context = workflowContext.BuildScriptContext();
        var script = @$"var __CALLER = () =>{{
            {Script}
            }}";


        var was = previous != null ? new ScriptWorkActivityCollection(previous) : new ScriptWorkActivityCollection();

        context.DefineVariable("workActivities").Assign(context.GlobalContext.ProxyValue(was));
        try
        {
            context.Eval(script);
        }
        catch (JSException ex)
        {
            throw ex.ToJSException(workflowContext, "分配器-" + GetType().Name, script);
        }

        var concatFunction = context.GetVariable("__CALLER").As<Function>();
        var jsValue = concatFunction.Call(new Arguments());

        if (jsValue.ValueType != JSValueType.Object && jsValue.ValueType != JSValueType.String)
            throw new WorkflowDefinedException("需要返回用户名称数组或者字符串，如return 'user' 或者 return ['user1',user2']", workflowContext.CurrentNode.Name);

        var assignResult = JsValueConvert.ToAssignScope(jsValue, this);

        assignResult.ScopeType = AssignScopeType;

        return assignResult;
    }
    //public override IEnumerable<AssignUser> GetManualUsers(PerformerManager performerManager)
    //{
    //    return new AssignUser[0];
    //}

  
}