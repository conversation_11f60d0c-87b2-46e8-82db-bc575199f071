﻿DECLARE CNT INT; BEGIN 
 SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END INTO CNT FROM SYSOBJECTS WHERE NAME = 'swf_efmigrationshistory' ;IF CNT == 0 THEN CREATE TABLE "swf_efmigrationshistory" (
        "MigrationId" NVARCHAR2(150) NOT NULL,
        "ProductVersion" NVARCHAR2(32) NOT NULL,
        CONSTRAINT "PK_swf_efmigrationshistory" PRIMARY KEY ("MigrationId")
    );
    END IF; END; 

/START TRANSACTION;

/CREATE TABLE "swf_assigner" (
    "Id" INT IDENTITY NOT NULL,
    "AssignScopeType" INT NOT NULL,
    "Discriminator" NVARCHAR2(21) NOT NULL,
    "Script" NVARCHAR2(3000) NULL,
    "Performers" NVARCHAR2(1000) NULL,
    CONSTRAINT "PK_swf_assigner" PRIMARY KEY ("Id")
);

/CREATE TABLE "swf_global_variable" (
    "Id" INT IDENTITY NOT NULL,
    "Name" NVARCHAR2(100) NULL,
    "Variable" NVARCHAR2(300) NULL,
    "Env" INT NOT NULL,
    CONSTRAINT "PK_swf_global_variable" PRIMARY KEY ("Id")
);

/CREATE TABLE "swf_globalScript" (
    "Id" INT IDENTITY NOT NULL,
    "Name" NVARCHAR2(100) NULL,
    "Script" text NULL,
    CONSTRAINT "PK_swf_globalScript" PRIMARY KEY ("Id")
);

/CREATE TABLE "swf_PI_Tags" (
    "Id" INT IDENTITY NOT NULL,
    "Name" NVARCHAR2(32767) NULL,
    "Number" INT NOT NULL,
    CONSTRAINT "PK_swf_PI_Tags" PRIMARY KEY ("Id")
);

/comment on table "swf_PI_Tags" is '工作流程标记';

/CREATE TABLE "swf_scripts" (
    "Id" INT IDENTITY NOT NULL,
    "Script" NVARCHAR2(6000) NULL,
    "Discriminator" NVARCHAR2(34) NOT NULL,
    CONSTRAINT "PK_swf_scripts" PRIMARY KEY ("Id")
);

/CREATE TABLE "swf_swf_setting" (
    "Id" INT IDENTITY NOT NULL,
    "Script" text NULL,
    "Discriminator" NVARCHAR2(21) NOT NULL,
    "Plugins" text NULL,
    CONSTRAINT "PK_swf_swf_setting" PRIMARY KEY ("Id")
);

/comment on table "swf_swf_setting" is '工作流实例';

/comment on column "swf_swf_setting"."Id" is 'id';

/comment on column "swf_swf_setting"."Script" is '脚本';

/CREATE TABLE "swf_workflowLog" (
    "Id" INT IDENTITY NOT NULL,
    "LogLevel" INT NOT NULL,
    "ProcessInstanceId" INT NOT NULL,
    "ProcessInstanceNumber" NVARCHAR2(20) NULL,
    "Version" INT NOT NULL,
    "WorkProcessName" NVARCHAR2(64) NULL,
    "NodeName" NVARCHAR2(64) NULL,
    "WorkActivityId" INT NULL,
    "Type" NVARCHAR2(64) NOT NULL,
    "Content" text NULL,
    "PluginName" NVARCHAR2(30) NULL,
    "CreateTime" DATETIME WITH TIME ZONE NOT NULL,
    CONSTRAINT "PK_swf_workflowLog" PRIMARY KEY ("Id")
);

/comment on column "swf_workflowLog"."ProcessInstanceId" is '工单id';

/comment on column "swf_workflowLog"."ProcessInstanceNumber" is '工单号';

/comment on column "swf_workflowLog"."Version" is '工作流定义版本';

/comment on column "swf_workflowLog"."WorkProcessName" is '工作流名称';

/comment on column "swf_workflowLog"."NodeName" is '节点名称';

/CREATE TABLE "swf_workProcessPermission" (
    "Id" INT IDENTITY NOT NULL,
    "ProcessName" NVARCHAR2(100) NULL,
    "ManageRole" NVARCHAR2(400) NULL,
    CONSTRAINT "PK_swf_workProcessPermission" PRIMARY KEY ("Id")
);

/CREATE TABLE "swf_workProcess" (
    "Id" INT IDENTITY NOT NULL,
    "Icon" NVARCHAR2(20) NULL,
    "Comment" NVARCHAR2(400) NULL,
    "Abbr" NVARCHAR2(100) NULL,
    "LogLevel" INT NOT NULL,
    "FormDesign" text NULL,
    "FormManageDesign" text NULL,
    "Name" NVARCHAR2(100) NULL,
    "UpdateTimeOffset" DATETIME WITH TIME ZONE NULL,
    "Version" INT NOT NULL,
    "OnCompleteId" INT NULL,
    "OnCancelId" INT NULL,
    "OnStartId" INT NULL,
    "Enable" BIT NOT NULL,
    "Prefix" NVARCHAR2(10) NULL,
    "Configurations" text NULL,
    "GlobalScript" text NULL,
    "FormTypeScriptDefined" text NULL,
    "Plugins" NVARCHAR2(500) NULL,
    "Creator" NVARCHAR2(50) NULL,
    "CanBeDeleteWorkActivityCount" INT NOT NULL,
    "Group" NVARCHAR2(50) NULL,
    CONSTRAINT "PK_swf_workProcess" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCancelId" FOREIGN KEY ("OnCancelId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_workProcess_swf_scripts_OnCompleteId" FOREIGN KEY ("OnCompleteId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_workProcess_swf_scripts_OnStartId" FOREIGN KEY ("OnStartId") REFERENCES "swf_scripts" ("Id")
);

/comment on column "swf_workProcess"."Icon" is '图标';

/comment on column "swf_workProcess"."Comment" is '备注';

/comment on column "swf_workProcess"."Abbr" is '简称';

/CREATE TABLE "swf_PermissionPerformer" (
    "Id" INT IDENTITY NOT NULL,
    "Type" INT NOT NULL,
    "Name" NVARCHAR2(32767) NULL,
    "WorkProcessPermissionId" INT NULL,
    CONSTRAINT "PK_swf_PermissionPerformer" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProcessPermissionId" FOREIGN KEY ("WorkProcessPermissionId") REFERENCES "swf_workProcessPermission" ("Id")
);

/CREATE TABLE "swf_node" (
    "Id" INT IDENTITY NOT NULL,
    "Name" NVARCHAR2(32) NULL,
    "Auto" BIT NOT NULL,
    "NextNodeId" INT NULL,
    "WorkProcessId" INT NULL,
    "Position" NVARCHAR2(32767) NULL,
    "Discriminator" NVARCHAR2(21) NOT NULL,
    "MatchDescription" NVARCHAR2(20) NULL,
    "Script" text NULL,
    "ElseNodeId" INT NULL,
    "ElseDescription" NVARCHAR2(20) NULL,
    "ConditionDecision_Script" text NULL,
    "NextTaskPerformers" INT NULL,
    "AssignerId" INT NULL,
    "CanGiveUp" BIT NULL,
    "FormDesign" text NULL,
    "WorkActivityCompleteScriptId" INT NULL,
    "WorkTaskCompleteScriptId" INT NULL,
    "WorkTaskStartScriptId" INT NULL,
    "SuggestionComment" NVARCHAR2(50) NULL,
    "ExtendInfo" text NULL,
    CONSTRAINT "PK_swf_node" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_node_swf_assigner_AssignerId" FOREIGN KEY ("AssignerId") REFERENCES "swf_assigner" ("Id"),
    CONSTRAINT "FK_swf_node_swf_node_ElseNodeId" FOREIGN KEY ("ElseNodeId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_node_swf_node_NextNodeId" FOREIGN KEY ("NextNodeId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_node_swf_scripts_WorkActivityCompleteScriptId" FOREIGN KEY ("WorkActivityCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskCompleteScriptId" FOREIGN KEY ("WorkTaskCompleteScriptId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_node_swf_scripts_WorkTaskStartScriptId" FOREIGN KEY ("WorkTaskStartScriptId") REFERENCES "swf_scripts" ("Id"),
    CONSTRAINT "FK_swf_node_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id") ON DELETE SET NULL
);

/comment on column "swf_node"."Script" is '执行脚本';

/comment on column "swf_node"."ConditionDecision_Script" is '执行脚本';

/CREATE TABLE "swf_processInstance" (
    "Id" INT IDENTITY NOT NULL,
    "IsDebug" BIT NOT NULL,
    "IsDelete" BIT NOT NULL,
    "WorkActivityCount" INT NOT NULL,
    "WorkProcessId" INT NULL,
    "Priority" INT DEFAULT 100 NOT NULL,
    "Subject" NVARCHAR2(500) NULL,
    "Creator" NVARCHAR2(32) NULL,
    "Status" INT NOT NULL,
    "FinishTime" TIMESTAMP NULL,
    "CreateTime" TIMESTAMP NOT NULL,
    "StartTime" TIMESTAMP NULL,
    "Number" NVARCHAR2(20) NOT NULL,
    "SuspendTime" TIMESTAMP NULL,
    "SuspendComment" NVARCHAR2(128) NULL,
    "Comment" NVARCHAR2(256) NULL,
    "Form" json(32767) NULL,
    "CreatorName" NVARCHAR2(50) NULL,
    CONSTRAINT "PK_swf_processInstance" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_processInstance_swf_workProcess_WorkProcessId" FOREIGN KEY ("WorkProcessId") REFERENCES "swf_workProcess" ("Id")
);

/comment on table "swf_processInstance" is '工作流实例';

/comment on column "swf_processInstance"."IsDebug" is '是否处于debug模式';

/comment on column "swf_processInstance"."IsDelete" is '是否已经删除';

/CREATE TABLE "swf_condition_decision" (
    "Id" INT IDENTITY NOT NULL,
    "NodeId" INT NULL,
    "MatchValue" NVARCHAR2(100) NULL,
    "Description" NVARCHAR2(300) NULL,
    "ConditionDecisionId" INT NULL,
    CONSTRAINT "PK_swf_condition_decision" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_condition_decision_swf_node_ConditionDecisionId" FOREIGN KEY ("ConditionDecisionId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_condition_decision_swf_node_NodeId" FOREIGN KEY ("NodeId") REFERENCES "swf_node" ("Id")
);

/comment on column "swf_condition_decision"."MatchValue" is '匹配的字符串';

/CREATE TABLE "swf_WorkTaskCommand" (
    "Id" INT IDENTITY NOT NULL,
    "Name" NVARCHAR2(100) NULL,
    "Order" INT NOT NULL,
    "Discriminator" NVARCHAR2(34) NOT NULL,
    "WorkTaskId" INT NULL,
    "Script" text NULL,
    CONSTRAINT "PK_swf_WorkTaskCommand" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_WorkTaskCommand_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id")
);

/CREATE TABLE "swf_files" (
    "Id" INT IDENTITY NOT NULL,
    "FileName" NVARCHAR2(100) NULL,
    "FileId" NVARCHAR2(50) NULL,
    "FileType" INT NOT NULL,
    "CreateUser" NVARCHAR2(100) NULL,
    "ProcessInstanceId" INT NULL,
    CONSTRAINT "PK_swf_files" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_files_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

/CREATE TABLE "swf_PI_Distribution" (
    "Id" INT IDENTITY NOT NULL,
    "ReadTime" DATETIME WITH TIME ZONE NULL,
    "CreateTime" DATETIME WITH TIME ZONE NOT NULL,
    "UserName" NVARCHAR2(20) NULL,
    "UserRealName" NVARCHAR2(20) NULL,
    "ProcessInstanceId" INT NULL,
    "HasRead" BIT NOT NULL,
    CONSTRAINT "PK_swf_PI_Distribution" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

/comment on table "swf_PI_Distribution" is '工作流程分发记录';

/CREATE TABLE "swf_PI_ProcessInstanceTags" (
    "Id" BIGINT IDENTITY NOT NULL,
    "TagId" INT NULL,
    "Color" NVARCHAR2(20) NULL,
    "ProcessInstanceId" INT NULL,
    "CanDelete" BIT NOT NULL,
    CONSTRAINT "PK_swf_PI_ProcessInstanceTags" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId" FOREIGN KEY ("TagId") REFERENCES "swf_PI_Tags" ("Id"),
    CONSTRAINT "FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

/comment on table "swf_PI_ProcessInstanceTags" is '工作流程标记';

/CREATE TABLE "swf_WorkActivity" (
    "Id" INT IDENTITY NOT NULL,
    "Priority" INT DEFAULT 100 NOT NULL,
    "WorkTaskId" INT NULL,
    "AssignPerformers" NVARCHAR2(1000) NULL,
    "Status" INT NOT NULL,
    "AssignTime" TIMESTAMP NULL,
    "TaskCreatingGroup" NVARCHAR2(32) NULL,
    "DisposeUser" NVARCHAR2(50) NULL,
    "DisposeUserName" NVARCHAR2(50) NULL,
    "DisposeTime" TIMESTAMP NULL,
    "CreateTime" TIMESTAMP NOT NULL,
    "Command" NVARCHAR2(32767) NULL,
    "Comment" NVARCHAR2(1000) NULL,
    "TimeSpan" TIMESTAMP NULL,
    "ProcessInstanceId" INT NULL,
    CONSTRAINT "PK_swf_WorkActivity" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_swf_WorkActivity_swf_node_WorkTaskId" FOREIGN KEY ("WorkTaskId") REFERENCES "swf_node" ("Id"),
    CONSTRAINT "FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId" FOREIGN KEY ("ProcessInstanceId") REFERENCES "swf_processInstance" ("Id")
);

/CREATE OR REPLACE TRIGGER "rowversion_swf_WorkActivity"
BEFORE INSERT OR UPDATE ON "swf_WorkActivity"
FOR EACH ROW
BEGIN
  :NEW."TimeSpan" := NVL(:OLD."TimeSpan", '00000000') + 1;
END;

/CREATE INDEX "IX_swf_condition_decision_ConditionDecisionId" ON "swf_condition_decision" ("ConditionDecisionId");

/CREATE INDEX "IX_swf_condition_decision_NodeId" ON "swf_condition_decision" ("NodeId");

/CREATE INDEX "IX_swf_files_ProcessInstanceId" ON "swf_files" ("ProcessInstanceId");

/CREATE INDEX "IX_swf_node_AssignerId" ON "swf_node" ("AssignerId");

/CREATE INDEX "IX_swf_node_ElseNodeId" ON "swf_node" ("ElseNodeId");

/CREATE INDEX "IX_swf_node_NextNodeId" ON "swf_node" ("NextNodeId");

/CREATE INDEX "IX_swf_node_WorkActivityCompleteScriptId" ON "swf_node" ("WorkActivityCompleteScriptId");

/CREATE INDEX "IX_swf_node_WorkProcessId" ON "swf_node" ("WorkProcessId");

/CREATE INDEX "IX_swf_node_WorkTaskCompleteScriptId" ON "swf_node" ("WorkTaskCompleteScriptId");

/CREATE INDEX "IX_swf_node_WorkTaskStartScriptId" ON "swf_node" ("WorkTaskStartScriptId");

/CREATE INDEX "IX_swf_PermissionPerformer_WorkProcessPermissionId" ON "swf_PermissionPerformer" ("WorkProcessPermissionId");

/CREATE INDEX "IX_swf_PI_Distribution_ProcessInstanceId" ON "swf_PI_Distribution" ("ProcessInstanceId");

/CREATE INDEX "IX_swf_PI_ProcessInstanceTags_ProcessInstanceId" ON "swf_PI_ProcessInstanceTags" ("ProcessInstanceId");

/CREATE INDEX "IX_swf_PI_ProcessInstanceTags_TagId" ON "swf_PI_ProcessInstanceTags" ("TagId");

/CREATE INDEX "IX_swf_processInstance_Number" ON "swf_processInstance" ("Number");

/CREATE INDEX "IX_swf_processInstance_WorkProcessId" ON "swf_processInstance" ("WorkProcessId");

/CREATE INDEX "IX_swf_WorkActivity_ProcessInstanceId" ON "swf_WorkActivity" ("ProcessInstanceId");

/CREATE INDEX "IX_swf_WorkActivity_WorkTaskId" ON "swf_WorkActivity" ("WorkTaskId");

/CREATE INDEX "IX_swf_workflowLog_ProcessInstanceNumber" ON "swf_workflowLog" ("ProcessInstanceNumber");

/CREATE UNIQUE INDEX "IX_swf_workProcess_Name_Version" ON "swf_workProcess" ("Name", "Version");

/CREATE INDEX "IX_swf_workProcess_OnCancelId" ON "swf_workProcess" ("OnCancelId");

/CREATE INDEX "IX_swf_workProcess_OnCompleteId" ON "swf_workProcess" ("OnCompleteId");

/CREATE INDEX "IX_swf_workProcess_OnStartId" ON "swf_workProcess" ("OnStartId");

/CREATE INDEX "IX_swf_workProcessPermission_ProcessName" ON "swf_workProcessPermission" ("ProcessName");

/CREATE INDEX "IX_swf_WorkTaskCommand_WorkTaskId" ON "swf_WorkTaskCommand" ("WorkTaskId");

/INSERT INTO "swf_efmigrationshistory" ("MigrationId", "ProductVersion")
VALUES ('20250616060737_init3', '8.0.10');

/COMMIT;

/