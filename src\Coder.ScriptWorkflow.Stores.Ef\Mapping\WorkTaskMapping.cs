﻿using Coder.ScriptWorkflow.Nodes;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class WorkTaskMapping : IEntityTypeConfiguration<WorkTask>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<WorkTask> builder)
    {
        builder.HasBaseType<Node>();
        builder.Property(_ => _.Name).HasMaxLength(32);
        builder.HasOne(_ => _.Assigner);
        builder.Property(_ => _.SuggestionComment).HasMaxLength(50);


        builder.Property(_ => _.FormDesign).HasColumnType("text");
        builder.HasMany(_ => _.Commands);
        builder.Property(_ => _.CanGiveUp);
        builder.HasOne(_ => _.WorkActivityCompleteScript);
        builder.HasOne(_ => _.WorkTaskCompleteScript);
        builder.HasOne(_ => _.WorkTaskStartScript);
        builder.Property(_ => _.ExtendInfo).HasColumnType("text").HasJsonValueConversion();
        builder.Property(_ => _.NextTaskPerformers);
    }
}