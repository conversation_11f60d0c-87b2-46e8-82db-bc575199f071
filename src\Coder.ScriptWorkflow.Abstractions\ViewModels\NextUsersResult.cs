﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
/// </summary>
public class NextUsersResult
{
    /// <summary>
    ///     是否showDialog
    /// </summary>
    public bool ShowDialog { get; set; } = true;

    /// <summary>
    /// </summary>
    public string WorkTaskName { get; set; }

    /// <summary>
    /// </summary>
    public IEnumerable<AssignUser> Users { get; set; }

    /// <summary>
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// </summary>
    public string Message { get; set; }
}