﻿using System;
using Dapper;
using MySqlConnector;
using NiL.JS.Core;
using Array = NiL.JS.BaseLibrary.Array;

namespace Coder.ScriptWorkflow.Scripts.Plugin.MySql;

/// <summary>
/// </summary>
public class MysqlExecute
{
    private readonly IWorkflowContext _workflowContext;

    public MysqlExecute(IWorkflowContext workflowContext)
    {
        _workflowContext = workflowContext;
    }
    /// <summary>
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sql"></param>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    public int ExecuteNon(string connection, string sql, JSValue jsValue)
    {
        if (connection == null) throw new ArgumentNullException(nameof(connection));
        if (sql == null) throw new ArgumentNullException(nameof(sql));

        using var mysqlConnection = new MySqlConnection(connection);
        var obj = jsValue.ToDynamicObject();
        if (jsValue != null)

            return mysqlConnection.Execute(sql, obj);
        return mysqlConnection.Execute(sql);
    }

    /// <summary>
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sql"></param>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    public JSValue ExecuteScalar(string connection, string sql, JSValue jsValue)
    {
        if (connection == null) throw new ArgumentNullException(nameof(connection));
        if (sql == null) throw new ArgumentNullException(nameof(sql));

        using var mysqlConnection = new MySqlConnection(connection);
        dynamic result = null;
        if (jsValue == null)
        {
            var obj = jsValue.ToDynamicObject();
            result = mysqlConnection.QueryFirst<dynamic>(sql, obj);
        }
        else
        {
            result = mysqlConnection.QueryFirst<dynamic>(sql);
        }

        
        return JsValueConvert.ToJsValue(result, _workflowContext.JavascriptGlobalContext);
    }

    /// <summary>
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="sql"></param>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    public Array ExecuteQuery(string connection, string sql, JSValue jsValue)
    {
        if (connection == null) throw new ArgumentNullException(nameof(connection));
        if (sql == null) throw new ArgumentNullException(nameof(sql));
        using var mysqlConnection = new MySqlConnection(connection);
        var obj = jsValue.ToDynamicObject();
        var dataResult = mysqlConnection.Query<dynamic>(sql, obj);

        var result = new Array();

        foreach (var item in dataResult) result.Add(JsValueConvert.ToJsValue(item, _workflowContext.JavascriptGlobalContext));

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="command"></param>
    /// <param name="key"></param>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    public MySqlParameter Create(MySqlCommand command, string key, JSValue jsValue)
    {
        command.CreateParameter();
        return new MySqlParameter
        {
            ParameterName = key,
            Value = jsValue.Value
        };
    }
}