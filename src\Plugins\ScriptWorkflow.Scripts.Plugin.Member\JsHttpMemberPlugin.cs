﻿using System.IO;
using Coder.Member.Clients;
using Coder.ScriptWorkflow.Scripts.Plugins;

namespace Coder.ScriptWorkflow.Scripts.Plugin.Member;

/// <summary>
///     /
/// </summary>
public class JsHttpMemberPlugin : IPlugin
{
    private readonly IUserClient _client;

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    public JsHttpMemberPlugin(IUserClient client)
    {
        _client = client;
    }

    /// <summary>
    /// </summary>
    public string Name { get; set; } = "jsMember";

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new JsHttpMember(_client, context);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        var stream = typeof(JsHttpMemberPlugin).Assembly
            .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.Member.defined.ts");
        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}