﻿IF OBJECT_ID(N'[swf_efmigrationshistory]') IS NULL
BEGIN
    CREATE TABLE [swf_efmigrationshistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK_swf_efmigrationshistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [swf_assigner] (
    [Id] int NOT NULL IDENTITY,
    [AssignScopeType] int NOT NULL,
    [Discriminator] nvarchar(21) NOT NULL,
    [Script] nvarchar(3000) NULL,
    [Performers] nvarchar(1000) NULL,
    CONSTRAINT [PK_swf_assigner] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [swf_global_variable] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NULL,
    [Variable] nvarchar(300) NULL,
    [Env] int NOT NULL,
    CONSTRAINT [PK_swf_global_variable] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [swf_globalScript] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NULL,
    [Script] text NULL,
    CONSTRAINT [PK_swf_globalScript] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [swf_PI_Tags] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NULL,
    [Number] int NOT NULL,
    CONSTRAINT [PK_swf_PI_Tags] PRIMARY KEY ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'工作流程标记';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_PI_Tags';
GO

CREATE TABLE [swf_scripts] (
    [Id] int NOT NULL IDENTITY,
    [Script] nvarchar(max) NULL,
    [Discriminator] nvarchar(34) NOT NULL,
    CONSTRAINT [PK_swf_scripts] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [swf_swf_setting] (
    [Id] int NOT NULL IDENTITY,
    [Script] text NULL,
    [Discriminator] nvarchar(21) NOT NULL,
    [Plugins] text NULL,
    CONSTRAINT [PK_swf_swf_setting] PRIMARY KEY ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'工作流实例';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_swf_setting';
SET @description = N'id';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_swf_setting', 'COLUMN', N'Id';
SET @description = N'脚本';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_swf_setting', 'COLUMN', N'Script';
GO

CREATE TABLE [swf_workflowLog] (
    [Id] int NOT NULL IDENTITY,
    [LogLevel] int NOT NULL,
    [ProcessInstanceId] int NOT NULL,
    [ProcessInstanceNumber] nvarchar(20) NULL,
    [Version] int NOT NULL,
    [WorkProcessName] nvarchar(64) NULL,
    [NodeName] nvarchar(64) NULL,
    [WorkActivityId] int NULL,
    [Type] nvarchar(64) NOT NULL,
    [Content] text NULL,
    [PluginName] nvarchar(30) NULL,
    [CreateTime] datetimeoffset NOT NULL,
    CONSTRAINT [PK_swf_workflowLog] PRIMARY KEY ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'工单id';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workflowLog', 'COLUMN', N'ProcessInstanceId';
SET @description = N'工单号';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workflowLog', 'COLUMN', N'ProcessInstanceNumber';
SET @description = N'工作流定义版本';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workflowLog', 'COLUMN', N'Version';
SET @description = N'工作流名称';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workflowLog', 'COLUMN', N'WorkProcessName';
SET @description = N'节点名称';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workflowLog', 'COLUMN', N'NodeName';
GO

CREATE TABLE [swf_workProcessPermission] (
    [Id] int NOT NULL IDENTITY,
    [ProcessName] nvarchar(100) NULL,
    [ManageRole] nvarchar(400) NULL,
    CONSTRAINT [PK_swf_workProcessPermission] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [swf_workProcess] (
    [Id] int NOT NULL IDENTITY,
    [Icon] nvarchar(20) NULL,
    [Comment] nvarchar(400) NULL,
    [Abbr] nvarchar(100) NULL,
    [LogLevel] int NOT NULL,
    [FormDesign] text NULL,
    [FormManageDesign] text NULL,
    [Name] nvarchar(100) NULL,
    [UpdateTimeOffset] datetimeoffset NULL,
    [Version] int NOT NULL,
    [OnCompleteId] int NULL,
    [OnCancelId] int NULL,
    [OnStartId] int NULL,
    [Enable] bit NOT NULL,
    [Prefix] nvarchar(10) NULL,
    [Configurations] text NULL,
    [GlobalScript] text NULL,
    [FormTypeScriptDefined] text NULL,
    [Plugins] nvarchar(500) NULL,
    [Creator] nvarchar(50) NULL,
    [CanBeDeleteWorkActivityCount] int NOT NULL,
    [Group] nvarchar(50) NULL,
    CONSTRAINT [PK_swf_workProcess] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_workProcess_swf_scripts_OnCancelId] FOREIGN KEY ([OnCancelId]) REFERENCES [swf_scripts] ([Id]),
    CONSTRAINT [FK_swf_workProcess_swf_scripts_OnCompleteId] FOREIGN KEY ([OnCompleteId]) REFERENCES [swf_scripts] ([Id]),
    CONSTRAINT [FK_swf_workProcess_swf_scripts_OnStartId] FOREIGN KEY ([OnStartId]) REFERENCES [swf_scripts] ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'图标';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workProcess', 'COLUMN', N'Icon';
SET @description = N'备注';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workProcess', 'COLUMN', N'Comment';
SET @description = N'简称';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_workProcess', 'COLUMN', N'Abbr';
GO

CREATE TABLE [swf_PermissionPerformer] (
    [Id] int NOT NULL IDENTITY,
    [Type] int NOT NULL,
    [Name] nvarchar(max) NULL,
    [WorkProcessPermissionId] int NULL,
    CONSTRAINT [PK_swf_PermissionPerformer] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProcessPermissionId] FOREIGN KEY ([WorkProcessPermissionId]) REFERENCES [swf_workProcessPermission] ([Id])
);
GO

CREATE TABLE [swf_node] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(32) NULL,
    [Auto] bit NOT NULL,
    [NextNodeId] int NULL,
    [WorkProcessId] int NULL,
    [Position] nvarchar(max) NULL,
    [Discriminator] nvarchar(21) NOT NULL,
    [MatchDescription] nvarchar(20) NULL,
    [Script] text NULL,
    [ElseNodeId] int NULL,
    [ElseDescription] nvarchar(20) NULL,
    [ConditionDecision_Script] text NULL,
    [NextTaskPerformers] int NULL,
    [AssignerId] int NULL,
    [CanGiveUp] bit NULL,
    [FormDesign] text NULL,
    [WorkActivityCompleteScriptId] int NULL,
    [WorkTaskCompleteScriptId] int NULL,
    [WorkTaskStartScriptId] int NULL,
    [SuggestionComment] nvarchar(50) NULL,
    [ExtendInfo] text NULL,
    CONSTRAINT [PK_swf_node] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_node_swf_assigner_AssignerId] FOREIGN KEY ([AssignerId]) REFERENCES [swf_assigner] ([Id]),
    CONSTRAINT [FK_swf_node_swf_node_ElseNodeId] FOREIGN KEY ([ElseNodeId]) REFERENCES [swf_node] ([Id]),
    CONSTRAINT [FK_swf_node_swf_node_NextNodeId] FOREIGN KEY ([NextNodeId]) REFERENCES [swf_node] ([Id]),
    CONSTRAINT [FK_swf_node_swf_scripts_WorkActivityCompleteScriptId] FOREIGN KEY ([WorkActivityCompleteScriptId]) REFERENCES [swf_scripts] ([Id]),
    CONSTRAINT [FK_swf_node_swf_scripts_WorkTaskCompleteScriptId] FOREIGN KEY ([WorkTaskCompleteScriptId]) REFERENCES [swf_scripts] ([Id]),
    CONSTRAINT [FK_swf_node_swf_scripts_WorkTaskStartScriptId] FOREIGN KEY ([WorkTaskStartScriptId]) REFERENCES [swf_scripts] ([Id]),
    CONSTRAINT [FK_swf_node_swf_workProcess_WorkProcessId] FOREIGN KEY ([WorkProcessId]) REFERENCES [swf_workProcess] ([Id]) ON DELETE SET NULL
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'执行脚本';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_node', 'COLUMN', N'Script';
SET @description = N'执行脚本';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_node', 'COLUMN', N'ConditionDecision_Script';
GO

CREATE TABLE [swf_processInstance] (
    [Id] int NOT NULL IDENTITY,
    [IsDebug] bit NOT NULL,
    [IsDelete] bit NOT NULL,
    [WorkActivityCount] int NOT NULL,
    [WorkProcessId] int NULL,
    [Priority] int NOT NULL DEFAULT 100,
    [Subject] nvarchar(500) NULL,
    [Creator] nvarchar(32) NULL,
    [Status] int NOT NULL,
    [FinishTime] datetime2 NULL,
    [CreateTime] datetime2 NOT NULL,
    [StartTime] datetime2 NULL,
    [Number] nvarchar(20) NOT NULL,
    [SuspendTime] datetime2 NULL,
    [SuspendComment] nvarchar(128) NULL,
    [Comment] nvarchar(256) NULL,
    [Form] json NULL,
    [CreatorName] nvarchar(50) NULL,
    CONSTRAINT [PK_swf_processInstance] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_processInstance_swf_workProcess_WorkProcessId] FOREIGN KEY ([WorkProcessId]) REFERENCES [swf_workProcess] ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'工作流实例';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_processInstance';
SET @description = N'是否处于debug模式';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_processInstance', 'COLUMN', N'IsDebug';
SET @description = N'是否已经删除';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_processInstance', 'COLUMN', N'IsDelete';
GO

CREATE TABLE [swf_condition_decision] (
    [Id] int NOT NULL IDENTITY,
    [NodeId] int NULL,
    [MatchValue] nvarchar(100) NULL,
    [Description] nvarchar(300) NULL,
    [ConditionDecisionId] int NULL,
    CONSTRAINT [PK_swf_condition_decision] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_condition_decision_swf_node_ConditionDecisionId] FOREIGN KEY ([ConditionDecisionId]) REFERENCES [swf_node] ([Id]),
    CONSTRAINT [FK_swf_condition_decision_swf_node_NodeId] FOREIGN KEY ([NodeId]) REFERENCES [swf_node] ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'匹配的字符串';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_condition_decision', 'COLUMN', N'MatchValue';
GO

CREATE TABLE [swf_WorkTaskCommand] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NULL,
    [Order] int NOT NULL,
    [Discriminator] nvarchar(34) NOT NULL,
    [WorkTaskId] int NULL,
    [Script] text NULL,
    CONSTRAINT [PK_swf_WorkTaskCommand] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_WorkTaskCommand_swf_node_WorkTaskId] FOREIGN KEY ([WorkTaskId]) REFERENCES [swf_node] ([Id])
);
GO

CREATE TABLE [swf_files] (
    [Id] int NOT NULL IDENTITY,
    [FileName] nvarchar(100) NULL,
    [FileId] nvarchar(50) NULL,
    [FileType] int NOT NULL,
    [CreateUser] nvarchar(100) NULL,
    [ProcessInstanceId] int NULL,
    CONSTRAINT [PK_swf_files] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_files_swf_processInstance_ProcessInstanceId] FOREIGN KEY ([ProcessInstanceId]) REFERENCES [swf_processInstance] ([Id])
);
GO

CREATE TABLE [swf_PI_Distribution] (
    [Id] int NOT NULL IDENTITY,
    [ReadTime] datetimeoffset NULL,
    [CreateTime] datetimeoffset NOT NULL,
    [UserName] nvarchar(20) NULL,
    [UserRealName] nvarchar(20) NULL,
    [ProcessInstanceId] int NULL,
    [HasRead] bit NOT NULL,
    CONSTRAINT [PK_swf_PI_Distribution] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId] FOREIGN KEY ([ProcessInstanceId]) REFERENCES [swf_processInstance] ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'工作流程分发记录';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_PI_Distribution';
GO

CREATE TABLE [swf_PI_ProcessInstanceTags] (
    [Id] bigint NOT NULL IDENTITY,
    [TagId] int NULL,
    [Color] nvarchar(20) NULL,
    [ProcessInstanceId] int NULL,
    [CanDelete] bit NOT NULL,
    CONSTRAINT [PK_swf_PI_ProcessInstanceTags] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId] FOREIGN KEY ([TagId]) REFERENCES [swf_PI_Tags] ([Id]),
    CONSTRAINT [FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstanceId] FOREIGN KEY ([ProcessInstanceId]) REFERENCES [swf_processInstance] ([Id])
);
DECLARE @defaultSchema AS sysname;
SET @defaultSchema = SCHEMA_NAME();
DECLARE @description AS sql_variant;
SET @description = N'工作流程标记';
EXEC sp_addextendedproperty 'MS_Description', @description, 'SCHEMA', @defaultSchema, 'TABLE', N'swf_PI_ProcessInstanceTags';
GO

CREATE TABLE [swf_WorkActivity] (
    [Id] int NOT NULL IDENTITY,
    [Priority] int NOT NULL DEFAULT 100,
    [WorkTaskId] int NULL,
    [AssignPerformers] nvarchar(1000) NULL,
    [Status] int NOT NULL,
    [AssignTime] datetime2 NULL,
    [TaskCreatingGroup] nvarchar(32) NULL,
    [DisposeUser] nvarchar(50) NULL,
    [DisposeUserName] nvarchar(50) NULL,
    [DisposeTime] datetime2 NULL,
    [CreateTime] datetime2 NOT NULL,
    [Command] nvarchar(max) NULL,
    [Comment] nvarchar(1000) NULL,
    [TimeSpan] datetime2 NULL,
    [ProcessInstanceId] int NULL,
    CONSTRAINT [PK_swf_WorkActivity] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_swf_WorkActivity_swf_node_WorkTaskId] FOREIGN KEY ([WorkTaskId]) REFERENCES [swf_node] ([Id]),
    CONSTRAINT [FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId] FOREIGN KEY ([ProcessInstanceId]) REFERENCES [swf_processInstance] ([Id])
);
GO

CREATE INDEX [IX_swf_condition_decision_ConditionDecisionId] ON [swf_condition_decision] ([ConditionDecisionId]);
GO

CREATE INDEX [IX_swf_condition_decision_NodeId] ON [swf_condition_decision] ([NodeId]);
GO

CREATE INDEX [IX_swf_files_ProcessInstanceId] ON [swf_files] ([ProcessInstanceId]);
GO

CREATE INDEX [IX_swf_node_AssignerId] ON [swf_node] ([AssignerId]);
GO

CREATE INDEX [IX_swf_node_ElseNodeId] ON [swf_node] ([ElseNodeId]);
GO

CREATE INDEX [IX_swf_node_NextNodeId] ON [swf_node] ([NextNodeId]);
GO

CREATE INDEX [IX_swf_node_WorkActivityCompleteScriptId] ON [swf_node] ([WorkActivityCompleteScriptId]);
GO

CREATE INDEX [IX_swf_node_WorkProcessId] ON [swf_node] ([WorkProcessId]);
GO

CREATE INDEX [IX_swf_node_WorkTaskCompleteScriptId] ON [swf_node] ([WorkTaskCompleteScriptId]);
GO

CREATE INDEX [IX_swf_node_WorkTaskStartScriptId] ON [swf_node] ([WorkTaskStartScriptId]);
GO

CREATE INDEX [IX_swf_PermissionPerformer_WorkProcessPermissionId] ON [swf_PermissionPerformer] ([WorkProcessPermissionId]);
GO

CREATE INDEX [IX_swf_PI_Distribution_ProcessInstanceId] ON [swf_PI_Distribution] ([ProcessInstanceId]);
GO

CREATE INDEX [IX_swf_PI_ProcessInstanceTags_ProcessInstanceId] ON [swf_PI_ProcessInstanceTags] ([ProcessInstanceId]);
GO

CREATE INDEX [IX_swf_PI_ProcessInstanceTags_TagId] ON [swf_PI_ProcessInstanceTags] ([TagId]);
GO

CREATE INDEX [IX_swf_processInstance_Number] ON [swf_processInstance] ([Number]);
GO

CREATE INDEX [IX_swf_processInstance_WorkProcessId] ON [swf_processInstance] ([WorkProcessId]);
GO

CREATE INDEX [IX_swf_WorkActivity_ProcessInstanceId] ON [swf_WorkActivity] ([ProcessInstanceId]);
GO

CREATE INDEX [IX_swf_WorkActivity_WorkTaskId] ON [swf_WorkActivity] ([WorkTaskId]);
GO

CREATE INDEX [IX_swf_workflowLog_ProcessInstanceNumber] ON [swf_workflowLog] ([ProcessInstanceNumber]);
GO

CREATE UNIQUE INDEX [IX_swf_workProcess_Name_Version] ON [swf_workProcess] ([Name], [Version]) WHERE [Name] IS NOT NULL;
GO

CREATE INDEX [IX_swf_workProcess_OnCancelId] ON [swf_workProcess] ([OnCancelId]);
GO

CREATE INDEX [IX_swf_workProcess_OnCompleteId] ON [swf_workProcess] ([OnCompleteId]);
GO

CREATE INDEX [IX_swf_workProcess_OnStartId] ON [swf_workProcess] ([OnStartId]);
GO

CREATE INDEX [IX_swf_workProcessPermission_ProcessName] ON [swf_workProcessPermission] ([ProcessName]);
GO

CREATE INDEX [IX_swf_WorkTaskCommand_WorkTaskId] ON [swf_WorkTaskCommand] ([WorkTaskId]);
GO

INSERT INTO [swf_efmigrationshistory] ([MigrationId], [ProductVersion])
VALUES (N'20250616060729_init3', N'8.0.10');
GO

COMMIT;
GO

