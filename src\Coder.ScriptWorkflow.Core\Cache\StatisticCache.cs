﻿using System;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Stores;
using Microsoft.Extensions.Caching.Distributed;

namespace Coder.ScriptWorkflow.Cache;

public class StatisticCache
{
    private readonly IDistributedCache _cache;
    private readonly DistributedCacheEntryOptions _cacheOption;

    /// <summary>
    /// </summary>
    /// <param name="cache"></param>
    public StatisticCache(IDistributedCache cache)
    {
        _cache = cache;
        _cacheOption = new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15)
        };
    }

    /// <summary>
    ///     获取关于用户的在途工单统计数字
    /// </summary>
    /// <param name="user"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    public async Task<int> TryOnProcessing(string user, IProcessInstanceStore store, bool forceBuild)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        var key = $"pi-processing-{user}";
        var bytes = await _cache.GetAsync(key);
        if (bytes != null && !forceBuild) return BitConverter.ToInt32(bytes);

        var total = await store.CountAsync(user,
            new[] { ProcessInstanceStatus.Created, ProcessInstanceStatus.Processing });
        SetProcessing(user, total);
        return total;
    }

    public void SetProcessing(string user, int total)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        var key = $"pi-processing-{user}";
        var bytes = BitConverter.GetBytes(total);
        _cache.Set(key, bytes, _cacheOption);
    }

    public async Task<int> ProcessInstanceAllCount(string user, IProcessInstanceStore store, bool forceBuild)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        var key = $"pi-all-{user}";
        var bytes = await _cache.GetAsync(key);
        if (bytes != null && !forceBuild) return BitConverter.ToInt32(bytes);

        var total = await store.CountAsync(user,
            new[]
            {
                ProcessInstanceStatus.Created, ProcessInstanceStatus.Processing, ProcessInstanceStatus.Completed,
                ProcessInstanceStatus.Suspend
            });
        SetAllProcessCount(user, total);
        return total;
    }

    public void SetAllProcessCount(string user, int total)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        var key = $"pi-all-{user}";
        var bytes = BitConverter.GetBytes(total);
        _cache.Set(key, bytes, _cacheOption);
    }
}