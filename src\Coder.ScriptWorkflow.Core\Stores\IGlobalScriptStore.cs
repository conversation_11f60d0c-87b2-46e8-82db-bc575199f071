﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IGlobalScriptStore
{
    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GlobalScriptItem> GetAsync(int id);

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    Task DeleteAsync(int id);

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate(GlobalScriptItem workTask);

    /// <summary>
    ///     保存保存
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<IEnumerable<GlobalScriptItem>> ListAsync(string name = null);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GlobalScriptViewModel>> ListAllAsync();
}