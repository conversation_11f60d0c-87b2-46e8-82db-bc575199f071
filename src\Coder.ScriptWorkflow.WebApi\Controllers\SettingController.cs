﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Settings;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Settings;
using Coder.WebHttpClient;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流定义接口
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize]
public class SettingController : Controller
{
    private readonly IUserSettingStore _userSettingStore;

    /// <summary>
    /// </summary>
    /// <param name="userSettingStore"></param>
    public SettingController(IUserSettingStore userSettingStore)
    {
        _userSettingStore = userSettingStore;
    }


    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    [HttpPost("save-make-tags-script")]
    public async Task<ResponseMessage> Save([FromBody] SwfMakeTagsSettingSubmit submit)
    {
        var setting = submit.Id != 0
            ? await _userSettingStore.GetMakeTagSetting()
            : new SwfMakeTagsSetting();


        submit.Fill(setting);
        _userSettingStore.AddOrUpdate(setting);
        await _userSettingStore.SaveChangesAsync();

        return new ResponseMessage("保存成功。", false);
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("make-tags-script")]
    public async Task<SwfMakeTagsSettingSubmit> Get()
    {
        var entity = await _userSettingStore.GetMakeTagSetting();
        if (entity == null) return new SwfMakeTagsSettingSubmit { Id = 0, Script = "" };
        return entity.ToViewModel();
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [HttpGet("tags")]
    public async Task<IEnumerable<string>> GetTags([FromServices] GlobalScriptContext context)
    {
        var entity = await _userSettingStore.GetMakeTagSetting();
        var result = entity.MakeUserTags(User, context);
        return result;
    }
}