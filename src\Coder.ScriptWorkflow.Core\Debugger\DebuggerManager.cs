﻿using System;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow.Debugger;

/// <summary>
/// </summary>
public class DebuggerManager : IDisposable
{
    private readonly IDebuggerPusher _pusher;


    // private readonly IDebuggerPusher _puhser;
    /// <summary>
    /// </summary>
    /// <param name="pusher"></param>
    public DebuggerManager(IDebuggerPusher pusher)
    {
        _pusher = pusher;
    }

    public void Dispose()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="message"></param>
    public void SendMessage(ProcessInstance processInstance, string message, DebuggerType type = DebuggerType.Info)
    {
        _pusher.SendMessageAsync(processInstance.Id, new DebuggerInfo(message)
        {
            Form = processInstance.Form,
            Type = type
        }).Wait();
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    public void SendMessage(IWorkflowContext context, string message, DebuggerType type = DebuggerType.Info)
    {
        if (context.IsDryRun)
            return;
        var processInstance = context.ProcessInstance;
        var node = context.CurrentNode;

        _pusher.SendMessageAsync(processInstance.Id, new DebuggerInfo(node, message)
        {
            Form = processInstance.Form,
            Type = type
        }).Wait();
    }

    /// <summary>
    /// </summary>
    /// <param name="eventName"></param>
    /// <param name="message"></param>
    public void SendMessage(IWorkflowContext context, string eventName, string message,
        DebuggerType type = DebuggerType.Info)
    {
        if (context.IsDryRun)
            return;
        var processInstance = context.ProcessInstance;
        var node = context.CurrentNode;

        _pusher.SendMessageAsync(processInstance.Id, new DebuggerInfo(node, message, eventName)
        {
            Form = processInstance.Form,
            Type = type
        }).Wait();
    }


    /// <summary>
    /// </summary>
    /// <param name="errorMessage"></param>
    /// <param name="eventName"></param>
    /// <param name="code"></param>
    /// <param name="codeLine"></param>
    public void SendCodeError(IWorkflowContext context, string errorMessage, string eventName, string code,
        ScriptErrorLocation codeLine)
    {
        if (context.IsDryRun)
            return;
        var processInstance = context.ProcessInstance;
        var node = context.CurrentNode;

        _pusher.SendMessageAsync(processInstance.Id, new DebuggerInfo(node, errorMessage, eventName)
        {
            Code = code,
            Codeline = codeLine.ToString(),
            Form = processInstance.Form,
            Type = DebuggerType.Error
        }).Wait();
    }


    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="message"></param>
    /// <param name="eventName"></param>
    /// <param name="plugin"></param>
    /// <param name="code"></param>
    /// <param name="codeLine"></param>
    public void SendPluginError(IWorkflowContext context, string message, string eventName, string plugin, string code,
        ScriptErrorLocation codeLine)
    {
        if (context.IsDryRun)
            return;
        var processInstance = context.ProcessInstance;
        var node = context.CurrentNode;

         _pusher.SendMessageAsync(processInstance.Id, new DebuggerInfo(node, message)
        {
            Code = code,
            Codeline = codeLine.ToString(),
            Plugin = plugin,
            Form = processInstance.Form,
            EventName = eventName,
            Type = DebuggerType.Error
        }).Wait();

    
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="ex"></param>
    /// <param name="messsagePrefix"></param>
    public void SendJsException(IWorkflowContext context, JsRuntimeException ex, string messsagePrefix = null)
    {
        var isPlugin = !string.IsNullOrEmpty(ex.PluginErrorMessage);


        var prefix = messsagePrefix != null ? messsagePrefix + "\r\n" : "";
        if (isPlugin)
        {
            context.Logger.LogError(context, WorkflowLogType.Plugin, ex.PluginErrorMessage);
            context.Debugger?.SendPluginError(context
                , prefix + ex.PluginErrorMessage
                , ex.PluginName ?? "未知插件"
                , ex.EventName
                , ex.Code, ex.Coordinates);
        }
        else
        {
            context.Debugger.SendCodeError(context
                , prefix + ex.Message
                , ex.EventName
                , ex.Code
                , ex.Coordinates
            );
        }
    }
}