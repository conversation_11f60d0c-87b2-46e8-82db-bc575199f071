﻿using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel.Translator;

public class ScriptDescitionSubmitTest
{
    [Fact]
    public void FllByScriptDecision()
    {
        var sp = new WorkProcess("name");
        var nextNode = new WorkTask("next", sp);
        var elseNode = new WorkTask("else", sp);
        var actual = new BoolScriptDecision(nextNode, elseNode);
        actual.Script = "var a=1";

        actual.Name = "判定器";
        actual.Id = 10;


        var target = actual.ToViewModel();

        Assert.Equal(target.ElseNodeName, elseNode.Name);
        Assert.Equal(target.NextNodeName, nextNode.Name);

        Assert.Equal(actual.Id, target.Id);
        Assert.Equal(actual.Script, target.Script);
    }
}