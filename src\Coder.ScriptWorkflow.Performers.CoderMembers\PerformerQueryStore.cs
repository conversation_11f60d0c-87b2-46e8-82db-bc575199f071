﻿using System.Collections.Concurrent;
using Coder.Member;
using Coder.Member.Clients;
using Coder.Member.ViewModels.Roles;
using Coder.Member.ViewModels.Users;
using Coder.Orgs.Clients;
using Coder.Orgs.ViewModels.Orgs;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.Performers.CoderMembers;

/// <summary>
/// </summary>
public class PerformerQueryStore : IPerformerQueryStore
{
    private readonly IOrgClient _orgClient;
    private readonly IRoleClient _roleClient;
    private readonly IUserClient _userClient;


    /// <summary>
    /// </summary>
    /// <param name="userClient"></param>
    /// <param name="orgClient"></param>
    public PerformerQueryStore(IUserClient userClient, IRoleClient roleClient, IOrgClient orgClient)
    {
        _userClient = userClient;
        _roleClient = roleClient;

        _orgClient = orgClient;
    }

    /// <summary>
    /// </summary>
    /// <param name="roleNames"></param>
    /// <returns></returns>
    public async Task<IEnumerable<AssignUser>> GetByRolesAsync(string[] roleNames)
    {
        var r = (await _userClient.GetByRolesAsync(roleNames)).Data;
        return r.Select(_ => new AssignUser
        {
            Name = _.Name,
            UserName = _.UserName
        });
    }

    /// <summary>
    /// </summary>
    /// <param name="orgNames"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByOrgAsync(string[] orgNames)
    {
        var queue = new Queue<string>(orgNames);

        var resultQueue = new ConcurrentQueue<AssignUser>();
        var result = Task.Run(() =>
        {
            var a = new List<Task>();
            while (queue.Count > 0)
            {
                var claimTask = _userClient.GetUsersByClaimAsync(UserDefined.ClaimOrg, queue.Dequeue()).ContinueWith(
                    result =>
                    {
                        var r = result.Result.Data.Select(_ => new AssignUser
                        {
                            Name = _.Name,
                            UserName = _.UserName
                        });
                        foreach (var user in r) resultQueue.Enqueue(user);
                    });

                a.Add(claimTask);
            }

            Task.WaitAll(a.ToArray());
            return (IEnumerable<AssignUser>)resultQueue;
        });


        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="users"></param>
    /// <returns></returns>
    public async Task<IEnumerable<AssignUser>> GetByUserNamesAsync(IEnumerable<string> users)
    {
        var t = await _userClient.GetSimpleUsersAsync(users.ToArray());

        var re = t.Data.Select(_ => new AssignUser
        {
            Name = _.Name,
            UserName = _.UserName
        });

        return re;
    }

    public async Task<IEnumerable<Performer>> SearchAsync(PerformerSearcher searcher)
    {
        switch (searcher.Type)
        {
            case PerformerType.User:
                var r = await _userClient.ListAsync(new SimpleUserSearch
                {
                    Name = searcher.Name,
                    Page = searcher.Page,
                    PageSize = searcher.PageSize
                });
                return r.Data.Select(_ => new Performer
                {
                    Type = PerformerType.User,
                    Name = _.Name,
                    Key = _.UserName
                });

            case PerformerType.Role:
                var role = await _roleClient.ListAsync(new RoleSearch
                {
                    Name = searcher.Name,
                    Page = searcher.Page,
                    PageSize = searcher.PageSize
                });
                return role.Data.Select(_ => new Performer
                {
                    Type = PerformerType.Role,
                    Name = _.Comment,
                    Key = _.Name
                });

            case PerformerType.Org:
            {
                var orgs = await _orgClient.ListAsync(new OrgSearcher
                {
                    Name = searcher.Name,
                    Page = searcher.Page,
                    PageSize = searcher.PageSize
                });
                return orgs.Data.Select(_ => new Performer
                {
                    Type = PerformerType.Org,
                    Name = _.Name,
                    Key = _.Path
                });
            }
        }

        throw new NotImplementedException();
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<int> CountAsync(PerformerSearcher searcher)
    {
        switch (searcher.Type)
        {
            case PerformerType.User:
                var userCount = await _userClient.CountAsync(new SimpleUserSearch
                {
                    Name = searcher.Name,
                    Page = searcher.Page,
                    PageSize = searcher.PageSize
                });
                return userCount.Data;

            case PerformerType.Role:
                var roleResult = await _roleClient.CountAsync(new RoleSearch
                {
                    Description = searcher.Name,
                    Page = searcher.Page,
                    PageSize = searcher.PageSize
                });
                return roleResult.Data;

            case PerformerType.Org:
            {
                var orgCount = await _orgClient.CountAsync(new OrgSearcher
                {
                    Name = searcher.Name,
                    Page = searcher.Page,
                    PageSize = searcher.PageSize
                });
                return orgCount.Data;
            }
        }

        throw new NotImplementedException();
    }
}