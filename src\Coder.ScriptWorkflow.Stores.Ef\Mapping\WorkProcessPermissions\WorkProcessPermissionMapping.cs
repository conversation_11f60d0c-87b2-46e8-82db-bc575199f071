﻿using Coder.ScriptWorkflow.Permissions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.WorkProcessPermissions;

/// <summary>
/// </summary>
internal class WorkProcessPermissionMapping : IEntityTypeConfiguration<WorkProcessPermission>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    internal WorkProcessPermissionMapping(string prefix)
    {
        _prefix = prefix;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<WorkProcessPermission> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.Property(_ => _.ProcessName).HasMaxLength(100);
        builder.Property(_ => _.ManageRoles).HasColumnName("ManageRole")
            .HasMaxLength(400);
        builder.ToTable($"{_prefix}_workProcessPermission");

        builder.HasMany(_ => _.Performers);

        builder.HasIndex(_ => _.ProcessName);
    }
}