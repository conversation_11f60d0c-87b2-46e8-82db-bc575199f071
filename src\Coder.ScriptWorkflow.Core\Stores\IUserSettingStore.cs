﻿using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Settings;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IUserSettingStore
{
    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task Delete(int id);

    /// <summary>
    /// </summary>
    /// <param name="setting"></param>
    void AddOrUpdate<TSetting>(TSetting setting) where TSetting : SwfSetting;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

   
    Task<SwfMakeTagsSetting> GetMakeTagSetting();
}