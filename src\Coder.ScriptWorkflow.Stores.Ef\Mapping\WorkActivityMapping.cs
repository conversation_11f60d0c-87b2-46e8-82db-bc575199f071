﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
///     Workactivity Mapping类。
/// </summary>
internal class WorkActivityMapping : IEntityTypeConfiguration<WorkActivity>

{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix">table的前序。</param>
    public WorkActivityMapping(string prefix)
    {
        _prefix = prefix;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<WorkActivity> builder)
    {
        builder.HasKey(_ => _.Id);

        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.Property(_ => _.TaskCreatingGroup).HasMaxLength(32);
        builder.HasOne(_ => _.ProcessInstance);
        builder.Property(_ => _.Priority).HasDefaultValue(Priority.Normal);
        builder.HasOne(_ => _.WorkTask);
        builder.Property(_ => _.Status).IsConcurrencyToken();
        builder.Property(_ => _.Comment).HasMaxLength(1000);
        builder.Property(_ => _.DisposeUser).HasMaxLength(50);
        builder.Property(_ => _.DisposeUserName).HasMaxLength(50);

        builder.ToTable($"{_prefix}_WorkActivity");

        builder.Property(_ => _.AssignPerformers).HasMaxLength(1000).HasConversion(performers => To(performers), str => From(str));

        builder.Property(_ => _.DisposeTime);
        builder.Property(_ => _.AssignTime);
        builder.Property(_ => _.CreateTime);

        builder.Property(f => f.TimeSpan).ValueGeneratedOnAddOrUpdate().IsConcurrencyToken();
    }

    /// <summary>
    /// </summary>
    /// <param name="performers"></param>
    /// <returns></returns>
    private static string To(IEnumerable<Performer> performers)
    {
        var list = new List<string>();
        foreach (var performer in performers)
        {
            var str = $"{(int)performer.Type}:{performer.Key}:{performer.Name}";
            list.Add(str);
        }

        return string.Join(';', list.ToArray());
    }

    /// <summary>
    /// </summary>
    /// <param name="v"></param>
    /// <returns></returns>
    private static IEnumerable<Performer> From(string v)
    {
        var list = new List<Performer>();
        foreach (var performer in v.Split(';', StringSplitOptions.RemoveEmptyEntries))
        {
            var ary = performer.Split(':');
            var a = new Performer
            {
                Type = (PerformerType)Convert.ToInt32(ary[0]),
                Key = ary[1]
            };
            if (ary.Length > 2) a.Name = ary[2];
            list.Add(a);
        }

        return list;
    }
}