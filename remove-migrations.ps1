$projFolder = "Coder.ScriptWorkflow.WebApi"
$projMigrationPrefx = "Coder.ScriptWorkflow.Migrations"

cd src/${projFolder}
dotnet tool restore

$env:DB_TYPE="MYSQL"
dotnet ef  migrations remove  --force --project ../${projMigrationPrefx}.Mysql

$env:DB_TYPE="MSSQL"
dotnet ef  migrations remove --force --project ../${projMigrationPrefx}.Mssql 

$env:DB_TYPE="SQLITE"
dotnet ef migrations remove  --force --project ../${projMigrationPrefx}.Sqlite

$env:DB_TYPE="DM"
dotnet ef migrations remove  --force --project ../${projMigrationPrefx}.DM
