﻿using Coder.ScriptWorkflow.Nodes;

namespace Coder.ScriptWorkflow.Decisions.BooleanDecisions;

public class ConditionSetting
{
    /// <summary>
    ///     节点
    /// </summary>
    public virtual Node Node { get; set; }

    /// <summary>
    /// 匹配的值
    /// </summary>
    public string MatchValue { get; set; }


    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     id
    /// </summary>
    public int Id { get; set; }
}