﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.ViewModels.Permissions;

/// <summary>
///     WorkActivity权限相关
/// </summary>
public interface IWorkActivityWithPermission
{
    /// <summary>
    ///     是否能够处理当前的工作活动
    /// </summary>
    bool CanDisposeWorkActivity { get; set; }

    /// <summary>
    ///     是否允许放弃。
    /// </summary>
    public bool CanGiveUpWorkActivity { get; set; }

    /// <summary>
    ///     是否能够接受本任务
    /// </summary>
    bool CanAcceptWorkActivity { get; set; }

    /// <summary>
    ///     获取当前处理人。
    /// </summary>
    /// <returns></returns>
    string GetDisposeUser();

    /// <summary>
    /// </summary>
    /// <returns></returns>
    WorkActivityStatus GetWorkActivityStatus();

    /// <summary>
    ///     工作任务是否能够被当前处理人放弃。
    /// </summary>
    /// <returns></returns>
    bool GetWorkTaskGiveUpSetting();

    /// <summary>
    /// </summary>
    /// <returns></returns>
    IEnumerable<Performer> GetPerformers();
}