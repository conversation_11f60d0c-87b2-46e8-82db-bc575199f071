﻿using Coder.ScriptWorkflow.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class EndNodeMapping : IEntityTypeConfiguration<EndNode>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<EndNode> builder)
    {
        builder.HasBaseType<Node>();
    }
}