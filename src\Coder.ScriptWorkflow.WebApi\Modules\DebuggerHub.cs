﻿using Coder.ScriptWorkflow.Debugger;
using Microsoft.AspNetCore.SignalR;

namespace Coder.ScriptWorkflow.WebApi.Modules;
/// <summary>
/// 
/// </summary>
public class DebuggerHub : Hub
{
    private readonly DebuggerSignalRContext _context;
    private readonly WorkflowManager _manager;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="context"></param>
    /// <param name="manager"></param>
    public DebuggerHub(DebuggerSignalRContext context, WorkflowManager manager)
    {
        _context = context;
        _manager = manager;
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="processInstance"></param>
    public void Join(int processInstance)
    {
        Groups.AddToGroupAsync(Context.ConnectionId, "pi_" + processInstance).Wait();

        var pi = _manager.GetById(processInstance);
        _context.SendMessageAsync(processInstance, new DebuggerInfo("开始调试工作实例" + processInstance)
        {
            Type = DebuggerType.Info,
            Form = pi.Form
        }).Wait();
    }
}