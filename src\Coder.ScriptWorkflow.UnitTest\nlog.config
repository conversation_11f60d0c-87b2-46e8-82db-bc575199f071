﻿<?xml version="1.0" encoding="utf-8"?>

<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="/var/log/scriptWorkflow/internal-nlog.txt">

	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore" />
		<add assembly="Coder.Logger.NLog.Redis" />
	</extensions>

	<!-- the targets to write to -->
	<targets>
		<!-- write logs to file  -->
		<target xsi:type="File" name="allfile" fileName="/var/log/scriptWorkflow/nlog-all-${shortdate}.log"
		        maxArchiveDays="30" archiveAboveSize="10240"
		        layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />

		<!-- another file log, only own logs. Uses some ASP.NET core renderers -->
		<target xsi:type="File" name="ownFile-web" fileName="/var/log/scriptWorkflow/nlog-own-${shortdate}.log"
		        maxArchiveDays="30" archiveAboveSize="10240"
		        layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />

		<target xsi:type="Redis" name="redis" host="redis" port="6379" serviceName="Coder.ScriptWorkflow"
		        serviceId="Coder.ScriptWorkflow-01" channelName="logger_channel"
		        layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />

	</targets>

	<!-- rules to map from logger name to target -->
	<rules>
		<!--All logs, including from Microsoft-->
		<logger name="*" minlevel="Trace" writeTo="allfile" />
		<logger name="*" minlevel="Debug" writeTo="redis" />
		<!--Skip non-critical Microsoft logs and so log only own logs-->
		<logger name="Microsoft.*" maxlevel="Info" final="true" />
		<!-- BlackHole without writeTo -->
		<logger name="*" minlevel="Trace" writeTo="ownFile-web" />
	</rules>
</nlog>