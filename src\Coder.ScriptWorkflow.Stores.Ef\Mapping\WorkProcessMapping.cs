﻿using System;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class WorkProcessMapping : IEntityTypeConfiguration<WorkProcess>
{
    private const string DateFormat = "yyyy-MM-dd HH:mm:ss";
    private readonly bool _forSqlite;
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    /// <param name="isSqlite"></param>
    internal WorkProcessMapping(string prefix, bool isSqlite)
    {
        _prefix = prefix;
        _forSqlite = isSqlite;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<WorkProcess> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.Property(_ => _.Name).HasMaxLength(100);
        builder.ToTable($"{_prefix}_workProcess");
        builder.Property(_ => _.Version);
        builder.HasOne(_ => _.OnComplete);
        builder.HasOne(_ => _.OnCancel);
        builder.HasOne(_ => _.OnStart);
        builder.Property(_ => _.Enable);
        builder.Property(_ => _.Prefix).HasMaxLength(10);
        builder.Property(_ => _.Configurations).HasColumnType("text").HasJsonValueConversion();
        builder.Property(_ => _.GlobalScript).HasColumnType("text").HasJsonValueConversion();
        builder.Property(_ => _.FormTypeScriptDefined).HasColumnType("text");
        builder.Property(_ => _.FormDesign).HasColumnType("text");
        builder.Property(_ => _.FormManageDesign).HasColumnType("text");
        builder.Property(_ => _.LogLevel);
        builder.Property(_ => _.Plugins).HasJsonValueConversion().HasMaxLength(500);
        builder.Property(_ => _.Creator).HasMaxLength(50);
        builder.Property(_ => _.CanBeDeleteWorkActivityCount);

        builder.Property(_ => _.Comment).HasComment("备注").HasMaxLength(400);
        builder.Property(_ => _.Abbr).HasComment("简称").HasMaxLength(100);
        builder.Property(_ => _.Icon).HasComment("图标").HasMaxLength(50);

        builder.Property(_ => _.Group).HasMaxLength(50);

        var createTime = builder.Property(_ => _.UpdateTimeOffset);

        builder.HasIndex("Name", "Version").IsUnique();
        if (_forSqlite)
            createTime.HasConversion(f => f != null ? f.Value.ToString(DateFormat) : null,
                strDate => strDate == null ? null : DateTimeOffset.Parse(strDate));
    }
}