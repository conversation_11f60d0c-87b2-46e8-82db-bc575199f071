﻿
interface JsHttpContractClass {

    Save(): number;

    GetContractCode(submit: GetContractCodeSubmit): string;

    GetCContractCode(submit: GetContractCodeSubmit): string;

    SaveWorkload(startDate: string, endDate: string): number;

    ContractLock(submit: ContractLockSubmit);

    ContractWorkloadLock(submit: ContractLockSubmit);

    CreateReceiveOrader(): number;
    SaveReceiveOrader(): number;

    CreatePayOrader(): number;
    SavePayOrader(): number;

    SaveFromGroup(submit: GroupSubmit): number;
    
    BlockBlackList(): number;
}

interface ContractOrderSubmit {
    Id: number;
    Code: string;
    Name: string;
    ContractType: number;
    OppositeName: string;
    PlanBookDate: Date;
    ProjectName: string;
    ServiceTerm: string;
    ServiceContent: string;
    ServiceShip: string;
    ContractPrice: string;
    ContractTotal: number;
    ApplyDate: Date;
    OrderCloseDate: Date;
    OrgPath: string;
    OrderNo: string;
}

interface GetContractCodeSubmit {
    Year: string;
    ContractType: string;
    Org: string;
}

interface ContractLockSubmit {
    Code: string;
    OrderNo: string;
}

interface GroupSubmit {
    ContractCode: string;
    ContractGroupId: number;
    OrderNo: string;
    OrderUser: string;
    OrderDate: Date;
}

declare const jsContract: JsHttpContractClass;