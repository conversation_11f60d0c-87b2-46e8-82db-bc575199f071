﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IWorkProcessStore
{
    /// <summary>
    /// </summary>
    IQueryable<WorkProcess> WorkProcesses { get; }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    void AddOrUpdate([NotNull] WorkProcess workProcess);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    void Delete(WorkProcess workProcess);


    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkProcess Get(int id);

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkProcess GetByEffectName([NotNull] string name);

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    IEnumerable<WorkProcess> ListByName(string name);


    /// <summary>
    /// </summary>
    /// <param name="workProcessName"></param>
    /// <param name="version"></param>
    /// <param name="notIncludeId">排除这个Id进行检查</param>
    /// <returns></returns>
    bool Exist(string workProcessName, int? version, int? notIncludeId);

    /// <summary>
    ///     通过Name和Version获取
    /// </summary>
    /// <param name="name"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkProcess GetByNameVersion(string name, int version);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<IEnumerable<WorkProcess>> List(WorkProcessListSearcher searcher);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<int> Count(WorkProcessListSearcher searcher);

    /// <summary>
    /// </summary>
    /// <param name="submitPrefix"></param>
    /// <param name="exclude"></param>
    /// <returns></returns>
    bool ExistNumberPrefix(string submitPrefix, string exclude, int id);

   


    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<WorkProcessScript> GetScriptAsync(int id);

    /// <summary>
    /// </summary>
    /// <param name="scriptSubmit"></param>
    /// <returns></returns>
    void SaveScript(WorkProcessScript scriptSubmit);
}