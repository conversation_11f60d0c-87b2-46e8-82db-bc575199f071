﻿using System;
using System.Collections.Concurrent;
using System.Linq;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ValueGeneration;

namespace Coder.ScriptWorkflow.NumberGenerators;

internal class NumberFrom20200101Generator : ValueGenerator<string>
{
    private ConcurrentDictionary<string, int> _indexPool = new();

    public override bool GeneratesTemporaryValues { get; }

    public override string Next(EntityEntry entry)
    {
        lock (this)
        {
            var pi = (ProcessInstance)entry.Entity;
            var prefix = pi.WorkProcess.Prefix;
            //重 2020-01-01 到现在的秒。
            return prefix + (DateTimeOffset.Now.ToUnixTimeSeconds() - 1577808000);
        }
    }
}

/// <summary>
/// </summary>
internal class NumberGenerator : ValueGenerator<string>
{
    /// <summary>
    /// </summary>
    public int LengthOfEveryDay { get; set; } = 5;

    /// <summary>
    /// </summary>
    protected int? CurrentIndex { get; set; }

    /// <summary>
    /// </summary>
    public override bool GeneratesTemporaryValues { get; } = false;

    /// <summary>
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    public static int ToInt32(string number, string prefix)
    {
        var start = prefix.Length + 8;
        return Convert.ToInt32(number.Substring(start).TrimStart('0'));
    }

    /// <summary>
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    public string ToString(int number, string prefix)
    {
        var numberStr = number.ToString().PadLeft(LengthOfEveryDay, '0');
        return $"{prefix}{DateTime.Now:yyyyMMdd}{numberStr}";
    }

    /// <summary>
    /// </summary>
    /// <param name="entry"></param>
    /// <returns></returns>
    protected int GetLastWorkflowInstance(EntityEntry entry)
    {
        lock (this)
        {
            var now = DateTime.Today.AddDays(1);
            var yesterday = DateTime.Today.AddSeconds(-1);
            var pi = (ProcessInstance)entry.Entity;
            var prefix = pi.WorkProcess.Prefix;
            var last = entry.Context.Set<ProcessInstance>()
                .OrderByDescending(f => f.Id)
                .FirstOrDefault(f =>
                    f.WorkProcess.Id == pi.WorkProcess.Id && f.CreateTime < now && f.CreateTime > yesterday);

            if (last != null)
                if (!string.IsNullOrEmpty(last.Number))
                    return ToInt32(last.Number, prefix);

            return 0;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="entry"></param>
    /// <returns></returns>
    public sealed override string Next(EntityEntry entry)
    {
        lock (this)
        {
            if (CurrentIndex == null) CurrentIndex = GetLastWorkflowInstance(entry);

            CurrentIndex++;
            var pi = (ProcessInstance)entry.Entity;
            var prefix = pi.WorkProcess.Prefix;
            return ToString(CurrentIndex.Value, prefix);
        }
    }
}