﻿namespace Coder.ScriptWorkflow.ViewModels.Permissions;

/// <summary>
/// </summary>
public interface IProcessInstanceWithPermission : IWorkProcessWithPermission
{
    /// <summary>
    ///     是否
    /// </summary>
    bool CanDeleteProcessInstance { get; set; }

    /// <summary>
    ///     是否为实例创建者。
    /// </summary>
    bool IsInstanceCreator { get; set; }


    /// <summary>
    ///     是否为抄抄送用户
    /// </summary>
    public bool IsCCUser { get; set; }

    /// <summary>
    ///     读
    /// </summary>
    public bool HasRead { get; set; }

    /// <summary>
    ///     获取流程状态
    /// </summary>
    /// <returns></returns>
    ProcessInstanceStatus GetProcessInstanceStatus();

    /// <summary>
    ///     获取流程创建这信息。
    /// </summary>
    /// <returns></returns>
    string GetProcessInstanceCreator();
}