﻿using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;
using Newtonsoft.Json;

namespace Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

/// <summary>
/// </summary>
[JsonConverter(typeof(AssignSubmitJsonConverter))]
public abstract class AssignSubmit
{
    /// <summary>
    /// </summary>
    public AssignSubmit()
    {
        Type = $"{GetType().FullName}, Coder.ScriptWorkflow.Abstractions";
    }


    /// <summary>
    ///     获取或设置id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     派发给符合条件的用户。
    /// </summary>
    public virtual AssignScopeType AssignScopeType { get; set; } = AssignScopeType.AllOfThem;


    /// <summary>
    /// </summary>
    [JsonProperty("$type")]
    public string Type { get; set; }


    /// <summary>
    ///     验证分配器
    /// </summary>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public abstract bool Validate(out string errorMessage);
}