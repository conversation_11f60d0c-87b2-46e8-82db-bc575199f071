﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Nodes;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
///     节点
/// </summary>
public interface INodeStore
{
    /// <summary>
    ///     删除节点
    /// </summary>
    /// <param name="id"></param>
    void Delete(int id);

    /// <summary>
    ///     通过id获取
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    Node GetById(int id);

    /// <summary>
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    Task<T> GetByIdAsync<T>(int id) where T : Node;

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate([NotNull] Node workTask);

    /// <summary>
    ///     保存
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    ///     获取与workProcess 相关的 节点
    /// </summary>
    /// <param name="workProcess"></param>
    /// <returns></returns>
    Task<IEnumerable<Node>> GetNodesByWorkProcessAsync([NotNull] WorkProcess workProcess);

    /// <summary>
    ///     删除node，之后需要调用SaveChangesAsync
    /// </summary>
    /// <param name="removeNodes"></param>
    void Remove(IEnumerable<Node> removeNodes);
    /// <summary>
    /// 获取节点
    /// </summary>
    /// <param name="wp"></param>
    /// <param name="nodeName"></param>
    /// <returns></returns>
    Task<Node> GetByNodeAsync(WorkProcess wp, string nodeName);
}