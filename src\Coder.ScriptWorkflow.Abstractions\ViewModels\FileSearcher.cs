﻿namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
/// </summary>
public class FileSearcher
{
    private int _pageSize;

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public string ProcessInstanceNumber { get; set; }

    /// <summary>
    ///     页码
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    ///     当页显示多少个
    /// </summary>
    public int PageSize
    {
        get => _pageSize;
        set

        {
            if (value > 200 || value <= 0) value = 50;
            _pageSize = value;
        }
    }
}