﻿

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
/// </summary>
public class CreateProcessInstanceResult 
{
    /// <summary>
    /// </summary>
    public string CreateUser { get; set; }

    /// <summary>
    /// </summary>
    public ProcessInstanceStatus ProcessInstanceStatus { get; set; }

    /// <summary>
    /// </summary>
    public StartResult StartResult { get; set; }
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; }
    /// <summary>
    /// 成功创建。
    /// </summary>
    public bool Success { get; set; } = true;
    /// <summary>
    /// 
    /// </summary>
    public int ProcessInstanceId { get; set; }
}