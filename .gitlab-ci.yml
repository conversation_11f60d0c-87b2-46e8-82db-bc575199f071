stages:
    - test    
    - deploy
        

unittest:
  image: zhcoder-docker-registry.com:8000/builder/dotnet_sdk:6_and_8
  stage: test
  after_script:  #采用sqlite模拟db，执行完毕之后删除一下文件
    - 'rm *.db -rf'
  script:
    - dotnet --version
    - dotnet restore ./src/Coder.ScriptWorkflow.sln
    - cd ./src/Coder.ScriptWorkflow.UnitTest
    - dotnet test --test-adapter-path:. --logger:"junit;LogFilePath=..artifacts{assembly}-test-result.xml" --collect:"XPlat Code Coverage"
     # each reporttype must be a separate line, doesn't work together ; separated
    - ls -ld TestResults/*
    - dotnet tool install dotnet-reportgenerator-globaltool --tool-path ./
    - ls
    - ./reportgenerator "-reports:TestResults/*/coverage.cobertura.xml" "-targetdir:Reports_Coverage" -reportTypes:TextSummary;
    - ./reportgenerator "-reports:TestResults/*/coverage.cobertura.xml" "-targetdir:Reports_Coverage" -reportTypes:Html;
    - ls Reports_Coverage
    - cat ./Reports_Coverage/Summary.txt
    - echo 'End Summary'
  coverage: /Line coverage[sS].+%/
  artifacts:
    when: always
    paths:
      - ./src/Coder.ScriptWorkflow.UnitTest/TestResults/*test-result.xml
      - ./src/Coder.ScriptWorkflow.UnitTest/Reports_Coverage/
    reports:
      junit:
        - ./src/Coder.ScriptWorkflow.UnitTest/*test-result.xml
  tags:
    - docker


nuget:
    stage: deploy
    image: zhcoder-docker-registry.com:8000/builder/dotnet_sdk:6_and_8
    script:
        - cd src
        - 'dotnet restore --configfile="./nuget.config"'
        - 'dotnet build -c Release'
        - 'dotnet pack ./Coder.ScriptWorkflow.Abstractions/Coder.ScriptWorkflow.Abstractions.csproj -o ./nuget -c Release'
        - 'dotnet pack ./Coder.ScriptWorkflow.Core/Coder.ScriptWorkflow.Core.csproj -o ./nuget -c Release'
        - cd nuget
        - dotnet nuget list source
        - |
          for i in *.nupkg
            do 
               echo "dotnet nuget push $i -s $NUGET_PUBLISH_URL -k $NUGET_API_KEY  --skip-duplicate"
               dotnet nuget push $i -s $NUGET_PUBLISH_URL -k $NUGET_API_KEY  --skip-duplicate 
            done

    tags:
        - docker

docker-build-image: 
    stage: deploy
    tags:
        - shell
    before_script:
        - |
          if command -v apt-get >/dev/null 2>&1; then
             sudo apt-get update
             sudo apt-get install -y libxml2-utils
          # 检查 yum 是否可用（适用于 CentOS/RHEL）
          elif command -v yum >/dev/null 2>&1; then
             sudo yum install -y libxml2
          else
             echo "No supported package manager found!"
             exit 1
          fi
    script:
        - cd src
        - version=$(xmllint --xpath "//Version/text()" ./Coder.ScriptWorkflow.WebApi/Coder.ScriptWorkflow.WebApi.csproj 2>/dev/null)
        - echo "版本号 $version"
        - register=zhcoder-docker-registry.com:8000/coder
        - image="coder-script-workflow-webapi"
        - sudo docker buildx build --platform linux/amd64 -t $register/$image:$version --push -f ./Coder.ScriptWorkflow.WebApi/Dockerfile .