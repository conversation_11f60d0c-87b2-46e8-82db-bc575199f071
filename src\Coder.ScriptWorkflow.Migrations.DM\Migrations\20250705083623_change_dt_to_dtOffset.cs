﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.ScriptWorkflow.Migrations.DM.Migrations
{
    /// <inheritdoc />
    public partial class change_dt_to_dtOffset : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "SuspendTime",
                table: "swf_processInstance",
                type: "DATETIME WITH TIME ZONE",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "TIMESTAMP",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "StartTime",
                table: "swf_processInstance",
                type: "DATETIME WITH TIME ZONE",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "TIMESTAMP",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "FinishTime",
                table: "swf_processInstance",
                type: "DATETIME WITH TIME ZONE",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "TIMESTAMP",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreateTime",
                table: "swf_processInstance",
                type: "DATETIME WITH TIME ZONE",
                nullable: false,
                oldClrType: typeof(DateTime),
                oldType: "TIMESTAMP");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "SuspendTime",
                table: "swf_processInstance",
                type: "TIMESTAMP",
                nullable: true,
                oldClrType: typeof(DateTimeOffset),
                oldType: "DATETIME WITH TIME ZONE",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "StartTime",
                table: "swf_processInstance",
                type: "TIMESTAMP",
                nullable: true,
                oldClrType: typeof(DateTimeOffset),
                oldType: "DATETIME WITH TIME ZONE",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "FinishTime",
                table: "swf_processInstance",
                type: "TIMESTAMP",
                nullable: true,
                oldClrType: typeof(DateTimeOffset),
                oldType: "DATETIME WITH TIME ZONE",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreateTime",
                table: "swf_processInstance",
                type: "TIMESTAMP",
                nullable: false,
                oldClrType: typeof(DateTimeOffset),
                oldType: "DATETIME WITH TIME ZONE");
        }
    }
}
