﻿using Coder.ScriptWorkflow;
using Coder.ScriptWorkflow.Interceptors;
using Coder.VBen.Menu.Clients;
using Coder.VBen.Menu.HttpClients;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.VBenMenu.Interceptor;

/// <summary>
/// </summary>
public static class VBenMenuExtension
{
    /// <summary>
    /// </summary>
    /// <param name="option"></param>
    /// <param name="gateway"></param>
    /// <param name="prefix"></param>
    /// <returns></returns>
    public static WorkflowOptions AddVBenInterceptor(this WorkflowOptions option, string gateway, string prefix = "menu")
    {
        var host = gateway;
        if (!gateway.EndsWith('/'))
            host += "/";

        host += prefix;

        option.Services.AddTransient<MenuManager>();
        var services = option.Services;

        services.AddHttpClient(host, httpClient => { httpClient.BaseAddress = new Uri(host); });
        services.AddTransient(sp =>
        {
            var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
            var httpClient = httpClientFactory.CreateClient(host);
            return (IRouteClient)new RouteHttpClient(httpClient);
        }).AddTransient<IWorkProcessInterceptor, VbenWorkProcessInterceptor>();
        return option;
    }
}