﻿using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.ViewModels;
/// <summary>
/// </summary>
public class ResolveResult : StartResult
{
    /// <summary>
    /// </summary>
    public AssignScopeType AssignScopeType { get; set; }

    /// <summary>
    ///     结束
    /// </summary>
    public bool IsEnd { get; set; }
}
/// <summary>
/// 
/// </summary>
public class ProcessInstanceResolveResult
{
    public string Message { get; set; }


    public ProcessInstanceStatus? Status { get; set; }


    public bool Success { get; set; }
}