﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.Permissions.Cache;

/// <summary>
///     存储工作流定义缓存管理
/// </summary>
public class WorkProcessPermissionCacheManager
{
    private const string CachePrefix = "WP_Permission";
    private readonly IMemoryCache _cache;
    private readonly ILogger<WorkProcessPermissionCacheManager> _logger;
    private DateTime _lastLoad = DateTime.MinValue;
    private IDictionary<string, WorkProcessPermission> _mapping;

    /// <summary>
    /// </summary>
    /// <param name="cache"></param>
    /// <param name="logger"></param>
    public WorkProcessPermissionCacheManager(IMemoryCache cache, ILogger<WorkProcessPermissionCacheManager> logger)
    {
        _cache = cache;
        _logger = logger;
    }


    /// <summary>
    /// </summary>
    /// <param name="result"></param>
    /// <param name="user"></param>
    public void Set(IEnumerable<WorkProcessListItem> result, string user)
    {
        _cache.Set(CachePrefix + "_" + user, result);
    }

    private IDictionary<string, WorkProcessPermission> Init(IWorkProcessPermissionStore workProcessManager)
    {
        if (workProcessManager == null) throw new ArgumentNullException(nameof(workProcessManager));
        if (!_cache.TryGetValue(CachePrefix, out IDictionary<string, WorkProcessPermission> obj))
        {
            obj = new Dictionary<string, WorkProcessPermission>();

            //lazy load
            foreach (var p in workProcessManager.AllPermissions())
                obj.TryAdd(p.ProcessName, p);

            _cache.Set(CachePrefix, obj);
            _lastLoad = DateTime.Now;
        }

        return obj;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcessName"></param>
    /// <param name="workProcessManager"></param>
    /// <returns></returns>
    [return: MaybeNull]
    public WorkProcessPermission Get(string workProcessName, IWorkProcessPermissionStore workProcessManager)
    {
        if (workProcessName == null) throw new ArgumentNullException(nameof(workProcessName));
        if (workProcessManager == null) throw new ArgumentNullException(nameof(workProcessManager));
        _mapping ??= Init(workProcessManager);

        var result = _mapping.TryGetValue(workProcessName, out var value) ? value : null;

        if (result == null)
            if ((DateTime.Now - _lastLoad).TotalSeconds > 10)
            {
                _logger.LogWarning("WorkProcessPermission 为空，尝试充实加载");
                _mapping = Init(workProcessManager);
                result = _mapping.TryGetValue(workProcessName, out var value1) ? value1 : null;
                if (result == null)
                    _logger.LogWarning("尝试重新加载后， WorkProcessPermission 还是为空");
            }

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="permission"></param>
    /// <param name="workProcessManager"></param>
    public void Replace(WorkProcessPermission permission, IWorkProcessPermissionStore workProcessManager)
    {
        if (permission == null) throw new ArgumentNullException(nameof(permission));
        if (workProcessManager == null) throw new ArgumentNullException(nameof(workProcessManager));
        _mapping ??= Init(workProcessManager);
        if (_mapping.ContainsKey(permission.ProcessName))
            _mapping[permission.ProcessName] = permission;
        else
            _mapping.Add(permission.ProcessName, permission);
        _cache.Set(CachePrefix, _mapping);

    }

    /// <summary>
    /// </summary>
    /// <param name="workProcessManager"></param>
    /// <returns></returns>
    public IEnumerable<WorkProcessPermission> All(IWorkProcessPermissionStore workProcessManager)
    {
        if (workProcessManager == null) throw new ArgumentNullException(nameof(workProcessManager));
        var tags = Init(workProcessManager);
        return tags.Values;
    }
}