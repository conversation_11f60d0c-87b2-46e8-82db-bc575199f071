﻿using System.Collections.Generic;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest;

public class WorkTaskCommandScriptTest
{
    private static readonly UserViewModel AdminUserViewModel = new()
    {
        Name = "管理员",
        UserName = "admin"
    };

    [Fact]
    public void Test()
    {
        var pi = new ProcessInstance(new WorkProcess("wpName")
        {
            Configurations = new List<WorkflowConfiguration>
            {
                new()
                {
                    Name = "connection",
                    Value = "connection-test"
                }
            }
        }, "user");
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(
            new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            pi, new List<WorkTask>(), new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp),
            new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);

        var js = new
        {
            name = "测试",
            items = new[]
            {
                new { name = "测试。" }
            },
            content = @"
一个换行的测试。
"
        };

        pi.ReplaceForm(JsonConvert.SerializeObject(js));
        var wa = new WorkActivity(pi, new WorkTask("name", new WorkProcess("name")), Priority.Normal);

        var workTaskCommand = new WorkTaskScriptCommand();

        workTaskCommand.Script = @"
processInstance.Form.items[0].name=cfg.Get(""connection"")";

        workTaskCommand.Invoke(workflowContext, wa);
        var target = JsonConvert.DeserializeObject<JObject>(pi.Form);

        Assert.Equal(js.name, target["name"].Value<string>());

        var ary = target["items"].Value<JArray>();
        var aryItem1 = ary[0].Value<JObject>();
        Assert.Equal("connection-test", aryItem1["name"].Value<string>());
    }

    [Fact]
    public void TestNoneExecute()
    {
        var pi = new ProcessInstance(new WorkProcess("wpName"), "user");
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            pi, new List<WorkTask>(), new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp)
            , new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);

        var workTaskCommand = new WorkTaskScriptCommand();
        var wa = new WorkActivity
        {
            WorkTask = new WorkTask("名称", pi.WorkProcess)
        };
        workTaskCommand.Invoke(workflowContext, wa);
    }
}