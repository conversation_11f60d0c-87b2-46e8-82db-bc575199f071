﻿using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Coder.ScriptWorkflow.WorkTaskCommands;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.WorkCommands;

/// <summary>
/// </summary>
internal class PreviousWorkTaskCommandTranslator : WorkTaskCommandTranslator
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected override WorkTaskCommand CreateEntity()
    {
        return new PreviousWorkTaskCommand();
    }

    protected override WorkTaskCommandSubmit CreateViewModel()
    {
        return new PreviousCommandSubmit();
    }
}