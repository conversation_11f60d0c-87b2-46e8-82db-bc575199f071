﻿// <auto-generated />
using System;
using Coder.ScriptWorkflow;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Coder.ScriptWorkflow.Migrations.Sqlite.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250616060733_init3")]
    partial class init3
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true);

            modelBuilder.Entity("Coder.ScriptWorkflow.Assigners.Assigner", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AssignScopeType")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("swf_assigner", (string)null);

                    b.HasDiscriminator().HasValue("Assigner");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.BooleanDecisions.ConditionSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ConditionDecisionId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(300)
                        .HasColumnType("TEXT");

                    b.Property<string>("MatchValue")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("匹配的字符串");

                    b.Property<int?>("NodeId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ConditionDecisionId");

                    b.HasIndex("NodeId");

                    b.ToTable("swf_condition_decision", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Logger.WorkflowLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("CreateTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("LogLevel")
                        .HasColumnType("INTEGER");

                    b.Property<string>("NodeName")
                        .HasMaxLength(64)
                        .HasColumnType("TEXT")
                        .HasComment("节点名称");

                    b.Property<string>("PluginName")
                        .HasMaxLength(30)
                        .HasColumnType("TEXT");

                    b.Property<int>("ProcessInstanceId")
                        .HasColumnType("INTEGER")
                        .HasComment("工单id");

                    b.Property<string>("ProcessInstanceNumber")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("工单号");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("TEXT");

                    b.Property<int>("Version")
                        .HasColumnType("INTEGER")
                        .HasComment("工作流定义版本");

                    b.Property<int?>("WorkActivityId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("WorkProcessName")
                        .HasMaxLength(64)
                        .HasColumnType("TEXT")
                        .HasComment("工作流名称");

                    b.HasKey("Id");

                    b.HasIndex("ProcessInstanceNumber");

                    b.ToTable("swf_workflowLog", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Nodes.Node", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("Auto")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(32)
                        .HasColumnType("TEXT");

                    b.Property<int?>("NextNodeId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Position")
                        .HasColumnType("TEXT");

                    b.Property<int?>("WorkProcessId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("NextNodeId");

                    b.HasIndex("WorkProcessId");

                    b.ToTable("swf_node", (string)null);

                    b.HasDiscriminator().HasValue("Node");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Permissions.PermissionPerformer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WorkProcessPermissionId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("WorkProcessPermissionId");

                    b.ToTable("swf_PermissionPerformer", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Permissions.WorkProcessPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ManageRoles")
                        .HasMaxLength(400)
                        .HasColumnType("TEXT")
                        .HasColumnName("ManageRole");

                    b.Property<string>("ProcessName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProcessName");

                    b.ToTable("swf_workProcessPermission", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comment")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Creator")
                        .HasMaxLength(32)
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Form")
                        .HasColumnType("json");

                    b.Property<bool>("IsDebug")
                        .HasColumnType("INTEGER")
                        .HasComment("是否处于debug模式");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("INTEGER")
                        .HasComment("是否已经删除");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(100);

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Subject")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("SuspendComment")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("SuspendTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("WorkActivityCount")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WorkProcessId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("Number");

                    b.HasIndex("WorkProcessId");

                    b.ToTable("swf_processInstance", null, t =>
                        {
                            t.HasComment("工作流实例");
                        });
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstance+FileAttach", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreateUser")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("FileType")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ProcessInstanceId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProcessInstanceId");

                    b.ToTable("swf_files", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstanceDistribution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreateTime")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("HasRead")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ProcessInstanceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ReadTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserRealName")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProcessInstanceId");

                    b.ToTable("swf_PI_Distribution", null, t =>
                        {
                            t.HasComment("工作流程分发记录");
                        });
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstanceTag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Color")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int?>("ProcessInstanceId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("TagId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProcessInstanceId");

                    b.HasIndex("TagId");

                    b.ToTable("swf_PI_ProcessInstanceTags", null, t =>
                        {
                            t.HasComment("工作流程标记");
                        });
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.GlobalScripts.GlobalScriptItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Script")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("swf_globalScript", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.GlobalScripts.GlobalVariable", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("Env")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Variable")
                        .HasMaxLength(300)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("swf_global_variable", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.ScriptDefined", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("TEXT");

                    b.Property<string>("Script")
                        .HasMaxLength(6000)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("swf_scripts", (string)null);

                    b.HasDiscriminator().HasValue("ScriptDefined");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Settings.SwfSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasComment("id");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("TEXT");

                    b.Property<string>("Script")
                        .HasColumnType("text")
                        .HasComment("脚本");

                    b.HasKey("Id");

                    b.ToTable("swf_swf_setting", null, t =>
                        {
                            t.HasComment("工作流实例");
                        });

                    b.HasDiscriminator().HasValue("SwfSetting");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Tag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<int>("Number")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("swf_PI_Tags", null, t =>
                        {
                            t.HasComment("工作流程标记");
                        });
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkActivity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AssignPerformers")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("AssignTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Command")
                        .HasColumnType("TEXT");

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DisposeTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("DisposeUser")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DisposeUserName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(100);

                    b.Property<int?>("ProcessInstanceId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("INTEGER");

                    b.Property<string>("TaskCreatingGroup")
                        .HasMaxLength(32)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("TimeSpan")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("TEXT");

                    b.Property<int?>("WorkTaskId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProcessInstanceId");

                    b.HasIndex("WorkTaskId");

                    b.ToTable("swf_WorkActivity", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkProcess", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Abbr")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("简称");

                    b.Property<int>("CanBeDeleteWorkActivityCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comment")
                        .HasMaxLength(400)
                        .HasColumnType("TEXT")
                        .HasComment("备注");

                    b.Property<string>("Configurations")
                        .HasColumnType("text");

                    b.Property<string>("Creator")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("Enable")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FormDesign")
                        .HasColumnType("text");

                    b.Property<string>("FormManageDesign")
                        .HasColumnType("text");

                    b.Property<string>("FormTypeScriptDefined")
                        .HasColumnType("text");

                    b.Property<string>("GlobalScript")
                        .HasColumnType("text");

                    b.Property<string>("Group")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Icon")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("图标");

                    b.Property<int>("LogLevel")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("OnCancelId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("OnCompleteId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("OnStartId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Plugins")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Prefix")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdateTimeOffset")
                        .HasColumnType("TEXT");

                    b.Property<int>("Version")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("OnCancelId");

                    b.HasIndex("OnCompleteId");

                    b.HasIndex("OnStartId");

                    b.HasIndex("Name", "Version")
                        .IsUnique();

                    b.ToTable("swf_workProcess", (string)null);
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTaskCommands.WorkTaskCommand", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("Order")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WorkTaskId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("WorkTaskId");

                    b.ToTable("swf_WorkTaskCommand", (string)null);

                    b.HasDiscriminator().HasValue("WorkTaskCommand");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Assigners.ScriptAssigner", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Assigners.Assigner");

                    b.Property<string>("Script")
                        .HasMaxLength(3000)
                        .HasColumnType("TEXT");

                    b.HasDiscriminator().HasValue("ScriptAssigner");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Assigners.UsersAssigner", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Assigners.Assigner");

                    b.Property<string>("Performers")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.HasDiscriminator().HasValue("UsersAssigner");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.Decision", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Nodes.Node");

                    b.Property<string>("MatchDescription")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.HasDiscriminator().HasValue("Decision");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Nodes.EndNode", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Nodes.Node");

                    b.HasDiscriminator().HasValue("EndNode");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Nodes.StartNode", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Nodes.Node");

                    b.HasDiscriminator().HasValue("StartNode");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTask", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Nodes.Node");

                    b.Property<int?>("AssignerId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanGiveUp")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ExtendInfo")
                        .HasColumnType("text");

                    b.Property<string>("FormDesign")
                        .HasColumnType("text");

                    b.Property<int>("NextTaskPerformers")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SuggestionComment")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("WorkActivityCompleteScriptId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WorkTaskCompleteScriptId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WorkTaskStartScriptId")
                        .HasColumnType("INTEGER");

                    b.HasIndex("AssignerId");

                    b.HasIndex("WorkActivityCompleteScriptId");

                    b.HasIndex("WorkTaskCompleteScriptId");

                    b.HasIndex("WorkTaskStartScriptId");

                    b.HasDiscriminator().HasValue("WorkTask");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.WorkActivityScript", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Scripts.ScriptDefined");

                    b.HasDiscriminator().HasValue("WorkTaskScript");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.WorkProcessScript", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Scripts.ScriptDefined");

                    b.HasDiscriminator().HasValue("WorkProcessScript");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.WorkTaskCompleteScript", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Scripts.ScriptDefined");

                    b.HasDiscriminator().HasValue("WorkTaskCompleteScript");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Scripts.WorkTaskStartScript", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Scripts.ScriptDefined");

                    b.HasDiscriminator().HasValue("WorkTaskStartScript");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Settings.SwfMakeTagsSetting", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Settings.SwfSetting");

                    b.Property<string>("Plugins")
                        .HasColumnType("text");

                    b.HasDiscriminator().HasValue("SwfMakeTagsSetting");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTaskCommands.WorkTaskScriptCommand", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.WorkTaskCommands.WorkTaskCommand");

                    b.Property<string>("Script")
                        .HasColumnType("text");

                    b.HasDiscriminator().HasValue("WorkTaskCommandScript");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.BooleanDecisions.BoolScriptDecision", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Decisions.Decision");

                    b.Property<string>("ElseDescription")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<int?>("ElseNodeId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Script")
                        .HasColumnType("text")
                        .HasColumnName("Script")
                        .HasComment("执行脚本");

                    b.HasIndex("ElseNodeId");

                    b.HasDiscriminator().HasValue("BoolScriptDecision");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.BooleanDecisions.ConditionDecision", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.Decisions.Decision");

                    b.Property<string>("Script")
                        .HasColumnType("text")
                        .HasColumnName("ConditionDecision_Script")
                        .HasComment("执行脚本");

                    b.HasDiscriminator().HasValue("ConditionDecision");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTaskCommands.PreviousWorkTaskCommand", b =>
                {
                    b.HasBaseType("Coder.ScriptWorkflow.WorkTaskCommands.WorkTaskScriptCommand");

                    b.HasDiscriminator().HasValue("PreviousWorkTaskCommand");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.BooleanDecisions.ConditionSetting", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.Decisions.BooleanDecisions.ConditionDecision", null)
                        .WithMany("Settings")
                        .HasForeignKey("ConditionDecisionId");

                    b.HasOne("Coder.ScriptWorkflow.Nodes.Node", "Node")
                        .WithMany()
                        .HasForeignKey("NodeId");

                    b.Navigation("Node");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Nodes.Node", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.Nodes.Node", "NextNode")
                        .WithMany()
                        .HasForeignKey("NextNodeId");

                    b.HasOne("Coder.ScriptWorkflow.WorkProcess", "WorkProcess")
                        .WithMany()
                        .HasForeignKey("WorkProcessId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("NextNode");

                    b.Navigation("WorkProcess");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Permissions.PermissionPerformer", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.Permissions.WorkProcessPermission", "WorkProcessPermission")
                        .WithMany("Performers")
                        .HasForeignKey("WorkProcessPermissionId");

                    b.Navigation("WorkProcessPermission");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstance", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.WorkProcess", "WorkProcess")
                        .WithMany()
                        .HasForeignKey("WorkProcessId");

                    b.Navigation("WorkProcess");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstance+FileAttach", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.ProcessInstance", "ProcessInstance")
                        .WithMany("Files")
                        .HasForeignKey("ProcessInstanceId");

                    b.Navigation("ProcessInstance");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstanceDistribution", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.ProcessInstance", "ProcessInstance")
                        .WithMany()
                        .HasForeignKey("ProcessInstanceId");

                    b.Navigation("ProcessInstance");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstanceTag", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.ProcessInstance", "ProcessInstance")
                        .WithMany()
                        .HasForeignKey("ProcessInstanceId");

                    b.HasOne("Coder.ScriptWorkflow.Tag", "Tag")
                        .WithMany()
                        .HasForeignKey("TagId");

                    b.Navigation("ProcessInstance");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkActivity", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.ProcessInstance", "ProcessInstance")
                        .WithMany()
                        .HasForeignKey("ProcessInstanceId");

                    b.HasOne("Coder.ScriptWorkflow.WorkTask", "WorkTask")
                        .WithMany()
                        .HasForeignKey("WorkTaskId");

                    b.Navigation("ProcessInstance");

                    b.Navigation("WorkTask");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkProcess", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.Scripts.WorkProcessScript", "OnCancel")
                        .WithMany()
                        .HasForeignKey("OnCancelId");

                    b.HasOne("Coder.ScriptWorkflow.Scripts.WorkProcessScript", "OnComplete")
                        .WithMany()
                        .HasForeignKey("OnCompleteId");

                    b.HasOne("Coder.ScriptWorkflow.Scripts.WorkProcessScript", "OnStart")
                        .WithMany()
                        .HasForeignKey("OnStartId");

                    b.Navigation("OnCancel");

                    b.Navigation("OnComplete");

                    b.Navigation("OnStart");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTaskCommands.WorkTaskCommand", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.WorkTask", null)
                        .WithMany("Commands")
                        .HasForeignKey("WorkTaskId");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTask", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.Assigners.Assigner", "Assigner")
                        .WithMany()
                        .HasForeignKey("AssignerId");

                    b.HasOne("Coder.ScriptWorkflow.Scripts.WorkActivityScript", "WorkActivityCompleteScript")
                        .WithMany()
                        .HasForeignKey("WorkActivityCompleteScriptId");

                    b.HasOne("Coder.ScriptWorkflow.Scripts.WorkTaskCompleteScript", "WorkTaskCompleteScript")
                        .WithMany()
                        .HasForeignKey("WorkTaskCompleteScriptId");

                    b.HasOne("Coder.ScriptWorkflow.Scripts.WorkTaskStartScript", "WorkTaskStartScript")
                        .WithMany()
                        .HasForeignKey("WorkTaskStartScriptId");

                    b.Navigation("Assigner");

                    b.Navigation("WorkActivityCompleteScript");

                    b.Navigation("WorkTaskCompleteScript");

                    b.Navigation("WorkTaskStartScript");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.BooleanDecisions.BoolScriptDecision", b =>
                {
                    b.HasOne("Coder.ScriptWorkflow.Nodes.Node", "ElseNode")
                        .WithMany()
                        .HasForeignKey("ElseNodeId");

                    b.Navigation("ElseNode");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Permissions.WorkProcessPermission", b =>
                {
                    b.Navigation("Performers");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.ProcessInstance", b =>
                {
                    b.Navigation("Files");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.WorkTask", b =>
                {
                    b.Navigation("Commands");
                });

            modelBuilder.Entity("Coder.ScriptWorkflow.Decisions.BooleanDecisions.ConditionDecision", b =>
                {
                    b.Navigation("Settings");
                });
#pragma warning restore 612, 618
        }
    }
}
