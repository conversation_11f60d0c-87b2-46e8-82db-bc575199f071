﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HPSF;
using NPOI.SS.Formula.Functions;
using oa.contract.service;
using oa.contract.service.Clients;
using oa.contract.service.ViewModels.Contracts;
using oa.contract.service.ViewModels.Workloads;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient;

/// <summary>
/// </summary>
public class ContractHttpClient
{
    private readonly IContractHttpClient _client;
    private readonly IWorkflowContext _context;
    private readonly IWorkloadHttpClient _workloadClient;

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    /// <param name="context"></param>
    /// <param name="workloadClient"></param>
    public ContractHttpClient(IContractHttpClient client, IWorkflowContext context,
        IWorkloadHttpClient workloadClient)
    {
        _client = client;
        _context = context;
        _workloadClient = workloadClient;
    }

    /// <summary>
    ///     生成合同台账
    /// </summary>
    /// <returns></returns>
    public int Save()
    {
        var submit = new ContractOrderSubmit();
        var json = JsonConvert.DeserializeObject
            <JObject>(_context.ProcessInstance.Form);

        if (json.TryGetValue("name", out var nameToken))
            submit.Name = nameToken.Value<string>();

        if (json.TryGetValue("contractType", out var contractTypeToken))
            submit.ContractType = contractTypeToken.Value<string>() == "收款合同" ? ContractType.收款合同 : ContractType.付款合同;

        if (json.TryGetValue("bookType", out var bookTypeToken)) //总公司签订 分公司签订 分公司报总公司签订
            submit.BookType = bookTypeToken.Value<string>() == "总公司签订" ? BookType.总公司签订 : bookTypeToken.Value<string>() == "分公司签订" ? BookType.分公司签订 : BookType.分公司报总公司签订;

        if (json.TryGetValue("code", out var codeToken))
            submit.Code = codeToken.Value<string>();

        if (json.TryGetValue("oppositeName", out var oppositeNameToken))
            submit.OppositeName = oppositeNameToken.Value<string>();


        if (json.TryGetValue("oppositeCode", out var oppositeCodeToken))
            submit.OppositeCode = oppositeCodeToken.Value<string>();


        if (json.TryGetValue("planBookDate", out var planBookDateToken))
            submit.PlanBookDate = Convert.ToDateTime(planBookDateToken.Value<string>());


        if (json.TryGetValue("projectName", out var projectNameToken))
            submit.ProjectName = projectNameToken.Value<string>();


        if (json.TryGetValue("projectCode", out var projectCodeToken))
            submit.ProjectCode = projectCodeToken.Value<string>();


        if (json.TryGetValue("serviceTerm", out var serviceTerm))
            submit.ServiceTerm = serviceTerm.Value<string>();

        if (json.TryGetValue("promiseServiceTerm", out var promiseServiceTerm))
            submit.PromiseServiceTerm = promiseServiceTerm.Value<string>();

        if (json.TryGetValue("serviceContent", out var serviceContent))
            submit.ServiceContent = serviceContent.Value<string>();

        if (json.TryGetValue("serviceShip", out var serviceShip))
            submit.ServiceShip = serviceShip.Value<string>();

        if (json.TryGetValue("contractPrice", out var contractPrice))
            submit.ContractPrice = contractPrice.Value<string>();

        if (json.TryGetValue("contractPriceInfo", out var contractPriceInfo))
            submit.ContractPriceInfo = contractPriceInfo.Value<string>();

        if (json.TryGetValue("contractTotalInfo", out var contractTotalInfo))
            submit.ContractTotalInfo = contractTotalInfo.Value<string>();

        if (json.TryGetValue("contractTotal", out var contractTotal)) submit.ContractTotal = string.IsNullOrWhiteSpace(contractTotal.Value<string>()) ? 0 : contractTotal.Value<decimal>();

        if (json.TryGetValue("linkCode", out var linkCode))
            submit.LinkCode = linkCode.Value<string>();

        if (json.TryGetValue("applyDate", out var applyDate))
            submit.ApplyDate = Convert.ToDateTime(applyDate.Value<string>());


        if (json.TryGetValue("org", out var org))
            submit.OrgPath = org.Value<string>();

        if (json.TryGetValue("promiseDateType", out var promiseDateType))
            submit.PromiseDateType = promiseDateType.Value<string>();
        if (json.TryGetValue("projectArchiveId", out var ProjectArchiveId))
            submit.ProjectArchiveId = ProjectArchiveId.Value<int?>();
        if (json.TryGetValue("projectArchiveCode", out var ProjectArchiveCode))
            submit.ProjectArchiveCode = ProjectArchiveCode.Value<string>();
        if (json.TryGetValue("projectArchiveName", out var ProjectArchiveName))
            submit.ProjectArchiveName = ProjectArchiveName.Value<string>();
        //if (json.TryGetValue("filesId", out var FilesId))
        //    submit.FilesId = FilesId.Value<int>();
        // 2. 获取attachments数组
        submit.Attachments = new List<FileItem>();
        if (json.TryGetValue("attachments", out JToken attachmentsToken) && attachmentsToken is JArray attachments)
        {
            foreach (JObject item in attachments)
            {
                string name = item["name"].Value<string>();
                long size = item["size"].Value<long>();
                string fileId = item["fileId"].Value<string>();
                int? firstCategory = item.ContainsKey("firstCategory") ? item["firstCategory"].Value<int>():null;
                int? secondCategory =item.ContainsKey("secondCategory")? item["secondCategory"].Value<int>():null;
                submit.Attachments.Add(new FileItem
                {
                    Name = name,
                    Size = size,
                    FileId = fileId,
                    FirstCategory = firstCategory,
                    SecondCategory = secondCategory
                });

            }
        }
        if (json.TryGetValue("filesId", out var filesIdType))
            submit.FilesId = filesIdType.Value<int>();
       
        submit.OrderNo = _context.ProcessInstance.Number;
        submit.OrderCloseDate = DateTimeOffset.Now;
        submit.UpdateBy = _context.ProcessInstance.CreatorName;
        var r = _client.SaveAsync(submit).Result;
        return r.Data.Id;
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    public string GetContractCode(GetContractCodeSubmit submit)
    {
        var contract = _client.BuildContractCode(submit).Result;
        return contract.Data.Code;
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    public string GetCContractCode(GetContractCodeSubmit submit)
    {
        var contract = _client.BuildCContractCode(submit).Result;
        return contract.Data.Code;
    }

    /// <summary>
    ///     生成工作量
    /// </summary>
    /// <returns></returns>
    public int SaveWorkload(string startDate, string endDate)
    {
        var submit = new WorkloadSubmit
        {
            StartDate = Convert.ToDateTime(startDate),
            EndDate = Convert.ToDateTime(endDate)
        };

        var json = JsonConvert.DeserializeObject<JObject>(_context.ProcessInstance.Form);
        if (json != null)
        {
            if (json.TryGetValue("unitPrice", out var jToken))
                submit.UnitPrice = jToken.Value<decimal>();
            if (json.TryGetValue("amount", out var jTokenAmount))
                submit.Amount = jTokenAmount.Value<decimal>();
            if (json.TryGetValue("completeWorkload", out var jCompleteWorkload))
                submit.CompleteWorkload = jCompleteWorkload.Value<int>();
            if (json.TryGetValue("contractCode", out var jContractCode))
                submit.ContractCode = jContractCode.Value<string>();
            if (json.TryGetValue("projectCode", out var jProjectCode))
                submit.ProjectCode = jProjectCode.Value<string>();
        }

        submit.OrderNo = _context.ProcessInstance.Number;
        submit.UpdateUser = _context.ProcessInstance.CreatorName;
        var r = _workloadClient.SaveAsync(submit).Result;
        return r.Id;
    }

    /// <summary>
    ///     收付款工单锁定合同
    /// </summary>
    /// <param name="submit"></param>
    public void ContractLock(ContractLockSubmit submit)
    {
        _client.ContractLock(submit).Wait();
    }

    /// <summary>
    ///     工作量工单锁定合同
    /// </summary>
    /// <param name="submit"></param>
    public void ContractWorkloadLock(ContractLockSubmit submit)
    {
        _client.ContractWorkloadLock(submit).Wait();
    }

    /// <summary>
    ///     保存收款流程
    /// </summary>
    /// <returns></returns>
    public int CreateReceiveOrader()
    {
        var submit = GetContractReceiveSubmit();
        var r = _client.CreateFromReceive(submit).Result;
        return r.Data.Id;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public int SaveReceiveOrader()
    {
        var submit = GetContractReceiveSubmit();
        var r = _client.SaveFromReceive(submit).Result;
        return r.Data.Id;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private ContractReceiveSubmit GetContractReceiveSubmit()
    {
        var submit = new ContractReceiveSubmit();
        var json = JsonConvert.DeserializeObject
            <JObject>(_context.ProcessInstance.Form);
        if (json.ContainsKey("payDateStartStr"))
        {
            var payDateStartStr = json["payDateStartStr"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(payDateStartStr))
                submit.PayDateStart = Convert.ToDateTime(json["payDateStartStr"]?.Value<string>());
        }

        if (json.ContainsKey("payDateEndStr"))
        {
            var payDateEndStr = json["payDateEndStr"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(payDateEndStr))
                submit.PayDateEnd = Convert.ToDateTime(json["payDateEndStr"]?.Value<string>());
        }

        if (json.ContainsKey("contractCode"))
            submit.Code = json["contractCode"]?.Value<string>();
        if (json.ContainsKey("oldCode"))
            submit.OldCode = json["oldCode"]?.Value<string>();
        if (json.ContainsKey("supplierCode"))
            submit.SupplierCode = json["supplierCode"]?.Value<string>();
        if (json.ContainsKey("projectCode"))
            submit.ProjectCode = json["projectCode"]?.Value<string>();
        if (json.ContainsKey("invoiceType"))
        {
            var invoiceType = json["invoiceType"]?.Value<string>();
            submit.InvoiceType = invoiceType == "普票" ? InvoiceType.普票 : InvoiceType.专票;
        }

        if (json.ContainsKey("applicationDate"))
        {
            var applicationDate = json["applicationDate"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(applicationDate))
                submit.ApplicationDate = Convert.ToDateTime(json["applicationDate"]?.Value<string>());
        }

        if (json.ContainsKey("payCount"))
            submit.PayCount = json["payCount"]?.Value<int?>() ?? 0;
        if (json.ContainsKey("completeWorkload"))
            submit.CompleteWorkload = json["completeWorkload"]?.Value<int?>() ?? 0;

        if (json.ContainsKey("remark"))
            submit.Remark = json["remark"]?.Value<string>();
        if (json.ContainsKey("payContents"))
            submit.PayContents = json["payContents"]?.Value<string>();
        if (json.ContainsKey("invoiceNo"))
            submit.InvoiceNo = json["invoiceNo"]?.Value<string>();

        if (json.ContainsKey("invoiceDate"))
        {
            var invoiceDate = json["invoiceDate"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(invoiceDate))
                submit.InvoiceDate = Convert.ToDateTime(json["invoiceDate"]?.Value<string>());
        }

        if (json.ContainsKey("taxRate"))
        {
            var taxRate = json["taxRate"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(taxRate))
                submit.TaxRate = json["taxRate"]?.Value<decimal?>() ?? 0;
        }

        if (json.ContainsKey("payAmount"))
        {
            var amount = json["payAmount"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(amount))
                submit.Amount = json["payAmount"]?.Value<decimal?>() ?? 0;
        }

        if (json.ContainsKey("收款金额"))
        {
            var payAmount = json["收款金额"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(payAmount))
                submit.PayAmount = json["收款金额"]?.Value<decimal?>() ?? 0;
        }

        if (json.ContainsKey("payAmountDate"))
        {
            var payAmountDate = json["payAmountDate"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(payAmountDate))
                submit.PayAmountDate = Convert.ToDateTime(json["payAmountDate"]?.Value<string>());
        }


        if (json.TryGetValue("voucherNo", out var voucherNo))
            submit.VoucherNo = voucherNo.Value<string>();


        if (json.TryGetValue("csRemark", out var csRemark))
            submit.CsRemark = csRemark.Value<string>();

        if (json.TryGetValue("zdRemark", out var zdRemark))
            submit.ZdRemark = zdRemark.Value<string>();


        submit.OrderNo = _context.ProcessInstance.Number;
        submit.OrderUser = _context.ProcessInstance.CreatorName;
        submit.OrderType = OrderType.收款;
        return submit;
    }

    /// <summary>
    ///     保存付款流程
    /// </summary>
    /// <returns></returns>
    public int CreatePayOrader()
    {
        var submit = GetContractPaySubmit();
        var r = _client.CreateFromPay(submit).Result;
        return r.Data.Id;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public int SavePayOrader()
    {
        var submit = GetContractPaySubmit();
        var r = _client.SaveFromPay(submit).Result;
        return r.Data.Id;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private ContractPaySubmit GetContractPaySubmit()
    {
        var submit = new ContractPaySubmit();
        var json = JsonConvert.DeserializeObject
            <JObject>(_context.ProcessInstance.Form);

        if (json.ContainsKey("contractCode"))
            submit.Code = json["contractCode"]?.Value<string>();
        if (json.ContainsKey("supplierCode"))
            submit.SupplierCode = json["supplierCode"]?.Value<string>();
        if (json.ContainsKey("startDate"))
            submit.StartDate = Convert.ToDateTime(json["startDate"]?.Value<string>());
        if (json.ContainsKey("applicationDate"))
            submit.ApplicationDate = Convert.ToDateTime(json["applicationDate"]?.Value<string>());
        if (json.ContainsKey("orgUser"))
            submit.OrgUser = json["orgUser"]?.Value<string>();
        if (json.ContainsKey("startPayDate"))
            submit.StartPayDate = Convert.ToDateTime(json["startPayDate"]?.Value<string>());

        if (json.ContainsKey("payDateStartStr"))
            submit.PayDateStart = Convert.ToDateTime(json["payDateStartStr"]?.Value<string>());
        if (json.ContainsKey("payDateEndStr"))
            submit.PayDateEnd = Convert.ToDateTime(json["payDateEndStr"]?.Value<string>());

        if (json.ContainsKey("payCount"))
            submit.PayCount = json["payCount"]?.Value<int?>() ?? 0;
        if (json.ContainsKey("completeWorkload"))
            submit.CompleteWorkload = json["completeWorkload"]?.Value<int?>() ?? 0;
        if (json.ContainsKey("unitPrice"))
        {
            var unitPrice = json["unitPrice"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(unitPrice))
            {
                submit.UnitPrice = json["unitPrice"]?.Value<decimal?>() ?? 0;
            }
        }

        if (json.ContainsKey("amount"))
        {
            var amount = json["amount"]?.Value<string>();
            if (!string.IsNullOrWhiteSpace(amount))
                submit.Amount = json["amount"]?.Value<decimal?>() ?? 0;
        }
        if (json.TryGetValue("payments", out var payments))
        {
            submit.Payments = new List<PaymentInfo>();
            foreach (var item in (JArray)payments)
            {
                submit.Payments.Add(new PaymentInfo()
                {
                    PayAmount = Convert.ToDecimal(item["payAmount"]?.Value<string>()),
                    PayAmountDate = Convert.ToDateTime(item["payAmountDate"]?.Value<string>()),
                    VoucherNo = item["voucherNo"]?.Value<string>()
                });
            }

        }
        //if (json.ContainsKey("payAmount"))
        //{
        //    var payAmount = json["payAmount"]?.Value<string>();
        //    if (!string.IsNullOrWhiteSpace(payAmount))
        //        submit.PayAmount = json["payAmount"]?.Value<decimal>();
        //}

        //if (json.ContainsKey("voucherNo"))
        //    submit.VoucherNo = json["voucherNo"]?.Value<string>();
        //if (json.ContainsKey("payAmountDate"))
        //{
        //    var payAmountDate = json["payAmountDate"]?.Value<string>();
        //    if (!string.IsNullOrWhiteSpace(payAmountDate))
        //        submit.PayAmountDate = Convert.ToDateTime(json["payAmountDate"]?.Value<string>());
        //}

        submit.OrderNo = _context.ProcessInstance.Number;
        submit.OrderUser = _context.ProcessInstance.CreatorName;
        submit.OrderType = OrderType.付款;
        return submit;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public int SaveFromGroup()
    {
        var submit = new GroupSubmit();
        var json = JsonConvert.DeserializeObject
            <JObject>(_context.ProcessInstance.Form);
        submit.OrderUser = _context.ProcessInstance.CreatorName;
        submit.OrderNo = _context.ProcessInstance.Number;


        if (json.TryGetValue("contractCode", out var contractCodeToken))
            submit.ContractCode = contractCodeToken.Value<string>();

        if (json.TryGetValue("contractGroupId", out var contractGroupIdToken))
            submit.ContractGroupId = contractGroupIdToken.Value<int>();

        if (json.TryGetValue("applyDate", out var applyDateToken))
            submit.OrderDate = Convert.ToDateTime(applyDateToken.Value<string>());


        var r = _client.SaveFromGroup(submit).Result;
        return r.Data.Id;
    }
    /// <summary>
    /// 拉黑对方单位
    /// </summary>
    /// <returns></returns>
    public int BlockBlackList()
    {
        var submit = new Supplier();
        var json = JsonConvert.DeserializeObject
            <JObject>(_context.ProcessInstance.Form);
        if (json.TryGetValue("supplierId", out var supplierIdToken))
            submit.Id = supplierIdToken.Value<int>();
        var r = _client.BlockBlackList(submit).Result;       
        return r.Data.Id;
    }    
}