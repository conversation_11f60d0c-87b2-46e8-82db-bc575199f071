﻿using System.Collections.Generic;

namespace Coder.ScriptWorkflow.Scripts;

public class SwfUser
{
    public string UserName { get; set; }
    public List<string> Roles { get; set; } = new();

    public List<SwfClaim> Claims { get; set; } = new();

    public string Name { get; set; }

    public void Add(string type, string val)
    {
        Claims.Add(new SwfClaim
        {
            Type = type,
            Value = val
        });
    }
}