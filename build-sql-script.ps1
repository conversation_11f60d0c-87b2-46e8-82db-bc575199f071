$projFolder = "Coder.ScriptWorkflow.WebApi"
$projMigrationPrefx = "Coder.ScriptWorkflow.Migrations"

cd src/${projFolder}
dotnet tool restore

$env:DB_TYPE="MYSQL"
dotnet ef migrations script   --project ../${projMigrationPrefx}.Mysql/${projMigrationPrefx}.Mysql.csproj -o mysql.sql

$env:DB_TYPE="MSSQL"
dotnet ef migrations script  --project ../${projMigrationPrefx}.Mssql -o mssql.sql

$env:DB_TYPE="SQLITE"
dotnet ef migrations script  --project ../${projMigrationPrefx}.Sqlite  -o sqlite.sql


$env:DB_TYPE="DM"
dotnet ef migrations script  --project ../${projMigrationPrefx}.DM -o dm.sql
