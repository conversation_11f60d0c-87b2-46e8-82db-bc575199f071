﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Permissions;

namespace Coder.ScriptWorkflow.ViewModels.WorkProcesses;

/// <summary>
/// </summary>
public class WorkProcessListItem : IWorkProcessWithPermission
{
    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public bool Publish { get; set; }

    /// <summary>
    /// </summary>
    public int Version { get; set; }

    /// <summary>
    ///     创建者的UserName.
    /// </summary>
    public string Creator { get; set; }


    /// <summary>
    /// </summary>
    public IList<WorkProcessListItem> ChildNodes { get; } = new
        List<WorkProcessListItem>();

    /// <summary>
    ///     工作流分组
    /// </summary>
    public string Group { get; set; }

    /// <summary>
    /// 当前用户是否当前用户的创建者
    /// </summary>
    public bool IsWorkProcessCreator { get; set; }

    /// <summary>
    /// 当前用户是否本工作流程的管理员
    /// </summary>
    public bool IsManager { get; set; }

    /// <summary>
    /// 当前用户是否可以创建
    /// </summary>
    public bool CanCreateProcessInstance { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string Comment { get; set; }
    /// <summary>
    /// 图标
    /// </summary>
    public string Icon { get; set;}
    /// <summary>
    /// 简称
    /// </summary>
    public string Abbr { get; set; }

    public string GetWorkProcessCreator()
    {
        return Creator;
    }
}