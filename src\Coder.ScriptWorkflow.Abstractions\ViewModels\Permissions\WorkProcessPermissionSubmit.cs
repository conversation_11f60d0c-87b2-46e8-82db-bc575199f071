﻿using System.Collections.Generic;

namespace Coder.ScriptWorkflow.ViewModels.Permissions;

/// <summary>
///     客户端提交修改工作流程权限的数据传输对象
/// </summary>
/// <remarks>
///     <para>此类用于从客户端接收工作流程权限的修改请求。</para>
///     <para>包含了工作流程的权限配置信息，包括管理角色和可启动流程的执行者列表。</para>
///     <para>是 WorkProcessPermission 实体的 ViewModel 对应类。</para>
/// </remarks>
public class WorkProcessPermissionSubmit
{
    /// <summary>
    ///     工作流程权限配置的唯一标识符
    /// </summary>
    /// <value>
    ///     对应数据库中 WorkProcessPermission 表的主键ID。
    ///     新增时为0，更新时为实际的ID值。
    /// </value>
    public int Id { get; set; }

    /// <summary>
    ///     工作流程的名称
    /// </summary>
    /// <value>
    ///     工作流程定义的名称，用于标识特定的工作流程。
    ///     此名称应与 WorkProcess 实体中的 Name 属性保持一致。
    /// </value>
    /// <example>请假审批流程</example>
    public string ProcessName { get; set; }

    /// <summary>
    ///     管理该工作流程的角色列表
    /// </summary>
    /// <value>
    ///     包含可以管理此工作流程的所有角色名称。
    ///     拥有这些角色的用户将具有工作流程的管理权限，
    ///     包括但不限于：查看所有实例、修改权限配置、强制推进流程等。
    /// </value>
    /// <example>["工作流管理员", "部门经理", "系统管理员"]</example>
    public IEnumerable<string> ManageRoles { get; set; }

    /// <summary>
    ///     可以启动此工作流程的执行者配置列表
    /// </summary>
    /// <value>
    ///     定义了哪些用户、角色或组织单元可以启动此工作流程。
    ///     每个执行者包含类型（用户/角色/组织）和对应的名称标识。
    ///     系统将根据此配置来验证用户是否有权限创建新的流程实例。
    /// </value>
    /// <remarks>
    ///     <para>支持三种执行者类型：</para>
    ///     <list type="bullet">
    ///         <item>
    ///             <description>User: 特定的用户</description>
    ///         </item>
    ///         <item>
    ///             <description>Role: 拥有特定角色的所有用户</description>
    ///         </item>
    ///         <item>
    ///             <description>Org: 属于特定组织单元的所有用户</description>
    ///         </item>
    ///     </list>
    /// </remarks>
    /// <example>
    ///     可以包含多种类型的执行者 :
    ///     - 用户: "张三", "李四"
    ///     - 角色: "员工", "经理"
    ///     - 组织: "技术部", "人事部"
    /// </example>
    public PermissionPerformerViewModel[] Performers { get; set; }
}