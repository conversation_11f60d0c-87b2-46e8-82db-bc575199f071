﻿namespace Coder.ScriptWorkflow.ViewModels.Permissions;

/// <summary>
///     工作流与当前登录用户的权限关系。
/// </summary>
public interface IWorkProcessWithPermission
{
    /// <summary>
    ///     当前用户是否创建者
    /// </summary>
    bool IsWorkProcessCreator { get; set; }

    /// <summary>
    ///     当前用户是否Manager
    /// </summary>
    bool IsManager { get; set; }

    /// <summary>
    ///     当前用户：是否能够启动 工作流程。
    /// </summary>
    bool CanCreateProcessInstance { get; set; }

    /// <summary>
    ///     获取流程创建者
    /// </summary>
    /// <returns></returns>
    string GetWorkProcessCreator();
}