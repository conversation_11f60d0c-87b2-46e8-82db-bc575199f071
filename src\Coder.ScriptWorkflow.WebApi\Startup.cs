using System;
using System.Configuration;
using System.IO;
using System.Text.RegularExpressions;
using coder.car.service.HttpClients;
using Coder.Authentication;
using Coder.ConsulHelper;
using Coder.HealthChecks;
using Coder.Member.Clients.Http;
using Coder.Orgs.Clients;
using Coder.PreventDuplicateSubmit;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Performers.CoderMembers;
using Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient;
using Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient;
using Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient;
using Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient;
using Coder.ScriptWorkflow.Scripts.Plugin.JsRepaidCarClient;
using Coder.ScriptWorkflow.Scripts.Plugin.Member;
using Coder.ScriptWorkflow.Scripts.Plugin.MySql;
using Coder.ScriptWorkflow.WebApi.Filtters;
using Coder.ScriptWorkflow.WebApi.Modules;
using Coder.Token.HttpClients;
using Coder.VBenMenu.Interceptor;
using Coder.VBenNotify.Interceptor;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using oa.contract.service.HttpClients;
//using Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient;

//using oa.contract.service.HttpClients;

namespace Coder.ScriptWorkflow.WebApi;

/// <summary>
/// </summary>
public class Startup
{
    private static readonly Regex OriginWithRegex = new("(http|ws)://localhost:*?", RegexOptions.IgnoreCase);
    private readonly IWebHostEnvironment _env;

    /// <summary>
    /// </summary>
    /// <param name="configuration"></param>
    public Startup(IConfiguration configuration, IWebHostEnvironment env)
    {
        _env = env;
        Configuration = configuration;
    }

    /// <summary>
    /// </summary>
    public IConfiguration Configuration { get; }

    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    /// <param name="env"></param>
    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddMemberCache(_env, Configuration)
            .AddHealthChecks();

        services.Configure<KestrelServerOptions>(options =>
        {
            // Set the limit to 256 MB

            long limitSize = 1024 * 1024 * 256;

            var strLimitSize = Environment.GetEnvironmentVariable("FILE_UPLOAD_SIZE");
            if (strLimitSize != null) limitSize = Convert.ToInt64(strLimitSize);

            options.Limits.MaxRequestBodySize = limitSize;
        });


        services.Configure<FormOptions>(options =>
        {
            // Set the limit to 256 MB
            options.MultipartBodyLengthLimit = 402653184;
        });

        services.AddHttpClient()
            .AddPreventDuplicateService();
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();

        services.AddControllers(options =>
            {
                options.Filters
                    .Add<CustomExceptionFilterAttribute>();
                // 移除已删除的SampleActionFilter
            })
            .ConfigureApiBehaviorOptions(options =>
            {
                options.InvalidModelStateResponseFactory = context =>
                {
                    var problems = new CoderBadRequest(context);
                    return new OkObjectResult(problems);
                };
            })
            .AddApplicationPart(typeof(Startup).Assembly)
            .AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
            });
        ;
        services.AddCors(builder => builder.AddDefaultPolicy(
            opt =>
                opt
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials()
                    .SetIsOriginAllowed(origin =>
                    {
                        var a = OriginWithRegex.IsMatch(origin);
                        return a;
                    })));


        OnConfigDbContext(services);
        ConsulSetup(services);

        var gateway = Configuration.GetSection("gateway").Value;
        if (!gateway.EndsWith('/')) gateway += "/";

        SetupAuthentication(services, gateway);

        services.AddOrganizationHttpClient(gateway);

        services.AddContracHttpService(gateway + "contract");
        
        services.AddScriptWorkflowServices(options =>
            {
                options.FileSystemHost = gateway + "fs";
                //options.FileSystemHost = "http://localhost:54387";
                options.AddEfStores<ApplicationDbContext>();
                //每次添加
                options.AddVBenInterceptor(gateway);
                options.AddCoderVBenNotify(gateway);

                options.AddJsPlugin<JsHttpClientPlugin>();
                options.AddJsPlugin<MysqlPlugin>();
                options.AddJsPlugin<JsHttpMemberPlugin>();
                options.AddJsPlugin<JsHttpContractPlugin>();

                //options.AddCoderWorkBench(gateway); //增加 coder_workbench的流程工作台引用

                //options.Services.AddTransient<IPerformerQueryStore, DemoPerformerQueryStore>();
                options.AddCoderMember();

                options.AddJsPlugin<JsHttpOrgPlugin>();
                options.AddJsPlugin<JsHttpHumanResourcePlugin>();
                options.AddJsPlugin<JsHttpRepaidCarPlugin>();
                //options.AddJsPlugin<JsDataPusherPlugin>(option =>
                //{
                //    var path = "migrator-db";
                //    if (!gateway.EndsWith("/"))
                //        path = gateway + "/" + path;
                //    else
                //        path = gateway + path;
                //    services.AddMigratorHttpClient(path);
                //});
            })
            .AddTransient<HumanResourceHttpFactory>()
            .AddTransient(sp =>
            {
                return new HumResourceConfig
                {
                    Url = Configuration.GetSection("HumanResourceServer").Value
                };
            })
            .AddTransient<IDebuggerPusher>(sp => sp.GetRequiredService<DebuggerSignalRContext>())
            .AddTransient<DebuggerSignalRContext>();

        // 注册文件管理相关服务
        services.AddScoped<Coder.ScriptWorkflow.Services.FileAttachmentService>()
                .AddScoped<Coder.ScriptWorkflow.Services.ProcessInstanceFileService>();

        services.AddSignalR();


        services.AddOrganizationHttpClient(gateway);
        services.AddCarHttpService(gateway + "car");
    }

    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    /// <param name="gateway"></param>
    private void SetupAuthentication(IServiceCollection services, string gateway)
    {
        var opt = Configuration.GetSection("TokenService");
        //是否验证token
        var client = opt["client"];
        var secretKey = opt["secretKey"];
        if (!EF.IsDesignTime)
            services.AddCoderJwtAuth(option =>
            {

                option.Host = gateway + "token";
                option.Client = client;
                option.SecretKey = secretKey;
                opt.Bind(option);
            });
        //获取用户信息，需要AddCoderAuthTokenClient 获得访问票据
        services.AddMemberHttpClient(option =>
        {
            option.SecretKey = secretKey;
            option.MemberHost = gateway + "member";

            option.Client = client; 
            opt.Bind(option);
        });
        //TokenService 访问，用于生成token
        services.AddCoderAuthTokenClient(option =>
        {
            option.Host = gateway + "token";
            option.Client = client;
            option.SecretKey = secretKey;
            opt.Bind(option);
        });
        //Menu访问
    }

    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    /// <exception cref="ConfigurationErrorsException"></exception>
    /// <exception cref="Exception"></exception>
    protected virtual void OnConfigDbContext(IServiceCollection services)
    {
        //如果使用msyql 请使用下面代码。
        var connection = Environment.GetEnvironmentVariable("DB_CONNECTION")?.Trim();
        var databaseType = Environment.GetEnvironmentVariable("DB_TYPE")?.Trim() ?? Configuration["DbType"];

        if (string.IsNullOrEmpty(connection))
        {
            connection = null; //环境变量导入，可能是个空字符串，因此设置为空。
            var dbHost = Environment.GetEnvironmentVariable("DB_HOST")?.Trim();
            if (dbHost != null)
            {
                var dbPassword = Environment.GetEnvironmentVariable("DB_PASSWORD") ?? "";
                var dbName = Environment.GetEnvironmentVariable("DB_DATABASE");
                if (string.IsNullOrEmpty(dbName))
                    dbName = Environment.GetEnvironmentVariable("DB_DEFAULT_DATABASE");
                if (string.IsNullOrEmpty(dbName))
                    dbName = "coder_swf";
                var dbUser = Environment.GetEnvironmentVariable("DB_USER");
                var dbPort = Environment.GetEnvironmentVariable("DB_PORT")?.Trim();

                if (dbUser == null)
                    throw new ConfigurationErrorsException("采用环境变量模式设置Mysql链接，已经提供了DB_HOST的前提下，却没有提供用户。");

                switch (databaseType)
                {
                    case "DM":
                        {
                            var port = string.IsNullOrEmpty(dbPort) ? "" : $":{dbPort}";
                            connection = $"server={dbHost}{port};database={dbName};user={dbUser};password={dbPassword};";
                            break;
                        }
                    case "MYSQL":
                        {
                            var port = string.IsNullOrEmpty(dbPort) ? "" : $";port={dbPort}";
                            connection =
                                $"server={dbHost};database={dbName}{port};user={dbUser};password={dbPassword};SslMode=none;AllowPublicKeyRetrieval=True";
                            break;
                        }
                    default:
                        {
                            var port = string.IsNullOrEmpty(dbPort) ? "" : $";port={dbPort}";
                            connection = $"server={dbHost};database={dbName}{port};user={dbUser};password={dbPassword};";
                            break;
                        }
                }
            }
        }


        services.AddDbContext<ApplicationDbContext>(
            options1 =>
            {
                options1.UseLazyLoadingProxies();
                switch (databaseType)
                {
                    case "MSSQL":
                        var mssqlConn = connection ?? Configuration.GetConnectionString("mssql");
                        options1.UseSqlServer(mssqlConn, action =>
                        {
                            action.CommandTimeout(300);
                            action.MigrationsAssembly("Coder.ScriptWorkflow.Migrations.Mssql");
                            action.MigrationsHistoryTable("swf_efmigrationshistory");
                        });
                        break;
                    case "SQLITE":
                        if (!Directory.Exists("_data")) Directory.CreateDirectory("_data");

                        var sqliteConn = "Data Source=_data/database.db;";
                        options1.UseSqlite(sqliteConn, action =>
                        {
                            action.CommandTimeout(300);
                            action.MigrationsAssembly("Coder.ScriptWorkflow.Migrations.Sqlite");
                            action.MigrationsHistoryTable("swf_efmigrationshistory");
                        });
                        break;
                    case "MYSQL":
                        var mysqlConn = connection ?? Configuration.GetConnectionString("mysql");
                        options1.UseMySql(mysqlConn, new MySqlServerVersion(new Version(5, 7, 25)), action =>
                        {
                            action.CommandTimeout(300);
                            action.MigrationsAssembly("Coder.ScriptWorkflow.Migrations.Mysql");
                            action.MigrationsHistoryTable("swf_efmigrationshistory");
                        });
                        break;
                    case "DM":
                        var dmConn = connection ?? Configuration.GetConnectionString("DM");

                        options1.UseDm(dmConn, action =>
                        {
                            action.CommandTimeout(int.MaxValue);
                            action.MigrationsAssembly("Coder.ScriptWorkflow.Migrations.DM");
                            action.MigrationsHistoryTable("swf_efmigrationshistory");
                        });
                        break;
                    default:
                        throw new Exception("Not support " + databaseType);
                }
            });
    }


    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    protected virtual void ConsulSetup(IServiceCollection services)
    {
        services.RegisterToConsul(option =>
        {
            var serviceId = Environment.GetEnvironmentVariable("SERVICE_ID");
            Configuration.GetSection("ConsulOption").Bind(option);
            if (!string.IsNullOrEmpty(serviceId)) option.ServiceId = serviceId;
        });
    }


    /// <summary>
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment() || env.IsStaging())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseRouting();
        app.UseCors();
        app.UseAuthentication();
        app.UseAuthorization();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapCoderHealthChecks();
            endpoints.MapControllers();
            endpoints.MapHub<DebuggerHub>("/debugger");
        });
    }
}