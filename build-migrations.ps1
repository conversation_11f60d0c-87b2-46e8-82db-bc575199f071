$projFolder = "Coder.ScriptWorkflow.WebApi"
$projMigrationPrefx = "Coder.ScriptWorkflow.Migrations"

cd src/${projFolder}
#dotnet tool restore

$name = Read-Host "Please enter migration name."


$env:DB_TYPE="MYSQL"
dotnet ef migrations add ${name} --project ../${projMigrationPrefx}.Mysql/${projMigrationPrefx}.Mysql.csproj

$env:DB_TYPE="MSSQL"
dotnet ef migrations add ${name} --project ../${projMigrationPrefx}.Mssql

$env:DB_TYPE="SQLITE"
dotnet ef migrations add ${name} --project ../${projMigrationPrefx}.Sqlite

$env:DB_TYPE="DM"
dotnet ef migrations add ${name} --project ../${projMigrationPrefx}.DM
