﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.DtoTranslator.Define;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel.Translator;

/// <summary>
/// </summary>
public class WorkTaskSubmitTest
{
    /// <summary>
    /// </summary>
    /// <param name="workTaskId"></param>
    [Theory]
    [InlineData(1)]
    [InlineData(0)]
    public void WorkTaskFillToWorkTask(int workTaskId)
    {
        var wp = new WorkProcess("wp");
        var worktaskSubmit = new WorkTaskSubmit();
        worktaskSubmit.Id = workTaskId;

        worktaskSubmit.Auto = false;
        worktaskSubmit.FormDesign = nameof(worktaskSubmit.FormDesign);
        worktaskSubmit.Name = nameof(worktaskSubmit.Name);

        worktaskSubmit.WorkActivityCompleteScript = new WorkTaskScriptSubmit
        {
            Script = "WorkActivityCompleteScript"
        };
        worktaskSubmit.WorkTaskCompleteScript = new WorkTaskScriptSubmit
        {
            Script = "WorkTaskScriptSubmit"
        };

        worktaskSubmit.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "同意",
            Script = "同意"
        });
        worktaskSubmit.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "拒绝",
            Script = "拒绝"
        });

        var context = new WorkflowSubmitContext(wp, new NodeStoreMock(wp));
        context.OverWrite = false; //不覆盖,
        var a = new WorkTaskTranslator();
        var node = a.GetOrCreate(worktaskSubmit, context);

        Assert.Equal(worktaskSubmit.Name, node.Name);

        Assert.Equal(worktaskSubmit.FormDesign, node.FormDesign);
        Assert.False(worktaskSubmit.Auto);
        Assert.Equal(0, node.Id); //所有的Worktask必须是 0

        Assert.Equal(worktaskSubmit.WorkActivityCompleteScript.Script, node.WorkActivityCompleteScript.Script);
        Assert.Equal(worktaskSubmit.WorkTaskCompleteScript.Script, node.WorkTaskCompleteScript.Script);

        var 同意Command = (WorkTaskScriptCommandSubmit)worktaskSubmit.Commands.First(_ => _.Name == "同意");
        Assert.Equal(同意Command.Name, node.Commands.First(_ => _.Name == "同意").Name);
        Assert.Equal(同意Command.Script, ((WorkTaskScriptCommand)node.Commands.First(_ => _.Name == "同意")).Script);


        var 拒绝Command = (WorkTaskScriptCommandSubmit)worktaskSubmit.Commands.First(_ => _.Name == "拒绝");
        Assert.Equal(拒绝Command.Name, node.Commands.First(_ => _.Name == "拒绝").Name);
        Assert.Equal(拒绝Command.Script, ((WorkTaskScriptCommand)node.Commands.First(_ => _.Name == "拒绝")).Script);


        Assert.Null(node.NextNode);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void WorkTaskSubmitConstruction()
    {
        var wp = new WorkProcess("wp");
        var workTask = new WorkTask("workTask", wp);
        workTask.Auto = true;
        workTask.FormDesign = nameof(workTask.FormDesign);
        workTask.WorkActivityCompleteScript = new WorkActivityScript
        {
            Script = "WorkActivityCompleteScript"
        };

        workTask.NextTaskPerformers = NextTaskPerformers.Manual;
        var scriptAssigner = new ScriptAssigner
        {
            Id = 11,
            AssignScopeType = AssignScopeType.AllOfThem,
            Script = "AssignerScript"
        };
        workTask.Assigner = scriptAssigner;
        var translator = new WorkTaskTranslator();
        var target = translator.ToViewModel(workTask);


        Assert.Equal(workTask.Name, target.Name);
        Assert.Equal(workTask.FormDesign, target.FormDesign);
        Assert.Equal(workTask.WorkActivityCompleteScript.Script, target.WorkActivityCompleteScript.Script);
        Assert.Equal(workTask.NextTaskPerformers, target.NextTaskPerformers);
        Assert.True(workTask.Auto);
        Assert.Equal(workTask.Name, target.Name);

        //assigner
        Assert.Equal(workTask.Assigner.AssignScopeType, workTask.Assigner.AssignScopeType);
        Assert.Equal(scriptAssigner.Id, workTask.Assigner.Id);
        var assigner = (ScriptAssigner)workTask.Assigner;
        Assert.Equal(scriptAssigner.Script, assigner.Script);
    }

    /// <summary>
    /// </summary>
    public class NodeStoreMock : INodeStore
    {
        private readonly WorkProcess _wp;

        /// <summary>
        /// </summary>
        /// <param name="wp"></param>
        public NodeStoreMock(WorkProcess wp)
        {
            _wp = wp;
        }

        /// <summary>
        /// </summary>
        /// <param name="id"></param>
        /// <exception cref="NotImplementedException"></exception>
        public void Delete(int id)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Nodes.Node GetById(int id)
        {
            return new WorkTask("workTask", _wp)
            {
                Id = id
            };
        }

        /// <summary>
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="id"></param>
        /// <returns></returns>
        public Task<T> GetByIdAsync<T>(int id) where T : Nodes.Node
        {
            var a = GetById(id);
            return Task.FromResult((T)a);
        }

        public void AddOrUpdate(Nodes.Node workTask)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task SaveChangesAsync()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// </summary>
        /// <param name="workProcess"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task<IEnumerable<Nodes.Node>> GetNodesByWorkProcessAsync(WorkProcess workProcess)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// </summary>
        /// <param name="removeNodes"></param>
        /// <exception cref="NotImplementedException"></exception>
        public void Remove(IEnumerable<Nodes.Node> removeNodes)
        {
            throw new NotImplementedException();
        }

        public Task<Nodes.Node> GetByNodeAsync(WorkProcess wp, string nodeName)
        {
            throw new NotImplementedException();
        }
    }
}