﻿//using System;
//using System.Net.Http;
//using Coder.ScriptWorkflow.UnitTest.Http.Helper;
//using NiL.JS.Core;
//using Xunit;

//namespace Coder.ScriptWorkflow.UnitTest.Plugins;

//public class MockHttpClient : IHttpClientFactory
//{
//    private readonly CoderWebAppFactory _factory;

//    public MockHttpClient(CoderWebAppFactory factory)
//    {
//        _factory = factory;
//    }

//    public HttpClient CreateClient(string name)
//    {
//        return _factory.CreateClient();
//    }

//    public HttpClient CreateClient()
//    {
//        return _factory.CreateClient();
//    }
//}

///// <summary>
///// </summary>
//public class HttpClientPluginTest : IClassFixture<CoderWebAppFactory>, IDisposable
//{
//    private readonly CoderWebAppFactory _factory;

//    public HttpClientPluginTest(CoderWebAppFactory factory)
//    {
//        _factory = factory;
//    }

//    /// <summary>
//    /// </summary>
//    public void Dispose()
//    {
//        //var dbContext = _factory.Services.GetService(typeof(ApplicationDbContext)) as ApplicationDbContext;
//        //dbContext.Database.EnsureDeleted();
//    }

//    private JSValue GetFrom()
//    {
//        var con = new Context();
//        con.Eval("var jjjj={String:'string',Int32:11}");
//        var ff = con.GetVariable("jjjj");

//        return ff;
//    }

//    /// <summary>
//    /// </summary>
//    [Fact]
//    public void Test1_PostForm()
//    {
//        var client = new JsHttpPlugin(new MockHttpClient(_factory));
//        var postData = GetFrom();
//        var result = client.PostForm("JsHttpClient/PostForm", postData);
//        Assert.Equal(200, result.StatusCode);
//        Assert.Equal("{\"String\":\"string\",\"Int32\":11}", result.Body);
//    }

//    /// <summary>
//    /// </summary>
//    [Fact]
//    public void Test1_PostJSON()
//    {
//        var client = new JsHttpPlugin(new MockHttpClient(_factory));
//        var postData = GetFrom();
//        var result = client.PostJson("JsHttpClient/PostJSON", postData);
//        Assert.Equal(200, result.StatusCode);
//        Assert.Equal("{\"String\":\"string\",\"Int32\":11}", result.Body);
//    }

//    [Fact]
//    public void Test1_PostJSON_ServerNotValidable()
//    {
//        var client = new JsHttpPlugin(new MockHttpClient(_factory));
//        var postData = GetFrom();
//        var result = client.PostJson("http://notexit.com:65533/JsHttpClient/PostJSON", postData);
//        Assert.Equal(404, result.StatusCode);
//    }
//}

