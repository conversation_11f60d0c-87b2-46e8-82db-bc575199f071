﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow.Tags;

/// <summary>
/// </summary>
public class TagManager
{
    private readonly IPerformerQueryStore _performerQueryStore;
    private readonly IProcessInstanceStore _processInstanceStore;
    private readonly TagUserTagsCache _tagCache;
    private readonly ITagStore _tagStore;

    /// <summary>
    /// </summary>
    /// <param name="tagCache"></param>
    /// <param name="tagStore"></param>
    /// <param name="processInstanceStore"></param>
    /// <param name="performerQueryStore"></param>
    public TagManager(TagUserTagsCache tagCache, ITagStore tagStore, IProcessInstanceStore processInstanceStore,
        IPerformerQueryStore performerQueryStore)
    {
        _tagCache = tagCache;
        _tagStore = tagStore;
        _processInstanceStore = processInstanceStore;
        _performerQueryStore = performerQueryStore;
    }

    /// <summary>
    /// </summary>
    /// <param name="process"></param>
    /// <returns></returns>
    public async Task DeleteTags(ProcessInstance process)
    {
        var result = await _tagStore.GetProcessInstanceTagsAsync(process.Id);

        foreach (var processInstanceTag in result) RemoveTag(process, processInstanceTag.Tag.Name);
    }

    /// <summary>
    /// </summary>
    /// <param name="piIds"></param>
    /// <returns></returns>
    public async Task<Dictionary<int, List<ProcessInstanceTagViewModel>>> GetTagsByProcessInstanceId(int[] piIds)
    {
        var processInstanceTags = await _processInstanceStore.GetTagsByIdsAsync(piIds);

        var result = new Dictionary<int, List<ProcessInstanceTagViewModel>>(); //processinstance 与 tag之间的关系。
        //准备执行 远程获取用户名称。
        //key 是用户名称。value是对应的tag。
        var notInCacheUserTagMapping = new Dictionary<string, List<TagViewModel>>(); //存放tag 带有用户名称。

        foreach (var ptTag in processInstanceTags)
        {
            if (!result.TryGetValue(ptTag.ProcessInstanceId, out var tags))
            {
                tags = new List<ProcessInstanceTagViewModel>();
                result.Add(ptTag.ProcessInstanceId, tags);
            }

            tags.Add(ptTag);

            //以下是把带登录名称的tag 替换为中文tag。
            if (ptTag.Name.StartsWith(Tag.DisposeUserNameKey) || ptTag.Name.StartsWith(Tag.CCUserNameKey))
            {
                if (_tagCache.TryGetUserTag(ptTag.Name, out var newTagName))
                {
                    ptTag.Name = newTagName;
                }
                else
                {
                    //cache中，没有这个tag的信息。
                    var userName = Tag.GetDataAfterColon(ptTag.Name).ToUpper();
                    if (!notInCacheUserTagMapping.TryGetValue(userName, out var replaceTags))
                    {
                        replaceTags = new List<TagViewModel>();
                        notInCacheUserTagMapping.Add(userName, replaceTags);
                    }

                    replaceTags.Add(ptTag);
                }
            }
        }

        // cache 不存在的 需要远程获取中文名称。
        if (notInCacheUserTagMapping.Any())
        {
            var users = await _performerQueryStore.GetByUserNamesAsync(notInCacheUserTagMapping.Select(_ => _.Key));
            foreach (var user in users)
                if (notInCacheUserTagMapping.TryGetValue(user.UserName.ToUpper(), out var tags))
                    foreach (var tag in tags)
                    {
                        var beforeReplace = tag.Name;
                        tag.Name = Tag.ReplaceDataAfterColon(tag.Name, user.Name);
                        _tagCache.AddUserTag(beforeReplace, tag.Name);
                    }
        }

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="process"></param>
    /// <param name="tag"></param>
    public void RemoveTag(ProcessInstance process, string tag)
    {
        var piTag = _processInstanceStore.GetTagByTagName(process, tag);
        if (piTag != null)
            _processInstanceStore.RemoveTag(piTag);

        var tagIns = _tagStore.GetByName(tag);
        if (tagIns != null)
        {
            _processInstanceStore.SaveChanges();
            //更新引用数目
            _tagStore.UpdateAddReference(new[] { tagIns.Name });
        }
    }

    /// <summary>
    ///     添加Tag
    /// </summary>
    /// <param name="process"></param>
    /// <param name="tag"></param>
    /// <param name="color"></param>
    public void AddTag(ProcessInstance process, TagSubmit tag)
    {
        tag.Color ??= Tag.Random();

        var piTag = _processInstanceStore.GetTagByTagName(process, tag.Name);
        if (piTag != null) return;

        piTag = new ProcessInstanceTag
        {
            ProcessInstance = process,
            Color = tag.Color
        };

        var tagIns = _tagStore.GetByName(tag.Name);
        if (tagIns == null)
        {
            tagIns = new Tag
            {
                Name = tag.Name
            };
            _tagStore.AddOrUpdate(tagIns);
        }

        piTag.Tag = tagIns;

        _processInstanceStore.AddOrUpdate(piTag);
        _processInstanceStore.SaveChanges();
        //更新引用数目
        _tagStore.UpdateAddReference(new[] { piTag.Tag.Name });
    }

    /// <summary>
    ///     与ProcessInstance进行合并。
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="tags"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void MergeTags(ProcessInstance processInstance, IEnumerable<ScriptTagInfo> tags)
    {
        if (processInstance == null) throw new ArgumentNullException(nameof(processInstance));
        var processInstanceTags = _processInstanceStore.GetTagsAsync(processInstance).Result;

        //删除被移除的标记
        var beRemoveItem = tags.Where(a => a.TagStatus == TagChanged.Removed).Select(_ => _.TagName);
        //存放到列表中,如果后面新增加的标签，那么采用更换的方式。

        var removeInDb = new Queue<ProcessInstanceTag>(processInstanceTags.Where(_ => beRemoveItem.Contains(_.Tag.Name)));

        var newTags = tags.Where(a => a.TagStatus == TagChanged.Added).ToArray();
        var tagNames = newTags.Select(_ => _.TagName).ToArray();
        var tagInDb = _tagStore.GetOrCreate(tagNames).ToDictionary(_ => _.Name, _ => _);
        foreach (var newTag in newTags)
        {
            ProcessInstanceTag newProcessInstanceTag = null;
            if (removeInDb.Any())
                newProcessInstanceTag = removeInDb.Dequeue();
            else
                newProcessInstanceTag = new ProcessInstanceTag
                {
                    ProcessInstance = processInstance
                };
            newProcessInstanceTag.Tag = tagInDb[newTag.TagName];
            newProcessInstanceTag.Color = newTag.Color;
            _processInstanceStore.AddOrUpdate(newProcessInstanceTag);
        }

        while (removeInDb.Any()) _processInstanceStore.RemoveTag(removeInDb.Dequeue());
        _tagStore.UpdateRemoveReference(beRemoveItem);
        _tagStore.UpdateAddReference(tagNames);
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="tag"></param>
    public void UpdateTags(ProcessInstance processInstance, TagsSubmit tag)
    {
        var processInstanceTags = _processInstanceStore.GetTagsAsync(processInstance).Result;
        var willBeDeleteTags = processInstanceTags.ToDictionary(_ => _.Tag.Name, _ => _);
        var result = new List<ScriptTagInfo>();

        foreach (var submitTag in tag.Tags)
            if (willBeDeleteTags.ContainsKey(submitTag.Name))
            {
                var piTag = willBeDeleteTags[submitTag.Name];
                willBeDeleteTags.Remove(submitTag.Name);
                result.Add(new ScriptTagInfo
                {
                    Color = null,
                    ProcessInstanceTagId = piTag.Id,
                    TagName = submitTag.Name,
                    TagStatus = TagChanged.Removed
                });
            }
            else
            {
                result.Add(new ScriptTagInfo
                {
                    Color = submitTag.Color,
                    ProcessInstanceTagId = 0,
                    TagName = submitTag.Name,
                    TagStatus = TagChanged.Added
                });
            }

        MergeTags(processInstance, result);
    }

}