﻿using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Node;

public class WorkProcessTest
{
    [Fact]
    public void TestAdd()
    {
        /*
         * 起草->审批->发布->结束
         *
         */
        var wp = new WorkProcess("wpName");
        var startNode = new StartNode();
        var draf = new WorkTask("起草", wp)
        {
            Assigner = new ScriptAssigner
            {
                Script = "user"
            }
        };
        var auth = new WorkTask("审批", wp)
        {
            Assigner = new ScriptAssigner
            {
                Script = "auth-user"
            }
        };
        var publish = new WorkTask("发布", wp)
        {
            Assigner = new ScriptAssigner
            {
                Script = "publish-user"
            }
        };
        var end = new EndNode(wp);

        startNode.NextNode = draf;

        var authDecision = new BoolScriptDecision(publish, draf);
        auth.NextNode = authDecision;
        publish.NextNode = end;
    }
}