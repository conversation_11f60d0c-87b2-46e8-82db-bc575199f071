﻿using System.Collections.Generic;

namespace Coder.ScriptWorkflow.ViewModels.WorkProcesses;

/// <summary>
/// </summary>
public class WorkProcessListSearcher : Pager
{
    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public bool? Enable { get; set; }

    /// <summary>
    /// </summary>
    public ICollection<string> Names { get; set; }

    /// <summary>
    /// </summary>
    public bool IsAdmin { get; set; } = false;
}