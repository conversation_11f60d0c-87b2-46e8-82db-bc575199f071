﻿using System;
using System.Security.Claims;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Newtonsoft.Json;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using Array = NiL.JS.BaseLibrary.Array;

namespace Coder.ScriptWorkflow.Scripts;

public static class ScriptDtoExtension
{

    public static void SetJsContext(this ProcessInstance pi, bool isDraRun, Context context)
    {
        var model = pi.ToScriptModel(isDraRun);

        context.DefineVariable("processInstance").Assign(context.GlobalContext.WrapValue(model));

    }

    public static ScriptProcessInstance ToScriptModel(this ProcessInstance pi, bool isDryRun)
    {
        if (pi == null) throw new ArgumentNullException(nameof(pi));
        var result = new ScriptProcessInstance(pi);
        result.WorkProcess = pi.WorkProcess.ToScripModel();
        result.Creator = pi.Creator;
        result.Number = pi.Number;
        result.Id = pi.Id;
        result.Status = (int)pi.Status;
        if (pi.FinishTime != null)
            result.FinishTime = new Date(pi.FinishTime.Value);
        result.CreateTime = new Date(pi.CreateTime); ;

        result.IsDryRun = isDryRun;
        result.Form = JSON.parse(pi.Form ?? "{}");
        return result;
    }
    /// <summary>
    /// 把WorkActivity换成js的对象。
    /// </summary>
    /// <param name="wa"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static ScriptWorkActivity ToScriptModel(this WorkActivity wa)
    {
        if (wa == null) throw new ArgumentNullException(nameof(wa));
        var result = new ScriptWorkActivity();

        result.Id = wa.Id;
        result.Status = (int)wa.Status;
        result.DisposeUser = wa.DisposeUser;
        result.DisposeUserName = wa.DisposeUserName;
        if (wa.DisposeTime != null)
            result.DisposeTime = new Date(wa.DisposeTime.Value);
        result.Comment = wa.Comment;
        result.WorkTaskName = wa.WorkTask.Name;
        result.Command = wa.Command;
        return result;
    }

    /// <summary>
    ///     转换为script 中的 model
    /// </summary>
    /// <param name="workProcess"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static ScriptWorkProcess ToScripModel(this WorkProcess workProcess)
    {
        if (workProcess == null) throw new ArgumentNullException(nameof(workProcess));

        var result = new ScriptWorkProcess();
        result.Id = workProcess.Id;
        result.Name = workProcess.Name;

        return result;
    }

    /// <summary>
    ///     切换为 当前 用户
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public static JSValue ToScriptModel(this ClaimsPrincipal user)
    {
        var result = JSObject.CreateObject();

        result.DefineProperty("userName").Assign(user.Identity.Name);

        var roles = new Array();
        result.DefineProperty("roles").Assign(roles);

        var claims = new Array();
        result.DefineProperty("claims").Assign(claims);

        foreach (var claim in user.Claims)
            switch (claim.Type)
            {
                case "real_name":
                    result["name"] = claim.Value;
                    break;
                case ClaimTypes.Role:
                    roles.Add(claim.Value);
                    break;
                default:
                    var c = JSObject.CreateObject();
                    c.DefineProperty("type").Assign(claim.Type);
                    c.DefineProperty("value").Assign(claim.Value);
                    claims.Add(c);

                    break;
            }

        return result;

        //var swf = new SwfUser();
        //swf.UserName = user.Identity.Name;


        //foreach (var claim in user.Claims)
        //{
        //    switch (claim.Type)
        //    {
        //        case "real_name":
        //            swf.Name = claim.Value;
        //            break;
        //        case ClaimTypes.Role:
        //            swf.Roles.Add(claim.Value);
        //            break;
        //        default:
        //            swf.Add(claim.Type, claim.Value);
        //            break;
        //    }
        //}

        //return swf;
    }
}