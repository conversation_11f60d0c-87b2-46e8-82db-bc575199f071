﻿using Coder.ScriptWorkflow.Stores;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow;

internal class TransactionFactory<T> : ITransactionFactory where T : DbContext
{
    private readonly T _dbContext;

    public TransactionFactory(T dbContext)
    {
        _dbContext = dbContext;
        ;
    }

    public ITransactionManage BeginTransactions()
    {
        var trans = _dbContext.Database.CurrentTransaction == null ? _dbContext.Database.BeginTransaction() : null;

        return new TransactionManage(trans);
        ;
    }
}