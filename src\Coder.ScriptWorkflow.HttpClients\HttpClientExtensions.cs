﻿using Coder.ScriptWorkflow.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow.HttpClients
{
    public static class HttpClientExtensions
    {
        public static IServiceCollection AddScriptWorkflowHttpClient(this IServiceCollection services, string host, string prefix = "swf")
        {
            if (host == null) throw new ArgumentNullException(nameof(host));
           
            if (!host.EndsWith("/")) host += "/";

            var path = host + prefix;
            if (!path.EndsWith("/")) path += "/";

            services.AddHttpClient(path, client =>
            {
                client.BaseAddress = new Uri(path);
            });
            services.AddTransient<IProcessInstanceClient, ProcessInstanceClient>(sp =>
            {

                var httpClient = sp.GetRequiredService<IHttpClientFactory>().CreateClient(path);
                var result = new ProcessInstanceClient(httpClient);
                return result;
            });
            return services;


        }
    }
}
