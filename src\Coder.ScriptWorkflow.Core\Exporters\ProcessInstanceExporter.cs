﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Principal;
using Coder.Member.ViewModels.Users;
using Coder.Object2Report;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Coder.Utility;

namespace Coder.ScriptWorkflow.Exporters;

/// <summary>
/// </summary>
public class ProcessInstanceExporter
{
    /// <summary>
    ///     /
    /// </summary>
    /// <param name="data"></param>
    /// <param name="user"></param>
    /// <param name="users"></param>
    /// <param name="fileName"></param>
    public static void Write(IEnumerable<InstanceListItemViewModel> data, IIdentity user,
        IEnumerable<UserViewModel> users, out string fileName)
    {
        var file = GetTempPrivateFile(".xlsx", user);
        var result = new Report<InstanceListItemViewModel>();
        result.Column("工单号", _ => _.Number);
        result.Column("工单主题", _ => _.Subject);
        result.Column("工单状态", _ => _.Status.GetEnumDisplayName());
        result.Column("流程名称", _ => _.WorkProcessName);
        result.Column("创建时间", _ => _.CreateTime.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.CurrentCulture));
        result.Column("结束时间",
            _ => _.FinishTime.HasValue
                ? _.FinishTime.Value.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.CurrentCulture)
                : "");
        result.Column("创建用户", _ => _.Creator != null ? users.FirstOrDefault(u => u.UserName == _.Creator)?.Name : null);
        result.Column("当前任务", _ => _.WorkTaskName);
        result.Column("任务状态", _ => _.WorkActivityStatus.GetEnumDisplayName());
        result.Column("处理人",
            _ => _.WorkActivityDisposeUser != null
                ? users.FirstOrDefault(u => u.UserName == _.WorkActivityDisposeUser)?.Name
                : null);
        fileName = file;
        result.WriteToXlsx(data, file, "我的工单" + DateTime.Now.ToString("yyyy-MM-dd"));
    }

    /// <summary>
    /// </summary>
    /// <param name="extension"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public static string GetTempPrivateFile(string extension, IIdentity user)
    {
        if (!extension.StartsWith(".")) extension = "." + extension;
        var direct = GetPrivateTempFolder(user);
        var tempFileName = Guid.NewGuid().ToString("N", CultureInfo.CurrentCulture) + extension;
        return Path.Combine(direct, tempFileName);
    }

    /// <summary>
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static string GetPrivateTempFolder(IIdentity user)
    {
        if (user is null) throw new ArgumentNullException(nameof(user));
        //var direct = new DirectoryInfo("upload");
        //if (!direct.Exists) direct.Create();
        var direct = Path.Combine(Directory.GetCurrentDirectory(), "Upload", user.Name);

        if (Directory.Exists(direct) == false) Directory.CreateDirectory(direct);
        direct = Path.Combine(direct, "temp");
        if (Directory.Exists(direct) == false) Directory.CreateDirectory(direct);
        return direct;
    }
}