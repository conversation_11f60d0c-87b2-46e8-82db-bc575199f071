﻿using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Assigners;

/// <summary>
/// </summary>
public class UsersAssigner : Assigner
{
    /// <summary>
    /// </summary>
    public UsersAssigner()
    {
        AssignScopeType = AssignScopeType.SomeOfThem;
    }

    ///// <summary>
    /////     派发角色类型
    ///// </summary>
    //public PerformerType PerformerType { get; set; }

    /// <summary>
    /// </summary>
    public IList<Performer> Performers { get; set; } = new List<Performer>();

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="previous"></param>
    /// <returns></returns>
    public override AssignResult Assign(IWorkflowContext context, IEnumerable<WorkActivity> previous)
    {
        return new AssignResult(AssignScopeType)
        {
            //PerformerType = PerformerType,
            Performers = Performers.ToArray(),
           // ScopeType = AssignScopeType
        };
    }
}