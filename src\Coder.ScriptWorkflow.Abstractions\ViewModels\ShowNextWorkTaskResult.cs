﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;


namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     获取下一步执行人信息的结果
/// </summary>
public class ShowNextWorkTaskResult 
{
    private readonly Dictionary<string, AssignUser> _mapping = new();

    /// <summary>
    ///     派发范围。
    /// </summary>
    public AssignScopeType AssignScopeType { get; set; }

    /// <summary>
    /// </summary>
    public string WorkTaskName { get; set; }

    /// <summary>
    ///     结束
    /// </summary>
    public bool IsEnd { get; set; }

    /// <summary>
    ///     工作活动是否结束。
    /// </summary>
    public bool WorkTaskIsEnd { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; }
    /// <summary>
    /// </summary>
    public IEnumerable<AssignUser> AssignUsers => _mapping.Values;

    /// <summary>
    /// </summary>
    /// <param name="users"></param>
    public void AddWorkActivity(params AssignUser[] users)
    {
        if (users == null) throw new ArgumentNullException(nameof(users));
        foreach (var user in users) _mapping.TryAdd(user.UserName, user);
    }
}