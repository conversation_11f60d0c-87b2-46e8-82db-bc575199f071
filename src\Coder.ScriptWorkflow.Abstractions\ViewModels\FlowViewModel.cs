﻿using System;

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     流程ViewModel
/// </summary>
public class FlowViewModel
{
    /// <summary>
    ///     WorkActivity's Status
    /// </summary>
    public WorkActivityStatus Status { get; set; }

    /// <summary>
    ///     WorkTask's Id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     工作任务名称
    /// </summary>
    public string TaskName { get; set; }

    /// <summary>
    ///     处理人名称
    /// </summary>
    public string DisposeUserName { get; set; }

    /// <summary>
    ///     分配时间
    /// </summary>
    public DateTime? AssignTime { get; set; }

    /// <summary>
    ///     处理时间
    /// </summary>
    public DateTime? DisposeTime { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public virtual string WorkActivityDescription
    {
        get
        {
            var str = "";
            if (!string.IsNullOrEmpty(DisposeUserName))
            {
                //var assignTime = AssignTime?.ToString("yyyy-MM-dd HH:mm");
                var depositTime = DisposeTime?.ToString("yyyy-MM-dd HH:mm");

                //str += "于" + assignTime + "由" + DisposeUserName + "接受处理";
                str += "由" + DisposeUserName + "接受处理";
                str += depositTime != null ? ",并完成于" + depositTime + " 。" : "。";
            }
            else if (DisposeTime == null)
            {
                //str += CreateTime.ToString("yyyy-MM-dd HH:mm") + "创建,等待接受处理.";
                str += "任务创建,等待接受处理。";
            }

            return str;
        }
    }

    /// <summary>
    ///     命令
    /// </summary>
    public string Command { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Comment { get; set; }
}