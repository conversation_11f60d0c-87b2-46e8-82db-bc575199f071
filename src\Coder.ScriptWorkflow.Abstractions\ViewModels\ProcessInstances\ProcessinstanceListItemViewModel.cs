﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Permissions;

namespace Coder.ScriptWorkflow.ViewModels.ProcessInstances;

/// <summary>
///     工作流实例列表，如果工作已经启动，那么包含了最后一条/正在处理中WorkActivity的信息
/// </summary>
public class InstanceListItemViewModel : IWorkActivityWithPermission, IProcessInstanceWithPermission
{
    /// <summary>
    ///     流程名称。
    /// </summary>
    public string WorkProcessName { get; set; }

    /// <summary>
    /// </summary>
    public int WorkActivityId { get; set; }

    /// <summary>
    /// </summary>
    public string WorkTaskName { get; set; }

    /// <summary>
    /// </summary>
    public WorkActivityStatus WorkActivityStatus { get; set; }

    /// <summary>
    ///     工作活动处理人的登录名称
    /// </summary>
    public string WorkActivityDisposeUser { get; set; }

    /// <summary>
    ///     工作活动处理显示名称
    /// </summary>
    public string WorkActivityDisposeUserName { get; set; }

    /// <summary>
    /// </summary>
    public IEnumerable<Performer> AssignPerformers { get; set; }

    ///// <summary>
    ///// </summary>
    //public string AssignScope { get; set; }

    /// <summary>
    ///     完成时间
    /// </summary>
    public DateTimeOffset? FinishTime { get; set; }


    /// <summary>
    ///     工作流实例主题
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    ///     工作流创建人
    /// </summary>
    public string Creator { get; set; }

    /// <summary>
    ///     创建者用户名称
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     工作流状态
    /// </summary>
    public ProcessInstanceStatus Status { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>

    public DateTimeOffset CreateTime { get; set; }

    /// <summary>
    ///     工单编码
    /// </summary>
    public string Number { get; set; }


    /// <summary>
    ///     工作流备注
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    ///     工作流所带的Tag
    /// </summary>
    public TagViewModel[] Tags { get; set; }

    /// <summary>
    /// </summary>
    public bool WorkTaskCanGiveUp { get; set; }

    /// <summary>
    ///     优先级
    /// </summary>
    public Priority ProcessInstancePriority { get; set; }

    /// <summary>
    ///     当前执行的活动数目
    /// </summary>
    public int WorkActivityCount { get; set; }

    /// <summary>
    ///     当前
    /// </summary>
    public int CanDeleteWorkActivityCount { get; set; }

    /// <inheritdoc />
    public bool CanDeleteProcessInstance { get; set; }

    /// <inheritdoc />
    public bool IsInstanceCreator { get; set; }

    /// <inheritdoc />
    public string GetProcessInstanceCreator()
    {
        return Creator;
    }

    /// <summary>
    ///     当前处理人是否为抄送用户
    /// </summary>

    public bool IsCCUser { get; set; } = false;

    /// <summary>
    ///     cc用户 是否已经阅读了。
    /// </summary>
    public bool HasRead { get; set; } = false;


    /// <inheritdoc />
    public ProcessInstanceStatus GetProcessInstanceStatus()
    {
        return Status;
    }

    /// <summary>
    ///     当前用户是否创建者
    /// </summary>
    public bool IsWorkProcessCreator { get; set; }

    /// <summary>
    ///     当前用户是否Manager
    /// </summary>
    public bool IsManager { get; set; }

    /// <summary>
    ///     当前用户：是否能够启动 工作流程。
    /// </summary>
    public bool CanCreateProcessInstance { get; set; }

    public string GetWorkProcessCreator()
    {
        return Creator;
    }

    /// <summary>
    ///     当前用户是否能够处理任务。
    /// </summary>
    public bool CanDisposeWorkActivity { get; set; }

    /// <inheritdoc />

    public bool CanGiveUpWorkActivity { get; set; }

    /// <inheritdoc />
    public bool CanAcceptWorkActivity { get; set; }
    /// <summary>
    /// 流程id
    /// </summary>
    public int? WorkProcessId { get; set; }

    /// <inheritdoc />
    public string GetDisposeUser()
    {
        return WorkActivityDisposeUser;
    }

    /// <inheritdoc />
    public WorkActivityStatus GetWorkActivityStatus()
    {
        return WorkActivityStatus;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public bool GetWorkTaskGiveUpSetting()
    {
        return WorkTaskCanGiveUp;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public IEnumerable<Performer> GetPerformers()
    {
        return AssignPerformers;
    }
}