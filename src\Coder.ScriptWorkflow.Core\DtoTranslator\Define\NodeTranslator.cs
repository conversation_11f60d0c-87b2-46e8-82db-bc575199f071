﻿using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

/// <summary>
/// </summary>
/// <typeparam name="TNodeSubmit"></typeparam>
/// <typeparam name="TNode"></typeparam>
public abstract class NodeTranslator<TNodeSubmit, TNode> : ITranslator
    where TNodeSubmit : NodeSubmit, new()
    where TNode : Node, new()
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    public NodeSubmit ToViewModel(Node node)
    {
        return ToViewModel((TNode)node);
    }

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    Node ITranslator.GetOrCreate(NodeSubmit nodeSubmit, WorkflowSubmitContext context)
    {
        return GetOrCreate((TNodeSubmit)nodeSubmit, context);
    }

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public bool Validate(NodeSubmit nodeSubmit, out string errorMessage)
    {
        return Validate((TNodeSubmit)nodeSubmit, out errorMessage);
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="currentNode"></param>
    /// <param name="nodeSubmit"></param>
    public void BuildRelation(WorkflowSubmitContext context, Node currentNode, NodeSubmit nodeSubmit)
    {
        BuildEntityRelation(context, (TNode)currentNode, (TNodeSubmit)nodeSubmit);
    }

    /// <summary>
    ///     把submit得ViewModel 赋值到node
    /// </summary>
    /// <param name="node"></param>
    /// <param name="submit"></param>
    /// <param name="context"></param>
    /// <param name="nodeStore"></param>
    protected abstract void FillToEntity(TNode node, TNodeSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore);

    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    public TNodeSubmit ToViewModel(TNode node)
    {
        var result = new TNodeSubmit();
        result.Id = node.Id;
        result.Name = node.Name;

        if (node.NextNode != null)
            result.NextNodeName = node.NextNode.Name;
        FillToViewModel(node, result);
        result.Type = $"{result.GetType().FullName}, Coder.ScriptWorkflow.Abstractions";
        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public virtual bool Validate(TNodeSubmit nodeSubmit, out string errorMessage)
    {
        errorMessage = null;
        if (string.IsNullOrEmpty(nodeSubmit.NextNodeName))
        {
            errorMessage = $"{nodeSubmit.Name}-下一个节点必须填写";
            return false;
        }

        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="target"></param>
    protected abstract void FillToViewModel(TNode src, TNodeSubmit target);

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    public TNode GetOrCreate(TNodeSubmit nodeSubmit, WorkflowSubmitContext context)
    {
        TNode node;


        node = nodeSubmit.Id == 0 || context.OverWrite == false ? new TNode() : context.GetById<TNode>(nodeSubmit.Id);


        node.Name = nodeSubmit.Name;
        node.WorkProcess = context.WorkProcess;
        FillToEntity(node, nodeSubmit, context, context.NodeStore);


        return node;
    }

    /// <summary>
    ///     创建节点之间的关系。
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected virtual void BuildEntityRelation(WorkflowSubmitContext context, TNode node, TNodeSubmit nodeSubmit)
    {
        if (context.WorkProcess.Enable && string.IsNullOrEmpty(nodeSubmit.NextNodeName))
            throw new WorkflowDefinedException(nodeSubmit.Name + "需要设置‘下一个节点’");

        if (nodeSubmit.NextNodeName != null)
            node.NextNode = context.GetNode(nodeSubmit.NextNodeName);
    }
}