﻿using Coder.ScriptWorkflow;
using Coder.ScriptWorkflow.Stores;
using Coder.VBen.Menu;
using Coder.VBen.Menu.Clients;
using Coder.VBen.Menu.ViewModels;
using ToolGood.Words;

namespace Coder.VBenMenu.Interceptor;

public class MenuManager
{
    private readonly INodeStore _nodeStore;
    private readonly IRouteClient _routeClient;

    public MenuManager(INodeStore nodeStore, IRouteClient routeClient)
    {
        _nodeStore = nodeStore;
        _routeClient = routeClient;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <param name="isUpdate"></param>
    public void ToUpdateMenu(WorkProcess workProcess, bool isUpdate)
    {
        if (isUpdate)
            AddMenu(workProcess);
        else
            UpdateMenu(workProcess);
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    private void UpdateMenu(WorkProcess workProcess)
    {
        var routeName = workProcess.Prefix + WordsHelper.GetPinyin(workProcess.Name);
        var processMeun = _routeClient.GetByRouteNameAsync(routeName).Result;
        if (processMeun != null)
        {
            var parentId = processMeun.Id;
            var nodes = _nodeStore.GetNodesByWorkProcessAsync(workProcess).Result;
            var i = 1;
            foreach (var node in nodes)
                if (node is WorkTask taks)
                {
                    var taksName = workProcess.Prefix + WordsHelper.GetPinyin(taks.Name);
                    var taksMenu = _routeClient.GetByRouteNameAsync(taksName).Result;
                    var submitMenu = new RouteSubmit
                    {
                        Name = taksName,
                        Path = taksName,
                        Redirect = "/workflows/worlTask/" + workProcess.Name + "/" + taks.Name,
                        Component = "LAYOUT",
                        Meta = new RouteMeta
                        {
                            Title = taks.Name,
                            Icon = "microServer|svg"
                        },
                        SortOrder = i,
                        ParentId = parentId,
                        Enable = true
                    };
                    if (taksMenu != null) submitMenu.Id = taksMenu.Id;
                    _routeClient.SaveOrUpdateAsync(submitMenu);
                }
        }
        else
        {
            AddMenu(workProcess);
        }
    }


    private void AddMenu(WorkProcess workProcess)
    {
        var routeName = workProcess.Prefix + WordsHelper.GetPinyin(workProcess.Name);
        var processMenu = new RouteSubmit() //主菜单
        {
            Name = routeName,
            Path = "/" + routeName,
            Redirect = "/" + routeName + "/list",
            Component = "LAYOUT",
            Meta = new RouteMeta
            {
                Title = workProcess.Name,
                Icon = "service|svg"
            }, //gateway
            SortOrder = 66,
            Enable = true
        };
        var ss = _routeClient.SaveOrUpdateAsync(processMenu).Result;
        var parentId = ss.Id;
        var listMenu = new RouteSubmit() //列表菜单
        {
            Name = workProcess.Prefix + "list",
            Path = "list",
            Redirect = "/workflows/works/" + workProcess.Name,
            Component = "LAYOUT",
            Meta = new RouteMeta
            {
                Title = workProcess.Name + "列表",
                Icon = "microServer|svg"
            },
            SortOrder = 0,
            ParentId = parentId,
            Enable = true
        };
        _routeClient.SaveOrUpdateAsync(listMenu);
        var nodes = _nodeStore.GetNodesByWorkProcessAsync(workProcess).Result;
        var i = 1;
        foreach (var node in nodes)
            if (node is WorkTask taks)
            {
                var taksName = workProcess.Prefix + WordsHelper.GetPinyin(taks.Name);
                var nodeMenu = new RouteSubmit() //工作项菜单
                {
                    Name = taksName,
                    Path = taksName,
                    Redirect = "/workflows/worlTask/" + workProcess.Name + "/" + taks.Name,
                    Component = "LAYOUT",
                    Meta = new RouteMeta
                    {
                        Title = taks.Name,
                        Icon = "microServer|svg"
                    },
                    SortOrder = i,
                    ParentId = parentId,
                    Enable = true
                };
                _routeClient.SaveOrUpdateAsync(nodeMenu);
                i++;
            }
    }
}