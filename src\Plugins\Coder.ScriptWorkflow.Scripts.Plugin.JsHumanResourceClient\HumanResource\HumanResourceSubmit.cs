﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient.HumanResource
{
    public class HumanResourceSubmit
    {
        /// <summary>
        /// 用户登录账号
        /// </summary>
        public string userName { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTimeOffset humanResourceDate { get; set; }
        /// <summary>
        /// 持续时间
        /// </summary>
        public double humanResourceuration { get; set; }
        /// <summary>
        /// 工单ID
        /// </summary>
        public int orderId { get; set; }
        /// <summary>
        /// 工单编号
        /// </summary>
        public string orderNo { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public HumanResourceType humanResourceType { get; set; }

        /// <summary>
        /// 数据批次号
        /// </summary>
        public string batchNo { get; set; }
        /// <summary>
        /// 时间段描述
        /// </summary>
        public string timeDescription { get; set; }
        /// <summary>
        /// 工单类型
        /// </summary>
        public string orderType { get; set; }


    }
}
