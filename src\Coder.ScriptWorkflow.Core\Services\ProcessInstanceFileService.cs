using System;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.FileSystem;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.Services;

/// <summary>
///     流程实例文件管理服务
/// </summary>
public class ProcessInstanceFileService
{
    private readonly FileAttachmentService _fileAttachmentService;
    private readonly ILogger<ProcessInstanceFileService> _logger;
    private readonly WorkflowPermissionManager _permissionManager;
    private readonly WorkflowManager _workflowManager;

    public ProcessInstanceFileService(
        FileAttachmentService fileAttachmentService,
        WorkflowManager workflowManager,
        WorkflowPermissionManager permissionManager,
        ILogger<ProcessInstanceFileService> logger)
    {
        _fileAttachmentService =
            fileAttachmentService ?? throw new ArgumentNullException(nameof(fileAttachmentService));
        _workflowManager = workflowManager ?? throw new ArgumentNullException(nameof(workflowManager));
        _permissionManager = permissionManager ?? throw new ArgumentNullException(nameof(permissionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    ///     获取文件
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <param name="fileId">文件ID</param>
    /// <param name="user">当前用户</param>
    /// <param name="fileName">指定的文件名</param>
    /// <returns>文件操作结果</returns>
    public async Task<FileOperationResult<FileDownloadInfo>> GetFileAsync(
        int processInstanceId, string fileId, ClaimsPrincipal user, string fileName = null)
    {
        try
        {
            // 验证流程实例
            var validationResult = await ValidateProcessInstanceAccessAsync(processInstanceId, user, false);
            if (!validationResult.Success)
                return FileOperationResult<FileDownloadInfo>.CreateFailure(validationResult.ErrorMessage,
                    validationResult.ErrorCode);

            var processInstance = validationResult.Data;

            // 验证文件是否存在于附件中
            var fileValidationResult = ValidateFileInAttachments(processInstance.Form, fileId);
            if (!fileValidationResult.Success)
                return FileOperationResult<FileDownloadInfo>.CreateFailure(fileValidationResult.ErrorMessage, 404);

            // 获取文件信息
            var fileInfo = await _fileAttachmentService.GetFileInfoAsync(fileId);
            if (fileInfo == null)
                return FileOperationResult<FileDownloadInfo>.CreateFailure($"未找到文件ID为 {fileId} 的文件", 404);

            // 获取文件流
            var stream = _fileAttachmentService.GetFileStream(fileId);
            var contentType = HttpCoderUtility.GetContentTypeByExtension(Path.GetExtension(fileInfo.Name));

            var downloadInfo = new FileDownloadInfo
            {
                Stream = stream,
                ContentType = contentType,
                FileName = fileName ?? fileInfo.Name
            };

            _logger.LogInformation("用户 {User} 下载了流程实例 {ProcessInstanceId} 的文件 {FileId}",
                user.Identity?.Name, processInstanceId, fileId);

            return FileOperationResult<FileDownloadInfo>.CreateSuccess(downloadInfo);
        }
        catch (JsonException)
        {
            return FileOperationResult<FileDownloadInfo>.CreateFailure("工作流实例表单数据不是有效的JSON格式", 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件时发生错误，流程实例ID: {ProcessInstanceId}, 文件ID: {FileId}",
                processInstanceId, fileId);
            return FileOperationResult<FileDownloadInfo>.CreateFailure($"获取文件时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     删除文件
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <param name="fileId">文件ID</param>
    /// <param name="user">当前用户</param>
    /// <returns>文件操作结果</returns>
    public async Task<FileOperationResult> DeleteFileAsync(int processInstanceId, string fileId, ClaimsPrincipal user)
    {
        try
        {
            // 验证流程实例和上传权限
            var validationResult = await ValidateProcessInstanceAccessAsync(processInstanceId, user, true);
            if (!validationResult.Success)
                return FileOperationResult.CreateFailure(validationResult.ErrorMessage, validationResult.ErrorCode);

            var processInstance = validationResult.Data;

            // 验证文件是否存在于附件中
            var fileValidationResult = ValidateFileInAttachments(processInstance.Form, fileId);
            if (!fileValidationResult.Success)
                return FileOperationResult.CreateFailure(fileValidationResult.ErrorMessage, 404);

            // 从表单中移除文件记录
            var formObject = JObject.Parse(processInstance.Form);
            var attachments = _fileAttachmentService.GetOrCreateAttachments(processInstance.Form);

            if (!_fileAttachmentService.RemoveFileFromAttachments(attachments, fileId))
                return FileOperationResult.CreateFailure("从附件列表中删除文件记录失败");

            // 删除物理文件
            var deleteSuccess = _fileAttachmentService.DeleteFile(fileId);
            if (!deleteSuccess)
                _logger.LogWarning("物理文件删除失败，文件ID: {FileId}", fileId);

            // 更新流程实例表单
            processInstance.ReplaceForm(JsonConvert.SerializeObject(formObject));
            _workflowManager.Save(processInstance);

            _logger.LogInformation("用户 {User} 删除了流程实例 {ProcessInstanceId} 的文件 {FileId}",
                user.Identity?.Name, processInstanceId, fileId);

            return FileOperationResult.CreateSuccess(new { success = true, message = "删除成功" }, "删除成功");
        }
        catch (JsonException)
        {
            return FileOperationResult.CreateFailure("工作流实例表单数据不是有效的JSON格式", 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件时发生错误，流程实例ID: {ProcessInstanceId}, 文件ID: {FileId}",
                processInstanceId, fileId);
            return FileOperationResult.CreateFailure($"删除文件时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     上传文件
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <param name="uploadRequest">上传请求</param>
    /// <param name="user">当前用户</param>
    /// <returns>文件操作结果</returns>
    public async Task<FileOperationResult<FileAttachSubmit>> UploadFileAsync(
        int processInstanceId, UploadFileAttachment uploadRequest, ClaimsPrincipal user)
    {
        if (uploadRequest?.File == null)
            return FileOperationResult<FileAttachSubmit>.CreateFailure("上传文件不能为空", 400);

        try
        {
            // 验证流程实例和上传权限
            var validationResult = await ValidateProcessInstanceAccessAsync(processInstanceId, user, true);
            if (!validationResult.Success)
                return FileOperationResult<FileAttachSubmit>.CreateFailure(validationResult.ErrorMessage,
                    validationResult.ErrorCode);

            var processInstance = validationResult.Data;
            var userName = user.Identity?.Name;
            var fileName = FileAttachmentService.ConvertToHalfWidth(uploadRequest.File.FileName);

            // 上传文件到文件系统
            using var stream = uploadRequest.File.OpenReadStream();
            var uploadResult = await _fileAttachmentService.UploadFileAsync(
                stream, fileName, userName, processInstanceId.ToString());

            // 获取上传后的文件信息
            var fileInfo = await _fileAttachmentService.GetFileInfoAsync(uploadResult.Id);

            // 更新流程实例表单
            var formObject = JObject.Parse(processInstance.Form);
            var attachments = _fileAttachmentService.GetOrCreateAttachments(processInstance.Form);

            var attachmentObject = _fileAttachmentService.CreateAttachmentObject(
                uploadResult.Id, fileName, fileInfo.FileSize, uploadRequest.Claims);

            attachments.Add(JToken.FromObject(attachmentObject));
            formObject["attachments"] = attachments;
            processInstance.ReplaceForm(JsonConvert.SerializeObject(formObject));
            _workflowManager.Save(processInstance);

            var result = new FileAttachSubmit
            {
                FileId = uploadResult.Id,
                FileName = fileName
            };

            _logger.LogInformation("用户 {User} 向流程实例 {ProcessInstanceId} 上传了文件 {FileName}",
                userName, processInstanceId, fileName);

            return FileOperationResult<FileAttachSubmit>.CreateSuccess(result, "上传成功");
        }
        catch (JsonException)
        {
            return FileOperationResult<FileAttachSubmit>.CreateFailure("工作流实例表单数据不是有效的JSON格式", 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传文件时发生错误，流程实例ID: {ProcessInstanceId}, 文件名: {FileName}",
                processInstanceId, uploadRequest.File?.FileName);
            return FileOperationResult<FileAttachSubmit>.CreateFailure($"上传文件时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     获取附件列表
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <param name="user">当前用户</param>
    /// <returns>文件操作结果</returns>
    public async Task<FileOperationResult<JArray>> GetAttachmentListAsync(int processInstanceId, ClaimsPrincipal user)
    {
        try
        {
            // 验证流程实例
            var validationResult = await ValidateProcessInstanceAccessAsync(processInstanceId, user, false);
            if (!validationResult.Success)
                return FileOperationResult<JArray>.CreateFailure(validationResult.ErrorMessage,
                    validationResult.ErrorCode);

            var processInstance = validationResult.Data;
            var attachments = _fileAttachmentService.GetOrCreateAttachments(processInstance.Form);

            return FileOperationResult<JArray>.CreateSuccess(attachments);
        }
        catch (JsonException)
        {
            return FileOperationResult<JArray>.CreateFailure("工作流实例表单数据不是有效的JSON格式", 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附件列表时发生错误，流程实例ID: {ProcessInstanceId}", processInstanceId);
            return FileOperationResult<JArray>.CreateFailure($"获取附件列表时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     验证流程实例访问权限
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <param name="user">用户</param>
    /// <param name="requireUploadPermission">是否需要上传权限</param>
    /// <returns>验证结果</returns>
    private async Task<FileOperationResult<ProcessInstance>> ValidateProcessInstanceAccessAsync(
        int processInstanceId, ClaimsPrincipal user, bool requireUploadPermission)
    {
        var processInstance = _workflowManager.GetById(processInstanceId);
        if (processInstance == null)
            return FileOperationResult<ProcessInstance>.CreateFailure($"{processInstanceId}的工作流实例不存在", 404);

        if (string.IsNullOrWhiteSpace(processInstance.Form))
            return FileOperationResult<ProcessInstance>.CreateFailure("工作流实例表单数据为空", 400);

        // 检查读取权限
        if (!await _permissionManager.HasReadPermissionAsync(processInstance, user))
            return FileOperationResult<ProcessInstance>.CreateFailure("你无权访问本工单", 403);

        // 检查上传权限（如果需要）
        if (requireUploadPermission)
        {
            var workActivities = _workflowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
            if (!_permissionManager.CanUploadFiles(workActivities, user))
                return FileOperationResult<ProcessInstance>.CreateFailure("你无权上传或删除此工单的文件", 403);
        }

        return FileOperationResult<ProcessInstance>.CreateSuccess(processInstance);
    }

    /// <summary>
    ///     验证文件是否在附件中
    /// </summary>
    /// <param name="formString">表单JSON字符串</param>
    /// <param name="fileId">文件ID</param>
    /// <returns>验证结果</returns>
    private FileOperationResult ValidateFileInAttachments(string formString, string fileId)
    {
        try
        {
            var attachments = _fileAttachmentService.GetOrCreateAttachments(formString);

            if (!_fileAttachmentService.IsFileInAttachments(attachments, fileId))
                return FileOperationResult.CreateFailure($"文件ID {fileId} 在附件中不存在", 404);

            return FileOperationResult.CreateSuccess();
        }
        catch (JsonException)
        {
            return FileOperationResult.CreateFailure("表单数据不是有效的JSON格式", 400);
        }
    }
}

/// <summary>
///     文件下载信息
/// </summary>
public class FileDownloadInfo
{
    /// <summary>
    ///     文件流
    /// </summary>
    public Stream Stream { get; set; }

    /// <summary>
    ///     内容类型
    /// </summary>
    public string ContentType { get; set; }

    /// <summary>
    ///     文件名
    /// </summary>
    public string FileName { get; set; }
}