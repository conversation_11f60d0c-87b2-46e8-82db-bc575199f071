﻿using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Newtonsoft.Json;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel;

/// <summary>
/// </summary>
public class WorkActivityFormTest
{
    [Fact]
    public void Test()
    {
        var form = new WorkActivityForm();
        var a = new
        {
            WA_comment = "wa comment"
        };
        form.Form = JsonConvert.SerializeObject(a);
        form.FillSelfByJsonForm();

        Assert.Equal(a.WA_comment, form.WorkActivityComment);
    }
}