﻿namespace Coder.ScriptWorkflow.Assigners;

/// <summary>
///     分配结果
/// </summary>
public class AssignResult
{
    /// <summary>
    /// </summary>
    /// <param name="scopeType"></param>
    public AssignResult(AssignScopeType scopeType)
    {
        ScopeType = scopeType;
    }

    /// <summary>
    /// </summary>
    public AssignScopeType ScopeType { get; set; }


    /// <summary>
    ///     执行者集合，是执行者的唯一id或者可以
    /// </summary>
    public Performer[] Performers { get; set; }
}