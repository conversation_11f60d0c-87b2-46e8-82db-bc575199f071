﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
public class NodeStore<T> : INodeStore where T : DbContext
{
    private readonly T _dbContext;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public NodeStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    public void Delete(int id)
    {
        var asign = GetById(id);
        _dbContext.Remove(asign);
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Node GetById(int id)
    {
        return _dbContext.Set<Node>().FirstOrDefault(_ => _.Id == id);
    }

    /// <summary>
    /// </summary>
    /// <typeparam name="T1"></typeparam>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<T1> GetByIdAsync<T1>(int id) where T1 : Node
    {
        return _dbContext.Set<T1>().FirstOrDefaultAsync(_ => _.Id == id);
    }

    /// <summary>
    /// </summary>
    /// <param name="workTask"></param>
    public void AddOrUpdate(Node workTask)
    {
        _dbContext.Update(workTask);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <returns></returns>
    public async Task<IEnumerable<Node>> GetNodesByWorkProcessAsync(WorkProcess workProcess)
    {
        var result = await _dbContext.Set<Node>().TagWith("获取相关的Node")
            .Include(_ => ((WorkTask)_).Assigner)
            .Include(_ => ((WorkTask)_).WorkActivityCompleteScript)
            .Include(_ => ((WorkTask)_).WorkTaskCompleteScript)
            .Include(_ => ((WorkTask)_).WorkTaskStartScript)
            .Include(_ => ((WorkTask)_).Commands)
            .Where(_ => _.WorkProcess == workProcess).ToListAsync();
        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="removeNodes"></param>
    public void Remove(IEnumerable<Node> removeNodes)
    {
        foreach (var node in removeNodes.ToArray())
            switch (node)
            {
                case WorkTask wt:
                    _dbContext.RemoveRange(wt.Commands);
                    wt.Commands.Clear();
                    wt.NextNode = null;
                    _dbContext.Remove(wt.Assigner);

                    _dbContext.Remove(wt.WorkTaskStartScript);
                    _dbContext.Remove(wt.WorkActivityCompleteScript);
                    _dbContext.Remove(wt.WorkTaskCompleteScript);
                    break;
                case BoolScriptDecision bsd:
                    bsd.NextNode = null;
                    bsd.ElseNode = null;
                    bsd.WorkProcess = null;
                    break;
            }

        _dbContext.RemoveRange(removeNodes);
    }

    public Task<Node> GetByNodeAsync(WorkProcess wp, string nodeName)
    {
        return _dbContext.Set<Node>().FirstOrDefaultAsync(_ => _.WorkProcess == wp && _.Name == nodeName);
    }
}