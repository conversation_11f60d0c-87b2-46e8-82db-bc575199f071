﻿declare const jsMember: jsHttpClientClass;

interface AssignUserClass {
    UserName: string;
    Name: string;
}

interface UserDetailClass extends AssignUserClass {
    /**
     *
     */
    Claims: Array<ClaimViewModelClass>;
    /**
     *角色
     */
    Roles: Array<RoleClass>;
    /**
     *右键
     */
    Email: string;
    /**
     * 电话号码
     */

    PhoneNumber: string;

}

interface ClaimViewModelClass {
    Value: string;
    Type: string;
}

interface RoleClass {
    Name: string
}

/**
 * jsHttpClientClass
 */
interface jsHttpClientClass {
    /**
     * 通过Org获取用户信息
     * @param orgName 组织单元名称
   
     */
    GetByOrg(orgName: string): Array<AssignUserClass>;
    /**
     * 
     * 通过Role获取用户列表
     * @param roleName 提交的角色名称
     */
    GetByRole(roleName: string): Array<AssignUserClass>;
    /**
     * 
     * 获取一个用户信息
     * @param jObject
     */
    GetByUserName(userName: string): AssignUserClass;
    /*
     * 获取用户details
     * @param userName 用户登录名称。
     */
    GetUserDetailByUserName(userName: string): UserDetailClass;

    /*
     * 获取Claim
     * @param name cliamn的key值
     * @param value cliam 的 value值
     */
    GetByClaim(name: string, value: string): UserDetailClass;

    /**
     * 通过角色 声明获取用户信息。
     *
     * @param role 角色名称
     * @param claimType 声明的类型
     * @param claimValue 声明的值
     */

    GetUsersByRoleAndClaim(role: string, claimType: string, claimValue: string): Array<AssignUserClass>;

    /**
     * 
     * 根据用户名和角色筛选出符合指定角色的用户列表。
     *  该函数通过用户名找到用户所在部门，然后从该部门中筛选出所有符合指定角色的用户并返回。
     * @param userName 用户名，用于确定用户所在部门
     * @param roleInOrg 指定的角色，用于筛选符合条件的用户。
     */
    GetUserByRoleInCurrentUserOrg(userName:string,roleInOrg: string)

    /**
     * 当前用户。
     */
    CurrentUser(): UserDetailClass

}