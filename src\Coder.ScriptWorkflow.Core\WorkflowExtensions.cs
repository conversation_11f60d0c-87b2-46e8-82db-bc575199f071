﻿using System;
using Coder.FileSystem.Clients;
using Coder.ScriptWorkflow.Cache;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Interceptors;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Permissions.Cache;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.Plugins.Console;
using Coder.ScriptWorkflow.Scripts.Plugins.Logger;
using Coder.ScriptWorkflow.Scripts.Plugins.WorkflowManagers;
using Coder.ScriptWorkflow.Tags;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public static class WorkflowExtensions
{
    /// <summary>
    /// </summary>
    /// <param name="service"></param>
    /// <param name="options"></param>
    /// <returns></returns>
    public static IServiceCollection AddScriptWorkflowServices(this IServiceCollection service,
        Action<WorkflowOptions> options)
    {
        // service.AddHttpClient();

        service //.AddScoped<IWorkflowContext, WorkflowContext>()
            .AddScoped<WorkflowDefinedManager>()
            .AddScoped<WorkflowManager>()
            .AddScoped<WorkflowPermissionManager>()
            .AddSingleton<WorkflowLogManager>()
            .AddTransient<PerformerManager>()
            .AddTransient<DebuggerManager>().AddTransient<StatisticCache>();
        service.AddScoped(sp =>
        {
            var workflowInterceptors = sp.GetServices<IWorkflowInterceptor>();
            var workProcessInterceptors = sp.GetServices<IWorkProcessInterceptor>();
            var logger = sp.GetService<ILogger<InterceptorManager>>();
            var interceptorManager = new InterceptorManager(workflowInterceptors, workProcessInterceptors, logger);
            return interceptorManager;
        });
        service.AddScoped<IWorkflowInterceptor, WorkflowInterceptor>()
            .AddScoped<GlobalScriptContext>()
            .AddTransient<WorkProcessPermissionCacheManager>()
            ;


        //tag 相关
        service.AddSingleton<TagUserTagsCache>()
            .AddTransient<TagManager>();

        var option = new WorkflowOptions(service);
        option.AddJsPlugin<LoggerPlugin>()
            .AddJsPlugin<JsConsolePlugin>()
            .AddJsPlugin<WorkflowManagerPlugin>();
        options(option);
        if (option.FileSystemHost == null) throw new Exception("请配置文件服务器地址。");
        service.AddHttpFileManager(option.FileSystemHost);
        //Coder.
        //service.AddScoped(sp =>
        //{
        //    var icf = sp.GetService<IHttpClientFactory>();
        //    var ica = sp.GetService<IHttpContextAccessor>();
        //    return new HttpSimpleFileManager(option.FileSystemHost, icf, ica);
        //});

        return service;
    }

    /// <summary>
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="options"></param>
    /// <returns></returns>
    public static WorkflowOptions AddJsPlugin<T>(this WorkflowOptions options, Action<WorkflowOptions> config = null)
        where T : class, IPlugin
    {
        options.Services.AddTransient<IPlugin, T>();
        if (config != null) config(options);
        return options;
    }
}