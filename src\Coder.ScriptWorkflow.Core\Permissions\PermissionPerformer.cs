﻿using System;

namespace Coder.ScriptWorkflow.Permissions;

/// <summary>
/// </summary>
public class PermissionPerformer
{
    /// <summary>
    /// </summary>
    public PermissionPerformer()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="wp"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public PermissionPerformer(WorkProcessPermission wp)
    {
        WorkProcessPermission = wp ?? throw new ArgumentNullException(nameof(wp));
    }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public PerformerType Type { get; set; }

    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public virtual WorkProcessPermission WorkProcessPermission { get; set; }
}