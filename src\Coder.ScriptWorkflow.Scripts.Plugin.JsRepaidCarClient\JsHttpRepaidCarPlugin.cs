﻿using coder.car.service.Clients;
using Coder.ScriptWorkflow.Scripts.Plugins;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsRepaidCarClient
{
    public class JsHttpRepaidCarPlugin : IPlugin
    {
        private readonly IRepairCarHttpClient _client;
        public JsHttpRepaidCarPlugin(IRepairCarHttpClient client) { 
        _client = client;
        }
        public string Name { get; set; }= "jsRepaid";

        public void Dispose(object o)
        {
           
        }

        public string GetJsDefined()
        {
            var stream = typeof(JsHttpRepaidCarPlugin).Assembly
           .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.JsRepaidCarClient.defined.d.ts");
            if (stream == null)
                throw new ArgumentNullException("内置文件不存在");

            using var reader = new StreamReader(stream);
            var txt = reader.ReadToEnd();
            return txt;
        }

        public object GetObject(IWorkflowContext context)
        {
           return new RepaidCarHttpClient(_client, context);
        }
    }
}
