﻿using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
/// </summary>
internal static class JsonFormSubmitHelper
{
    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <param name="jsonObject"></param>
    /// <returns></returns>
    internal static string[] ToArray(string key, JObject jsonObject)
    {
        string[] distributions = null;

        if (jsonObject.TryGetValue(key, out var jsValue))
        {
            var ary = jsValue.Value<JArray>();
            if (ary == null)
                return null;
            distributions = new string[ary.Count];
            var aryIndex = 0;
            foreach (var item in ary)
                distributions[aryIndex++] = item.Value<string>();
        }

        return distributions;
    }

    internal static string ToString(string key, JObject jsonObject)
    {
        if (jsonObject.TryGetValue(key, out var jComment))
            return jComment.Value<string>();
        return null;
    }
}