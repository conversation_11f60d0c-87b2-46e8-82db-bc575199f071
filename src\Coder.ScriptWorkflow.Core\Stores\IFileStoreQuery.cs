﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
///     文件查询
/// </summary>
public interface IFileStoreQuery
{
    /// <summary>
    ///     文件列表查询
    /// </summary>
    /// <param name="fileSearcher"></param>
    /// <returns></returns>
    Task<IEnumerable<ProcessInstance.FileAttach>> ListAsync([NotNull] FileSearcher fileSearcher);

    /// <summary>
    /// </summary>
    /// <param name="fileSearcher"></param>
    /// <returns></returns>
    Task<int> CountAsync([NotNull] FileSearcher fileSearcher);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="fsSysFileId">文件系统的Id</param>
    /// <returns></returns>
    Task<ProcessInstance.FileAttach> GetByIdAsync(string fsSysFileId);

}