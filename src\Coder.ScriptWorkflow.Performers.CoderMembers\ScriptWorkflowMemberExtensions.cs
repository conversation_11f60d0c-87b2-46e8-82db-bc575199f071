﻿using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow.Performers.CoderMembers;

/// <summary>
/// </summary>
public static class ScriptWorkflowMemberExtensions
{
    /// <summary>
    ///     采用，请设置好 CoderMember 和 Org相关组件
    /// </summary>
    /// <param name="options"></param>
    /// <returns></returns>
    public static WorkflowOptions AddCoderMember(this WorkflowOptions options)
    {
        options.Services.AddTransient<IPerformerQueryStore, PerformerQueryStore>();
        return options;
    }
}