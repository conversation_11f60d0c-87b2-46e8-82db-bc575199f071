﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers;

/*
 * 重复进入之后，上一次的处理用户Default=true
 */
public class UserAssignerRentrySameWorkTask
{
    private static readonly UserViewModel Creator = new()
    {
        UserName = "creator"
    };

    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options =>
        {
            options.UseSqlite($"Data Source=UserAssignerRentry{dbFile}.db;");
        });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();
        scope.Dispose();
        return sp;
    }

    private static WorkProcess SimpleAuth(IServiceProvider sp, AssignScopeType assignScopeType)
    {
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();

        var workProcess = new WorkProcess("流程-测试")
        {
            Enable = true
        };
        wpStore.AddOrUpdate(workProcess);
        wpStore.SaveChangesAsync().Wait();

        wpStore.SaveChangesAsync().Wait();

        var draf = new WorkTask("草拟", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = @"logger.Info('分配','内容')
return 'user'"
            }
        };
        draf.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "提交"
        });


        var auth1 = new WorkTask("审批1", workProcess)
        {
            Assigner = new UsersAssigner
            {
                Performers = new List<Performer>
                {
                    new()
                    {
                        Key = "role",
                        Type = PerformerType.Role
                    }
                },
                AssignScopeType = assignScopeType
            }
        };
        auth1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意",
            Script = "processInstance.Form.auth1='同意'"
        });
        auth1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "拒绝",
            Script = "processInstance.Form.auth1='拒绝'"
        });


        wtStore.AddOrUpdate(draf);
        wtStore.AddOrUpdate(auth1);

        wtStore.SaveChangesAsync().Wait();
        var auth1Decision = new BoolScriptDecision(new EndNode(workProcess), draf)
        {
            Script = "return processInstance.Form.auth1=='同意'"
        };
        auth1.NextNode = auth1Decision;
        wtStore.AddOrUpdate(auth1);
        wtStore.SaveChangesAsync().Wait();

        var startNode = new StartNode(draf, workProcess);
        draf.NextNode = auth1;


        wtStore.AddOrUpdate(startNode);
        wtStore.AddOrUpdate(draf);
        wtStore.AddOrUpdate(auth1);
        wtStore.SaveChangesAsync().Wait();

        return workProcess;
    }

    /// <summary>
    ///     当再次进入的时候，前一次的user 为Default=true
    /// </summary>
    [InlineData(AssignScopeType.AllOfThem)]
    [InlineData(AssignScopeType.SomeOfThem)]
    [Theory]
    public void TestRetry(AssignScopeType assignScopeType)
    {
        var sp = Sp();
        var wp = SimpleAuth(sp, assignScopeType);
        var workflowManager = sp.GetRequiredService<WorkflowManager>();
        var startResult = workflowManager.Start(new ProcessInstance(wp, "creator"), Creator);

        var submit =
            new WorkflowResolveSubmit
            {
                Command = "提交",
                Comment = "测试",
                Performers = new[] { "role-1" }
            };
        var draftResult = workflowManager.Resolve(startResult.WorkActivities.First().WorkActivityId, submit, Creator);

        var authResult = workflowManager.Resolve(draftResult.WorkActivities.First().WorkActivityId,
            new WorkflowResolveSubmit
            {
                Command = "拒绝",
                Comment = "test"
            }, Creator);

        var workActivityId = authResult.WorkActivities.First().WorkActivityId;

        var nextUserResult = workflowManager.Resolve(workActivityId, new WorkflowResolveSubmit
        {
            Command = "提交"
        }, Creator);
    }
}