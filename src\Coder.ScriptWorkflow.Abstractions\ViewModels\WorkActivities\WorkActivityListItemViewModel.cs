﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Permissions;

namespace Coder.ScriptWorkflow.ViewModels.WorkActivities;

/// <summary>
/// </summary>
public class WorkActivityListItemViewModel : IWorkActivityWithPermission
{
    ///// <summary>
    /////     分配范围。
    ///// </summary>
    //public PerformerType AssignScope { get; set; } = PerformerType.User;

    /// <summary>
    ///     分配的执行者，可能是用户、角色，部门，
    /// </summary>
    public IEnumerable<Performer> AssignPerformer { get; set; }

    /// <summary>
    ///     优先级别
    /// </summary>
    public Priority Priority { get; set; }

    /// <summary>
    ///     工单号
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    ///     工单主题
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     工作任务名称
    /// </summary>
    public string WorkTaskName { get; set; }

    /// <summary>
    ///     工作活动状态
    /// </summary>
    public WorkActivityStatus Status { get; set; }

    /// <summary>
    /// </summary>
    public string DisposeUser { get; set; }

    /// <summary>
    ///     登录名字
    /// </summary>
    public string DisposeUserName { get; set; }

    /// <summary>
    /// </summary>
    public string Command { get; set; }

    /// <summary>
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    /// </summary>
    public DateTime? TimeSpan { get; set; }

    /// <summary>
    ///     获取或设置对应工作流实例
    /// </summary>
    public int ProcessInstanceId { get; set; }

    /// <summary>
    ///     工作流程名称
    /// </summary>
    public string WorkProcessName { get; set; }

    /// <summary>
    /// </summary>
    public string SuggestionComment { get; set; }

    /// <summary>
    ///     创建用户。
    /// </summary>
    public string ProcessInstanceUser { get; set; }

    /// <summary>
    ///     创建用户名称
    /// </summary>
    public string ProcessInstanceUserName { get; set; }

    /// <summary>
    ///     流程创建时间
    /// </summary>
    public DateTimeOffset CreateTime { get; set; }

    /// <summary>
    /// </summary>
    public DateTimeOffset? ProcessInstanceFinishTime { get; set; }

    /// <summary>
    /// </summary>
    public DateTimeOffset? ProcessInstanceCreateTime { get; set; }

    /// <summary>
    /// </summary>
    /// <summary>
    /// </summary>
    public DateTimeOffset? AssignTime { get; set; }

    /// <summary>
    /// </summary>
    public bool WorkTaskCanGiveup { get; set; }

    /// <summary>
    /// </summary>
    public DateTimeOffset? DisposeTime { get; set; }

    /// <inheritdoc />
    public bool WorkTaskCanGiveUp { get; set; }

    /// <inheritdoc />
    public bool CanDisposeWorkActivity { get; set; }

    /// <inheritdoc />
    public bool CanGiveUpWorkActivity { get; set; }

    /// <inheritdoc />
    public bool CanAcceptWorkActivity { get; set; }

    /// <inheritdoc />
    public string GetDisposeUser()
    {
        return DisposeUser;
    }

    /// <inheritdoc />
    public WorkActivityStatus GetWorkActivityStatus()
    {
        return Status;
    }

    /// <inheritdoc />
    public bool GetWorkTaskGiveUpSetting()
    {
        return WorkTaskCanGiveUp;
    }

    /// <inheritdoc />
    public IEnumerable<Performer> GetPerformers()
    {
        return AssignPerformer;
    }
}