﻿using System.Collections.Generic;

namespace Coder.ScriptWorkflow.Assigners;

/// <summary>
///     分配器，用于分配那些用户去WorkActivity 执行
/// </summary>
public abstract class Assigner
{
    /// <summary>
    ///     id
    /// </summary>

    public int Id { get; set; }

    /// <summary>
    ///     派发用户的规则
    /// </summary>
    public virtual AssignScopeType AssignScopeType { get; set; } = AssignScopeType.AllOfThem;


    /// <summary>
    ///     通过派发Assigner
    /// </summary>
    /// <param name="context">上下文</param>
    /// <param name="previous">上一个活动</param>
    /// <returns></returns>
    public abstract AssignResult Assign(IWorkflowContext context, IEnumerable<WorkActivity> previous);
}