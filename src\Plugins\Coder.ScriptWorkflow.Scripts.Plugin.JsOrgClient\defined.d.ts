﻿
interface jsHttpOrgClass {

    GetById(id: number): OrgViewModel;

    GetOrgByParent(parentId: number): Array<OrgViewModel>;

    GetByPath(path: string): OrgViewModel;

    GetParentByPath(parentPath: string): Array<OrgViewModel>;

}

interface OrgViewModel {
    Id: number;
    Name: string;
    Sequence: number;
    OrgType: number;
    ParentOrgId: number;
    OrgAll: string;
    OrgIdPath: string;
}


declare const jsOrg: jsHttpOrgClass;