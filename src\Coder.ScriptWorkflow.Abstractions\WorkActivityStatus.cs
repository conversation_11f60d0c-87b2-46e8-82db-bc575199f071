﻿using System.ComponentModel.DataAnnotations;

namespace Coder.ScriptWorkflow;

/// <summary>
///     工作活动状态
/// </summary>
public enum WorkActivityStatus
{
    /// <summary>
    ///     已经产生了，但是还没派发给个人 value:0
    /// </summary>
    [Display(Name = "等待处理")] UnAssign,

    /// <summary>
    ///     处理中 value:1
    /// </summary>
    [Display(Name = "处理中")] Processing,


    /// <summary>
    ///     成功审理 value 2
    /// </summary>
    [Display(Name = "完成")] Complete,

    /// <summary>
    ///     被管理员关闭 value 3
    /// </summary>
    [Display(Name = "取消")] CloseByAdmin,

    /// <summary>
    ///     挂起 value 4
    /// </summary>
    [Display(Name = "挂起")] Suspend
}