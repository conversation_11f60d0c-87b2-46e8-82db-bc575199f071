﻿using System;
using System.IO;
using Coder.DataInitial;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow.WebApi.Data;

/// <summary>
/// </summary>
public class Seed
{
    /// <summary>
    /// </summary>
    /// <param name="sp"></param>
    /// <param name="dropAndCreate">tru 用于测试，每次调用都会drop table</param>
    public static void Init(IServiceProvider sp, bool dropAndCreate = false)
    {
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<ApplicationDbContext>();
        if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
        if (!dropAndCreate)
        {
            var db = dbContext.GetInfrastructure().GetService<IMigrator>();
            db.Migrate();
            dbContext.Database.EnsureCreated();


            if (!File.Exists("logs/dataInitial.log"))
            {
                var initManager = new DataInitialManager(services, "logs/dataInitial.log");
                initManager.Run();
            }
        }
        else
        {
            // only for unit-tet
            dbContext.Database.EnsureDeleted();
            dbContext.Database.EnsureCreated();

            var initManager = new DataInitialManager(services, "logs/" + Guid.NewGuid().ToString("n") + " .log");
            initManager.Run();
        }

        scope.Dispose();
    }
}