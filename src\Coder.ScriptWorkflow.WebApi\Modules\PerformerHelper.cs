﻿using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Coder.Member;
using Coder.Member.Clients;
using Coder.ScriptWorkflow.Permissions;

namespace Coder.ScriptWorkflow.WebApi.Modules;

/// <summary>
/// </summary>
public static class PerformerHelper
{
   

    /// <summary>
    /// </summary>
    /// <param name="claims"></param>
    /// <returns></returns>
    public static List<PermissionPerformer> GetCurrentPerformerList(IEnumerable<Claim> claims)
    {
        var result = new List<PermissionPerformer>();

        var roles = claims.Where(_ => _.Type == ClaimTypes.Role);

        foreach (var role in roles)
            result.Add(new PermissionPerformer
            {
                Name = role.Value,
                Type = PerformerType.Role
            });

        var orgs = claims.Where(claim => claim.Type == UserDefined.ClaimOrg);

        foreach (var org in orgs)
            result.Add(new PermissionPerformer
            {
                Name = org.Value,
                Type = PerformerType.Org
            });
        return result;
    }

    
}