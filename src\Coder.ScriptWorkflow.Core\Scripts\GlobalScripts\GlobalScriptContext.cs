﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using Coder.ScriptWorkflow.Scripts.Plugins.WorkflowManagers;
using Coder.ScriptWorkflow.Stores;
using Microsoft.Extensions.DependencyInjection;
using NiL.JS.Core;

[assembly: InternalsVisibleTo("Coder.ScriptWorkflow.UnitTest")]

namespace Coder.ScriptWorkflow.Scripts.GlobalScripts;

/// <summary>
///     全局脚本。在工作流脚本中，脚本的上下文的起点都由这个上下文发起。
/// </summary>
public class GlobalScriptContext
{
    private static readonly string[] _allGlobalScripts =
    {
        "dayjs.min.js",
        "workActivity.js"
    };

    private readonly IServiceProvider _sp;


    /// <summary>
    /// </summary>
    /// <param name="sp"></param>
    /// <exception cref="ArgumentNullException">sp为空</exception>
    public GlobalScriptContext(IServiceProvider sp)
    {
        _sp = sp ?? throw new ArgumentNullException(nameof(sp));
        GlobalContext = LoadGlobalScriptFromDb(_sp);
    }

    /// <summary>
    ///     only fo test
    /// </summary>
    /// <param name="items">全局脚本定义</param>
    /// <param name="globalVariables">全局变量</param>
    internal GlobalScriptContext(List<GlobalScriptItem> items, IList<GlobalVariable> globalVariables)
    {
        GlobalContext = new Context();
        foreach (var globalScriptItem in items) GlobalContext.Eval(globalScriptItem.Script);

        MakeGlobalVariable(GlobalContext, globalVariables);

        foreach (var file in _allGlobalScripts)
            GlobalContext.Eval(GetEmbedString(file));
    }

    /// <summary>
    /// </summary>
    public Context GlobalContext { get; }

    private static void MakeGlobalVariable(Context globaContext, IEnumerable<GlobalVariable> globalVariables)
    {
        var globalItems = new List<string>();
        foreach (var globalVariable in globalVariables)
            globalItems.Add($"{globalVariable.Name}:'{globalVariable.Variable}'");

        var properties = string.Join(",\r\n", globalItems);

        var script = @$"const gCfg={{ 
{properties}           
}}";

        globaContext.Eval(script);
    }

    /// <summary>
    /// </summary>
    private static Context LoadGlobalScriptFromDb(IServiceProvider sp)
    {
        var globalContext = new Context();
        foreach (var file in _allGlobalScripts)
            globalContext.Eval(GetEmbedString(file));

        using var scope = sp.CreateScope();
        var scopeSp = scope.ServiceProvider;
        var globalScriptStore = scopeSp.GetRequiredService<IGlobalScriptStore>();
        var items = globalScriptStore.ListAsync().Result;
        foreach (var item in items)
            globalContext.Eval(item.Script);

        var globalVariableStore = scopeSp.GetRequiredService<IGlobalVariableStore>();
        var variables = globalVariableStore.ListAllAsync(GlobalVariableEnv.Server).Result;

        MakeGlobalVariable(globalContext, variables);

        return globalContext;
    }

    /// <summary>
    /// </summary>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public static string GetEmbedString(string fileName)
    {
        var filePath = $"Coder.ScriptWorkflow.TypeScript.d.{fileName}";
        var stream = typeof(WorkflowManagerPlugin).Assembly
            .GetManifestResourceStream(filePath);
        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}