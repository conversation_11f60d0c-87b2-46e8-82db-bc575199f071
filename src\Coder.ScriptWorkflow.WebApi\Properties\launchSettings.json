{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:53394", "sslPort": 0}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "weatherforecast", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Coder.ScriptWorkflow": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger/index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:13200"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/weatherforecast", "publishAllPorts": true}}}