﻿using Coder.Migrator.Clients;
using Coder.Migrator.ViewModels;
using Coder.ScriptWorkflow.Logger;
using Newtonsoft.Json;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts.Plugin.MigratorClient;

/// <summary>
/// </summary>
public class DataClient
{
    private readonly IWorkflowContext _context;
    private readonly IDataClient _dataClient;
    private readonly WorkflowLogManager _loggerManager;

    /// <summary>
    /// </summary>
    /// <param name="dataClient"></param>
    /// <param name="loggerManager"></param>
    /// <param name="context"></param>
    public DataClient(IDataClient dataClient, WorkflowLogManager loggerManager, IWorkflowContext context)
    {
        _dataClient = dataClient;
        _loggerManager = loggerManager;
        _context = context;
    }

    /// <summary>
    /// </summary>
    /// <param name="connectionName"></param>
    /// <param name="table"></param>
    /// <param name="jsObject"></param>
    public void Update(string connectionName, string table, JSObject jsObject)
    {
        _loggerManager.LogInfo(_context, WorkflowLogType.Plugin, $"jsDataPusher:对表{table}，Update 数据，{JsonConvert.SerializeObject(jsObject)}");
        var submit = new UpdateDataSubmit
        {
            Table = table
        };
        var columns = new List<string>();

        foreach (var jToken in jsObject)
        {
            columns.Add(jToken.Key);
            submit.Data.Add(jToken.Value.Value);
        }

        submit.Columns = columns;
        _dataClient.Update(connectionName, submit).Wait();
    }

    /// <summary>
    ///     /
    /// </summary>
    /// <param name="connectionName"></param>
    /// <param name="table"></param>
    /// <param name="jsObject"></param>
    /// <returns></returns>
    public JSValue Insert(string connectionName, string table, JSObject jsObject)
    {
        _loggerManager.LogInfo(_context, WorkflowLogType.Plugin, $"jsDataPusher:对表{table}，Insert 数据，{JsonConvert.SerializeObject(jsObject)}");
        var submit = new UpdateDataSubmit
        {
            Table = table
        };

        var columns = new List<string>();

        foreach (var jToken in jsObject)
        {
            columns.Add(jToken.Key);
            submit.Data.Add(jToken.Value.Value);
        }

        submit.Columns = columns;

        var r = _dataClient.Insert<object>(connectionName, submit).Result.Id;
        return JSValue.Marshal(r);
    }
}