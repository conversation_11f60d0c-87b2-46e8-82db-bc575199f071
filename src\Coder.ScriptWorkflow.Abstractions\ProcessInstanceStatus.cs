﻿using System.ComponentModel.DataAnnotations;

namespace Coder.ScriptWorkflow;

/// <summary>
///     工作流实例状态
/// </summary>
public enum ProcessInstanceStatus
{
    /// <summary>
    ///     草拟中
    /// </summary>
    [Display(Name = "草拟中")] Created,

    /// <summary>
    ///     审批中
    /// </summary>
    [Display(Name = "审批中")] Processing,

    /// <summary>
    ///     已完成
    /// </summary>
    [Display(Name = "已完成")] Completed,

    /// <summary>
    ///     取消
    /// </summary>
    [Display(Name = "取消")] Cancel,

    /// <summary>
    ///     挂起
    /// </summary>
    [Display(Name = "挂起")] Suspend
}