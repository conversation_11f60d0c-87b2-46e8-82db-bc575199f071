﻿using Coder.ScriptWorkflow.Assigners;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.Assigns;

internal class UsersAssignerMapping : IEntityTypeConfiguration<UsersAssigner>
{
    public void Configure(EntityTypeBuilder<UsersAssigner> builder)
    {
        builder.HasBaseType<Assigner>();

        builder.Property(_ => _.Performers)
            .HasJsonValueConversion()
            .HasMaxLength(1000);
    }
}