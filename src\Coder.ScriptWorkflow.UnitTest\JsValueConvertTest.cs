﻿using System.Collections.Generic;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NiL.JS.Core;
using Xunit;
using static Xunit.Assert;

namespace Coder.ScriptWorkflow.UnitTest;

/// <summary>
/// </summary>
public class JsValueConvertTest
{
    private static readonly UserViewModel Creator = new()
    {
        UserName = "creator"
    };

    /// <summary>
    /// </summary>
    [Fact]
    public void Test()
    {
        var ct = new Context();
        ct.Eval("var f=JSON.stringify({code:12,ff:'int'})");
        var jsValue = ct.GetVariable("f");
        var f = jsValue.Value.ToString();
        var re = JsValueConvert.ToUrlForm(f);

        var expect = "code=12&ff=int";
        Equal(expect, re.ReadAsStringAsync().Result);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void ArrayString()
    {
        var ct = new Context();
        ct.Eval("var f='user'");
        var jsValue = ct.GetVariable("f");
        var ary = JsValueConvert.ToStringArray(jsValue);
        Equal("user", ary[0]);

        ct.Eval("var f=['user','admin']");
        jsValue = ct.GetVariable("f");
        ary = JsValueConvert.ToStringArray(jsValue);
        Equal("user", ary[0]);
        Equal("admin", ary[1]);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void ErrorTest()
    {
        var obj = new
        {
            code = "ok",
            f = new
            {
                a1 = 12,
                a2 = true
            }
        };
        var objStr = JsonConvert.SerializeObject(obj);
        var ct = new Context();
        ct.Eval($"var f=JSON.stringify({objStr})");
        var jsValue = ct.GetVariable("f");
        var f = jsValue.Value.ToString();

        var ex = Throws<JsonConvertException>(() =>
        {
            var re = JsValueConvert.ToUrlForm(f);
        });

        Equal("jsObject只能是json，并且只有一层.如{code:1,boolean:true}", ex.Message);
    }

    /// <summary>
    /// </summary>
    /// <param name="script"></param>
    /// <param name="scope"></param>
    /// <param name="performers"></param>
    [Theory]
    [InlineData("var f= 'user1'", PerformerType.User, new[] { "user1" })]
    [InlineData("var f= ['user1','user2']", PerformerType.User, new[] { "user1", "user2" })]
    [InlineData("var f= {type:0,key:'user1',name:'管理员'}", PerformerType.User, new[] { "user1" })]
    [InlineData("var f= [{type:0,key:'user1',name:'user1-name'},{type:0,key:'user2',name:'user2-name'}]",
        PerformerType.User, new[] { "user1", "user2" })]
    [InlineData("var f= {type:1,key:'role1'}", PerformerType.Role, new[] { "role1" })]
    [InlineData("var f= [{type:1,key:'role1'},{type:1,key:'role2'}]", PerformerType.Role, new[] { "role1", "role2" })]
    [InlineData("var f= {type:2,key:'dept1'}", PerformerType.Org, new[] { "dept1" })]
    [InlineData("var f= [{type:2,key:'dept1'},{type:2,key:'dept2'}]", PerformerType.Org, new[] { "dept1", "dept2" })]
    public void TestAssignResult(string script, PerformerType scope, string[] performers)
    {
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();

        var global = new GlobalScriptContext(new List<GlobalScriptItem>(),new List<GlobalVariable>());
        var processInstance = new ProcessInstance(new WorkProcess("kk"), "user");
        var task = new WorkTask[0];
        var debugger = new DebuggerManager(new EmptyDebuggerPusher());
        var loggerManager = new WorkflowLogManager(sp);

        var globalContext = new WorkflowContext(global, processInstance, task, new List<IPlugin>(),
            new List<ScriptTagInfo>(), loggerManager, debugger, Creator);
        var ct = new Context(globalContext.JavascriptGlobalContext);
        ct.Eval(script);
        var jsValue = ct.GetVariable("f");
        var assignResult = JsValueConvert.ToAssignScope(jsValue, new ScriptAssigner());

        for (var i = 0; i < performers.Length; i++)
        {
            var expect = performers[i];
            var actual = assignResult.Performers[i];
            Equal(expect, actual.Key);
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="script"></param>
    [Theory]
    [InlineData("f = ''")]
    [InlineData("f = []")]
    public void ErrorOnAssignResult(string script)
    {
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();


        var global = new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>());
        var processInstance = new ProcessInstance(new WorkProcess("kk"), "user");
        var task = new WorkTask[0];
        var debugger = new DebuggerManager(new EmptyDebuggerPusher());
        var loggerManager = new WorkflowLogManager(sp);

        var globalContext = new WorkflowContext(global, processInstance, task, new List<IPlugin>(),
            new List<ScriptTagInfo>(), loggerManager, debugger, Creator);

        var ct = new Context(globalContext.JavascriptGlobalContext);
        ct.Eval(script);
        var jsValue = ct.GetVariable("f");
        var assignResult = JsValueConvert.ToAssignScope(jsValue, new ScriptAssigner());

        Empty(assignResult.Performers);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void ToDynamicObjectTest()
    {
        var script = @"class LockSubstanceSubmit {
constructor(){
this.Barcodes=[];
this.LockInfo=null;
}
}
 
var a=new LockSubstanceSubmit();
a.Barcodes=['aa','bb','cc']

";
        var ct = new Context();
        ct.Eval(script);
        var jsValue = ct.GetVariable("a");

        var result = jsValue.ToDynamicObject();
        var a = JsonConvert.SerializeObject(result);
        var obj = JsonConvert.DeserializeObject<LockSubstanceSubmit>(a);


        Equal("aa", obj.Barcodes[0]);
        Equal("bb", obj.Barcodes[1]);
        Equal("cc", obj.Barcodes[2]);
    }

    /// <summary>
    /// </summary>
    public class LockSubstanceSubmit
    {
        /// <summary>
        /// </summary>
        public string[] Barcodes { get; set; }
    }
}