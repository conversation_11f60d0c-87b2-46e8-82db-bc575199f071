﻿using System;
using Coder.ScriptWorkflow.Interceptors;
using oa.workbench.service.Clients;
using oa.workbench.service.ViewModels;

namespace Coder.ScriptWorkflow.CoderWorkBench.Interceptor;

/// <summary>
/// </summary>
internal class WorkflowInterceptor : IWorkflowInterceptor
{
    private readonly IWorkbenchClient _client;
    private readonly IWaitingReadClient _waitingReadClient;


    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    public WorkflowInterceptor(IWorkbenchClient client, IWaitingReadClient waitingReadClient)
    {
        _client = client;
        _waitingReadClient = waitingReadClient;
    }

    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="processInstance"></param>
    /// <param name="provider"></param>
    public void OnProcessChanged(IWorkflowContext topContext, ProcessInstance processInstance, IServiceProvider provider)
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="workActivity"></param>
    /// <param name="provider"></param>
    public void OnWorkActivityChanged(IWorkflowContext topContext, WorkActivity workActivity, IServiceProvider provider)
    {
        switch (workActivity.Status)
        {
            case WorkActivityStatus.Processing:
            case WorkActivityStatus.UnAssign:
                _client.WorkbenchStart(new WorkbenchSubmit
                {
                    CreateUser = topContext.CurrentWorkActivity?.DisposeUserName ?? workActivity.ProcessInstance.Creator,
                    Handler = workActivity.DisposeUser,
                    Name = "【" + workActivity.ProcessInstance.WorkProcess.Name + "】" + workActivity.ProcessInstance.Subject,
                    No = workActivity.ProcessInstance.Number,
                    WorkProcessId = workActivity.ProcessInstance.Id,
                    WorkActivityId = workActivity.Id,
                    WorkActivityName = workActivity.WorkTask.Name
                }).Wait();
                break;
            case WorkActivityStatus.CloseByAdmin:
                _client.WorkbenchDeleteByWorkActivityID(workActivity.Id).Wait();
                break;
            case WorkActivityStatus.Complete:
                _client.WorkbenchFinish(new WorkbenchSubmit
                {
                    No = workActivity.ProcessInstance.Number,
                    WorkActivityId = workActivity.Id,
                    FinishTime = workActivity.DisposeTime
                }).Wait();
                break;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivity"></param>
    /// <param name="userName"></param>
    public void NotifyDistributionUser(ProcessInstance processInstance, string userName)
    {
        _waitingReadClient.WaitingReadStart(new WaitingReadSubmit
        {
            CreateUser = processInstance.Creator,
            Handler = userName,
            Name = "【" + processInstance.WorkProcess.Name + "】" + processInstance.Subject,
            No = processInstance.Number,
            WorkProcessId = processInstance.Id
        });
    }
}