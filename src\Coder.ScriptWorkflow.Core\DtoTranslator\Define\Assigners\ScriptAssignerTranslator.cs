﻿using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.Assigners;

/// <summary>
/// </summary>
public class ScriptAssignerTranslator : AssignerTranslator
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected override Assigner CreateEntity()
    {
        return new ScriptAssigner();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected override AssignSubmit CreateViewModel()
    {
        return new ScriptAssignerSubmit();
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="traget"></param>
    protected override void FillToEntity(AssignSubmit src, Assigner traget)
    {
        var sa = (ScriptAssigner)traget;
        var sas = (ScriptAssignerSubmit)src;
        sa.Script = sas.Script;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="traget"></param>
    protected override void FillToViewModel(Assigner src, AssignSubmit traget)
    {
        var scriptAssign = (ScriptAssignerSubmit)traget;
        var scriptSrc = (ScriptAssigner)src;
        scriptAssign.Script = scriptSrc.Script;
    }

    /// <summary>
    /// </summary>
    /// <param name="assigner"></param>
    /// <returns></returns>
    protected override bool TypeIsMatch(Assigner assigner)
    {
        return assigner is ScriptAssigner;
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public override bool Validate(AssignSubmit submit, out string errorMessage)
    {
        var sas = (ScriptAssignerSubmit)submit;
        errorMessage = null;
        if (string.IsNullOrEmpty(sas.Script))
        {
            errorMessage = "用户派发脚本不能为空.";
            return false;
        }

        return true;
    }
}