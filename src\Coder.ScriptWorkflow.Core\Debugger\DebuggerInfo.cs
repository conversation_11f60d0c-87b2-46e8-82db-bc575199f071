﻿using Coder.ScriptWorkflow.Nodes;

namespace Coder.ScriptWorkflow.Debugger;

/// <summary>
/// </summary>
public enum DebuggerType
{
    Info,
    Warn,
    Error
}

/// <summary>
/// </summary>
public class DebuggerInfo
{
    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    public DebuggerInfo(string message)
    {
        Message = message;
    }

    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <param name="message"></param>
    public DebuggerInfo(Node node, string message) : this(message)
    {
        NodeName = node?.Name;
    }

    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <param name="message"></param>
    /// <param name="eventName"></param>
    public DebuggerInfo(Node node, string message, string eventName)
        : this(node, message)
    {
        EventName = eventName;
    }

    /// <summary>
    /// </summary>
    public DebuggerType Type { get; set; }

    /// <summary>
    /// </summary>
    public string NodeName { get; set; }

    /// <summary>
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// </summary>
    public string EventName { get; set; }

    /// <summary>
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// </summary>
    public string Codeline { get; set; }

    /// <summary>
    /// </summary>
    public string Plugin { get; set; }

    /// <summary>
    /// </summary>
    public string Form { get; set; }
}