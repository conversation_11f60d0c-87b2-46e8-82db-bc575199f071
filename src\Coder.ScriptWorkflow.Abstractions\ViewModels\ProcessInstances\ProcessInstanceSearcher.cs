﻿using System;

namespace Coder.ScriptWorkflow.ViewModels.ProcessInstances;

/// <summary>
///     工作实例查询
/// </summary>
public class ProcessInstanceSearcher : Pager
{
    private DateTime? _createTimeEnd;
    private DateTime? _createTimeStart;
    private ProcessInstanceStatus[] _status;

    /// <summary>
    ///     流程定义名称，模拟查询
    /// </summary>
    public string[] Name { get; set; }

    /// <summary>
    ///     是否删除
    /// </summary>
    public bool? IsDelete { get; set; }

    /// <summary>
    ///     是否处于调试
    /// </summary>
    public bool? IsDebug { get; set; }

    /// <summary>
    ///     工单编码
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    ///     查询标签。
    /// </summary>
    public string[] Tags { get; set; }


    /// <summary>
    ///     是否我管理下的工单。
    /// </summary>
    public bool OwnManage { get; set; }

    /// <summary>
    ///     我参与过的
    /// </summary>
    public bool OwnDisposed { get; set; } = false;

    public ProcessInstanceStatus[] Status
    {
        get
        {
            if (_status == null || _status.Length == 0)
                _status = new[]
                {
                    ProcessInstanceStatus.Processing,
                    ProcessInstanceStatus.Cancel,
                    ProcessInstanceStatus.Completed,
                    ProcessInstanceStatus.Created,
                    ProcessInstanceStatus.Suspend
                };
            return _status;
        }
        set => _status = value;
    }

    public string Subject { get; set; }
    public string Creator { get; set; }

    public DateTime? CreateTimeStart
    {
        get => _createTimeStart;
        set => _createTimeStart = DateHelp.GetDateS(value);
    }

    public DateTime? CreateTimeEnd
    {
        get => _createTimeEnd;
        set => _createTimeEnd = DateHelp.GetDateE(value);
    }

    public int? WorkProcessId { get; set; }
}