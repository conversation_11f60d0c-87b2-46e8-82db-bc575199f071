﻿
/**
 * mysql 执行对象
 */
declare var mysql: mysqlClass;

/**
 * mysql 执行
 */
declare class mysqlClass {
    /**
     * 执行sql，并且返回对象列表
     * @param conn 数据库链接串
     * @param sql  执行的sql
     * @param param 参数对象。
     */
    ExecuteQuery(conn, sql, param): Array<any>;

    /**
     * 执行 inser /update 等语句返回影响行数
     * @param conn
     * @param sql
     * @param param
     */
    ExecuteNon(conn, sql, param): number;

    /**
     * 执行一条sql并且返回一个值。
     * @param conn
     * @param sql
     * @param param
     */
    ExecuteScalar(conn, sql, param): any;
}