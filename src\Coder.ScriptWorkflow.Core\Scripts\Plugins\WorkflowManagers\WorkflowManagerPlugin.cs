﻿using System;
using System.IO;

namespace Coder.ScriptWorkflow.Scripts.Plugins.WorkflowManagers;

/// <summary>
/// </summary>
public class WorkflowManagerPlugin : IPlugin
{
    /// <summary>
    /// </summary>
    public const string PluginName = "workflowManager";

    private readonly WorkflowManager _workflowManager;

    /// <summary>
    /// </summary>
    /// <param name="workflowManager"></param>
    public WorkflowManagerPlugin(WorkflowManager workflowManager)
    {
        _workflowManager = workflowManager ?? throw new ArgumentNullException(nameof(workflowManager));
    }

    /// <summary>
    /// </summary>
    public string Name { get; } = PluginName;

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new ScriptWorkflowManager(_workflowManager, context);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        const string filePath = "Coder.ScriptWorkflow.TypeScript.d.workflowManagerPlugin.d.ts";
        var stream = typeof(WorkflowManagerPlugin).Assembly
            .GetManifestResourceStream(filePath);
        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}