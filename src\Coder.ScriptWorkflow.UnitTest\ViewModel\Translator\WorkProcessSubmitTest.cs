﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Scripts;
using Microsoft.Extensions.Logging;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel.Translator;

public class WorkProcessSubmitTest
{
    [Fact]
    public void SetConstructions()
    {
        var wp = new WorkProcess("name");
        wp.Id = 1;
        wp.Enable = true;
        wp.OnComplete = new WorkProcessScript
        {
            Script = "onComplete"
        };
        wp.FormDesign = "formDesign";
        wp.FormTypeScriptDefined = "FormTypeScriptDefined";
        wp.Prefix = "b";
        wp.LogLevel = LogLevel.None;
        wp.Version = 10;


        var target = wp.ToSubmitViewModel(new List<Nodes.Node>());

        Assert.Equal(wp.Id, target.Id);
        Assert.Equal(wp.OnComplete.Script, target.OnCompleteScript);
        Assert.Equal(wp.Name, target.Name);
        Assert.Equal(wp.FormDesign, target.FormDesign);
        Assert.Equal(wp.Prefix, target.Prefix);
        Assert.Equal(wp.FormTypeScriptDefined, target.FormTypeScriptDefined);
        Assert.Equal(wp.Version, target.Version);
    }
}