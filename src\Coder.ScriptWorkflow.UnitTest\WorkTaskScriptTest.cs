﻿using System;
using System.Collections.Generic;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest;

public class WorkTaskScriptTest
{
    private static UserViewModel Creator = new UserViewModel()
    {
        UserName = "creator",
    };
    [Theory]
    [InlineData("return true", true)]
    [InlineData("return false", false)]
    [InlineData("", true)]
    public void Test(string script, bool result)
    {
        var pi = new ProcessInstance(new WorkProcess("wpName")
        {
            Configurations = new List<WorkflowConfiguration>
            {
                new()
                {
                    Name = "connection",
                    Value = "connection-test"
                }
            }
        }, "user");
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var currentActivity = new WorkActivity(pi, new WorkTask("任务1", new WorkProcess("wpName")), Priority.Normal);
        var workflowContext = new WorkflowContext(
            new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            Array.Empty<Nodes.Node>(),
            currentActivity,Array.Empty<WorkActivity>(),Array.Empty<IPlugin>(),
            new List<ScriptTagInfo>(), new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()), Creator);
      
        
        var onComplete = new WorkActivityScript();
        onComplete.Script = script;


        var target = onComplete
            .Invoke(workflowContext);

        Assert.Equal(result, target);
    }
}