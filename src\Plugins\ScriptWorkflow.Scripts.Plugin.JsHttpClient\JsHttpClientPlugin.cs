﻿using System;
using System.IO;
using System.Net.Http;
using Coder.ScriptWorkflow.Scripts.Plugins;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient;
/// <summary>
/// 
/// </summary>
public class JsHttpClientPlugin : IPlugin
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IServiceProvider _sp;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="httpClientFactory"></param>
    public JsHttpClientPlugin(IHttpClientFactory httpClientFactory, IServiceProvider sp)
    {
        _httpClientFactory = httpClientFactory;
        _sp = sp;
    }
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = "jsHttpClient";
    /// <summary>
    /// 
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new JsHttpClient(_httpClientFactory, _sp);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        var stream = typeof(JsHttpClientPlugin).Assembly
            .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient.jsHttpClient.d.ts");
        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}