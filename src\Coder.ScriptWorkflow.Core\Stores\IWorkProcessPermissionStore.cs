﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.ViewModels.Permissions;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IWorkProcessPermissionStore
{
    IQueryable<WorkProcessPermission> Permissions { get; }

    /// <summary>
    ///     删除分配
    /// </summary>
    /// <param name="id"></param>
    void Delete(int id);

    /// <summary>
    ///     通过id获取分配
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkProcessPermission GetById(int id);

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate(WorkProcessPermission workTask);

    /// <summary>
    ///     保存保存
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    ///     列出个人相关工作流程。
    /// </summary>
    /// <param name="search"></param>
    /// <param name="performers"></param>
    /// <param name="currentName"></param>
    /// <returns></returns>
    IEnumerable<WorkProcess> List(WorkProcessPermissionSearch search, PermissionPerformer[] performers, string currentName);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    Task<int> Count(WorkProcessPermissionSearch searcher, PermissionPerformer[] performers, string currentName);

    /// <summary>
    /// </summary>
    /// <param name="workflowName"></param>
    /// <returns></returns>
    Task<WorkProcessPermission> GetByProcessNameAsync(string workflowName);

    void Remove(IEnumerable<PermissionPerformer> removePerformers);

    Task<IEnumerable<WorkProcessPermission>> GetByNamesAsync(IEnumerable<string> workProcessName);
    IEnumerable<WorkProcessPermission> AllPermissions();
}