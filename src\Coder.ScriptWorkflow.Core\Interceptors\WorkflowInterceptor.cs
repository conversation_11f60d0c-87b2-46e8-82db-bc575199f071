﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Interceptors;

/// <summary>
/// </summary>
internal class WorkflowInterceptor : IWorkflowInterceptor
{


    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="processInstance"></param>
    /// <param name="provider"></param>
    public void OnProcessChanged(IWorkflowContext topContext, ProcessInstance processInstance, IServiceProvider provider)
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="workActivity"></param>
    /// <param name="provider"></param>
    public void OnWorkActivityChanged(IWorkflowContext topContext, WorkActivity workActivity, IServiceProvider provider)
    {
    }

    public Task NotifyDistributionUserAsync(IWorkflowContext workflowContext, IEnumerable<string> userName)
    {
        return Task.CompletedTask;
    }
}