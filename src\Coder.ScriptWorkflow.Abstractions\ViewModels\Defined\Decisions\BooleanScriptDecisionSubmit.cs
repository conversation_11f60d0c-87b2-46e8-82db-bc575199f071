﻿

// ReSharper disable once CheckNamespace

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// 多条件匹配
/// </summary>
public class ConditionSettingSubmit
{
    /// <summary>
    ///     节点
    /// </summary>
    public virtual string NodeName { get; set; }

    /// <summary>
    ///     脚本
    /// </summary>
    public string matchValue { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     id
    /// </summary>
    public int Id { get; set; }
}

/// <summary>
/// </summary>
public class BooleanScriptDecisionSubmit : DecisionSubmit
{
    /// <summary>
    /// </summary>
    public BooleanScriptDecisionSubmit()
    {
    }


    /// <summary>
    /// </summary>
    public string Script { get; set; }

    /// <summary>
    ///     任务名称
    /// </summary>
    public string ElseNodeName { get; set; }

    /// <summary>
    ///     拒绝说明
    /// </summary>
    public string ElseDescription { get; set; }


    /// <summary>
    ///     验证判断器
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public override bool Validate(out string message)
    {
        if (!base.Validate(out message))
            return false;
        var decision = $"判断器{Name}";
        if (!base.Validate(out message))
        {
            message = decision + message;
            return false;
        }

        message = null;
        if (string.IsNullOrEmpty(Script))
        {
            message = $"[{decision}]脚本必须填写";
            return false;
        }

        if (ElseNodeName == null)
        {
            message = $"[{decision}]否决节点必须填写。";
            return false;
        }


        return true;
    }
}