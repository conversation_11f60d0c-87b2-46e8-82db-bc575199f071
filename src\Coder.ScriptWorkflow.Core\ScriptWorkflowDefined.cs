﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow
{
    /// <summary>
    /// 默认名字，用于规范使用一些工作累内置名称而制作
    /// </summary>
    public static class ScriptWorkflowDefined
    {
        /// <summary>
        /// 系统最大管理员，不单止是工作流管理员，而且是系统级别管理员。
        /// </summary>
        public static string SystemAdmin = "admin";
        /// <summary>
        /// 工作流系统管理员角色，
        /// </summary>
        public static string WorkflowManager = "workflowManager";
    }
}
