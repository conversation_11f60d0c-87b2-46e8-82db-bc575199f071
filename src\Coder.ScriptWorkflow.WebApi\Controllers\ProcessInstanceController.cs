using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.FileSystem.Clients;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
/// 工作流实例控制器
/// 负责处理工作流实例的各种操作，包括查询、删除等核心功能
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize]
public class ProcessInstanceController : Controller
{

    private readonly IProcessInstanceStore _processInstanceStore;
    private readonly WorkflowPermissionManager _workflowPermissionManager;
    private readonly ILogger<ProcessInstanceController> _logger;
    private readonly IHttpFileManager _fileManager;
    private readonly WorkflowManager _workflowManager;
    private readonly ApplicationDbContext _context;
    public ProcessInstanceController(IProcessInstanceStore processInstanceStore, ILogger<ProcessInstanceController> logger, WorkflowPermissionManager workflowPermissionManager,
        IHttpFileManager fileManager, WorkflowManager workflowManager, ApplicationDbContext context)
    {
        _processInstanceStore = processInstanceStore;
        _logger = logger;
        _workflowPermissionManager = workflowPermissionManager;
        _fileManager = fileManager;
        _workflowManager = workflowManager;
        _context = context;
    }

    [HttpGet("list-process-instance-numbers")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<ProcessInstanceViewModel>>))]
    public async Task<IActionResult> ListByNumbers([FromQuery] List<string> numbers, [FromServices] IProcessInstanceStore processInstanceStore)
    {
        try
        {
            // 1. 先同步获取数据并强制执行查询
            var processInstances = (await _processInstanceStore.ListByNumbersAsync(numbers)).ToList();

            // 2. 创建一个列表存储转换后的视图模型
            var viewModels = new List<ProcessInstanceViewModel>();

            // 3. 逐个处理实例，避免并发问题
            foreach (var instance in processInstances)
            {
                var viewModel = await instance.ToViewModelAsync(_processInstanceStore);
                viewModels.Add(viewModel);
            }

            return Ok(viewModels.ToSuccess());
        }
        catch (Exception ex)
        {
            // 适当的错误处理
            return BadRequest(new SwfResult<object>
            {
                Code = 0,
                Message = "获取工作流实例列表失败",
                Data = ex.Message
            });
        }
    }
    /// <summary>
    /// 根据实例ID列表获取工作流实例
    /// </summary>
    /// <param name="ids">实例ID列表</param>
    /// <param name="processInstanceStore">工作流实例存储服务（依赖注入）</param>
    /// <returns>工作流实例视图模型列表</returns>
    [HttpGet("list-process-instance-ids")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<ProcessInstanceViewModel>>))]
    public async Task<IActionResult> ListByIds([FromQuery] List<int> ids,
        [FromServices] IProcessInstanceStore processInstanceStore)
    {
        try
        {
            // 1. 根据ID列表异步获取工作流实例数据
            var processInstances = (await _processInstanceStore.ListByIdsAsync(ids)).ToList();

            // 2. 创建视图模型列表用于存储转换后的数据
            var viewModels = new List<ProcessInstanceViewModel>();

            // 3. 逐个处理实例转换为视图模型，避免并发问题
            foreach (var instance in processInstances)
            {
                var viewModel = await instance.ToViewModelAsync(_processInstanceStore);
                viewModels.Add(viewModel);
            }

            return Ok(viewModels.ToSuccess());
        }
        catch (Exception ex)
        {
            // 统一异常处理，记录错误并返回友好的错误信息
            return BadRequest(new SwfResult<object>
            {
                Code = 0,
                Message = "获取工作流实例列表失败",
                Data = ex.Message
            });
        }
    }

    /// <summary>
    /// 根据工单号列表统计工作流实例数量
    /// </summary>
    /// <param name="numbers">工单号列表</param>
    /// <returns>符合条件的工作流实例数量</returns>
    [HttpGet("count-process-instance-numbers")]
    [ProducesDefaultResponseType(typeof(SwfResult<int>))]
    public async Task<IActionResult> Count([FromQuery] List<string> numbers)
    {
        var result = await _processInstanceStore.CountByNumbersAsync(numbers);
        return Ok(result.ToSuccess());
    }

    /// <summary>
    /// 根据工单号获取单个工作流实例
    /// </summary>
    /// <param name="number">工单号</param>
    /// <returns>工作流实例详细信息</returns>
    [HttpGet("get-by-code/{number}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceViewModel>))]
    public IActionResult GetByNumber([FromRoute] string number)
    {
        // 根据工单号查找工作流实例
        var result = _processInstanceStore.Get(number);

        if (result == null)
        {
            _logger.LogError("找不到Number=" + number + "的工作流程实列。");
            return Ok();
        }

        // 检查用户是否有读取权限
        if (!_workflowPermissionManager.HasReadPermissionAsync(result, User).Result)
            return Ok(new ProcessInstanceViewModel
            {
                Success = true,
                Message = "你无权访问本工单。"
            }.ToSuccess());

        // 转换为视图模型并设置权限信息
        var model = result.ToViewModel(_processInstanceStore);
        _workflowPermissionManager.SetProcessInstance(model, User);

        return Ok(model.ToSuccess());
    }

    /// <summary>
    /// 根据实例ID获取单个工作流实例
    /// </summary>
    /// <param name="id">工作流实例ID</param>
    /// <returns>工作流实例详细信息</returns>
    [HttpGet("{id:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceViewModel>))]
    public IActionResult Get([FromRoute] int id)
    {
        // 根据ID查找工作流实例
        var result = _processInstanceStore.GetById(id);

        if (result == null)
        {
            _logger.LogError("找不到id=" + id + "的工作流程实列。");
            return Ok();
        }

        // 检查用户是否有读取权限
        if (!_workflowPermissionManager.HasReadPermissionAsync(result, User).Result)
            return Ok(new ProcessInstanceViewModel
            {
                Success = true,
                Message = "你无权访问本工单。"
            }.ToSuccess());

        // 转换为视图模型并设置权限信息
        var model = result.ToViewModel(_processInstanceStore);
        _workflowPermissionManager.SetProcessInstance(model, User);

        return Ok(model.ToSuccess());
    }

    /// <summary>
    /// 删除指定的工作流实例
    /// 执行完整的删除流程，包括取消流程、删除工作活动、清理标签和文件等
    /// </summary>
    /// <param name="id">工作流实例ID</param>
    /// <param name="fileManager">文件管理器（依赖注入）</param>
    /// <returns>删除操作结果</returns>
    [HttpDelete("{id:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<object>))]
    public async Task<IActionResult> Delete([FromRoute] int id, [FromServices] IHttpFileManager fileManager)
    {
        try
        {
            // 1. 查找要删除的工作流实例
            var processInstance = _processInstanceStore.GetById(id);
            if (processInstance == null)
            {
                _logger.LogError("找不到id=" + id + "的工作流程实例。");
                return NotFound(new SwfResult<object>
                {
                    Code = 0,
                    Message = "找不到指定的工作流实例",
                    Data = null
                });
            }

            // 2. 检查用户是否有删除权限
            var viewModel = await processInstance.ToViewModelAsync(_processInstanceStore);
            _workflowPermissionManager.SetProcessInstance(viewModel, User);

            if (!viewModel.CanDeleteProcessInstance)
                return Ok(new SwfResult<object>
                {
                    Code = 0,
                    Message = "你无权删除本流程或不符合删除条件。",
                    Data = null
                });

            // 3. 执行删除操作（包括取消流程、删除工作活动、清理标签和文件）
            await _workflowManager.DeleteAsync(id, fileManager, User.ToUserViewModel());

            // 4. 记录删除操作日志
            _logger.LogInformation("用户 {User} 成功删除工作流实例 {ProcessInstanceId}", User.Identity?.Name, id);

            return Ok(new SwfResult<object>
            {
                Code = 1,
                Message = "成功删除工作流实例",
                Data = new { id }
            }.ToSuccess());
        }
        catch (Exception ex)
        {
            // 记录错误日志并返回错误信息
            _logger.LogError(ex, "删除工作流实例 {ProcessInstanceId} 时发生错误", id);
            return BadRequest(new SwfResult<object>
            {
                Code = 0,
                Message = "删除工作流实例失败: " + ex.Message,
                Data = null
            });
        }
    }

    /// <summary>
    /// 清空所有工作流实例
    /// 管理员专用功能，用于批量删除系统中的所有工作流实例
    /// 注意：此操作不可逆，请谨慎使用
    /// </summary>
    /// <param name="fileManager">文件管理器（依赖注入）</param>
    /// <param name="id"></param>
    /// <returns>批量删除操作结果</returns>
    [HttpDelete("clear-all/{id}")]
    [Authorize(Roles = "admin")] // 仅管理员可以执行此操作
    [ProducesDefaultResponseType(typeof(SwfResult<object>))]
    public async Task<IActionResult> ClearAll([FromServices] IHttpFileManager fileManager, [FromRoute] int id)
    {
        try
        {
            // 1. 双重权限验证：注解验证 + 手动验证
            if (!WorkflowPermissionManager.IsAdmin(User)) return Forbid();

            // 2. 获取所有工作流实例
            var allInstances = _processInstanceStore.ProcessInstances.Where(_ => _.WorkProcess.Id == id).ToList();
            var deletedCount = 0; // 成功删除的数量
            var errors = new List<string>(); // 删除失败的错误信息

            // 3. 逐个删除工作流实例，确保部分失败不影响其他实例的删除
            foreach (var instance in allInstances)
                try
                {
                    await _workflowManager.DeleteAsync(instance.Id, fileManager, User.ToUserViewModel());
                    deletedCount++;
                }
                catch (Exception ex)
                {
                    // 记录单个实例删除失败的信息，但继续处理其他实例
                    errors.Add($"删除实例 {instance.Id} 失败: {ex.Message}");
                    _logger.LogError(ex, "删除工作流实例 {ProcessInstanceId} 时发生错误", instance.Id);
                }

            // 4. 记录重要的管理操作日志
            _logger.LogWarning("用户 {User} 执行了清空所有工作流实例操作，成功删除 {DeletedCount} 个实例",
                User.Identity?.Name, deletedCount);

            // 5. 构建操作结果
            var result = new SwfResult<object>
            {
                Code = 1,
                Message = $"清空操作完成，成功删除 {deletedCount} 个工作流实例",
                Data = new { deletedCount, errors = errors.ToArray() }
            };

            // 6. 如果有失败的情况，在消息中提醒用户
            if (errors.Any()) result.Message += $"，有 {errors.Count} 个实例删除失败";

            return Ok(result.ToSuccess());
        }
        catch (Exception ex)
        {
            // 记录整体操作失败的错误日志
            _logger.LogError(ex, "清空所有工作流实例时发生错误");
            return BadRequest(new SwfResult<object>
            {
                Code = 0,
                Message = "清空所有工作流实例失败: " + ex.Message,
                Data = null
            });
        }
    }

    /// <summary>
    ///     获取工单列表
    /// </summary>
    /// <param name="searcher">查询参数</param>
    /// <param name="permissionManager"></param>
    /// <param name="userSettingStore"></param>
    /// <returns></returns>
    [HttpGet("list")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<InstanceListItemViewModel>>))]
    public async Task<IActionResult> List(
        [FromQuery] ProcessInstanceSearcher searcher,
        [FromServices] WorkflowPermissionManager permissionManager
        , [FromServices] IUserSettingStore userSettingStore)
    {
        var processInstanceListItemViewModels =
            await _processInstanceStore.ListAsync(searcher, User, permissionManager);

        var result = processInstanceListItemViewModels.ToArray();
        await permissionManager.FillProcessInstanceList(result, User);

        return Ok(result.ToSuccess());
    }
    /// <summary>
    /// 获取某个流程实例的附件列表
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    [HttpGet("get-files-by-processInstanceId/{processInstanceId}")]
    public async Task<IActionResult> GetFilesByProcessInstanceId(int processInstanceId)
    {
        var processInstance = _processInstanceStore.GetById(processInstanceId);

        // if (!_workflowPermissionManager.HasReadPermissionAsync(processInstance, User).Result) return Forbid();

        if (processInstance == null)
            return NotFound(new { success = false, message = "没有找到id=" + processInstanceId + "工作实例。" });

        if (processInstance.Files == null || processInstance.Files.Count == 0)
            return Ok(Array.Empty<object>().ToSuccess());
        IEnumerable<ProcessInstance.FileAttach> files = processInstance.Files
            .Where(fileAttach => fileAttach.FileType == FileType.附件)
            .ToArray();

        // IEnumerable<SimpleSFileViewModel> fsfiles = await _fileManager.ListFilesAsync(files.Select(fileAttach => fileAttach.FileId).ToArray());


        return Ok(files.Select(_ => _.ToViewModel()).ToSuccess());
    }



    /// <summary>
    ///     删除工单附件附件
    /// </summary>
    /// <param name="id">文件id</param>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    [HttpDelete("deleteFile/{fileId}/{processInstanceId}")]
    public IActionResult DeleteFile([FromRoute] string fileId, [FromRoute] int processInstanceId)
    {
        var pi = _workflowManager.GetById(processInstanceId); ;

        foreach (var file in pi.Files)
            if (file.FileId == fileId)
            {
                _fileManager.Delete(file.FileId);

                _context.Remove(file);
                _context.SaveChanges();
                break;
            }

        _logger.LogWarning(new EventId(900), $"删除文件{fileId}，属于工作流实例{processInstanceId}");
        return Ok(new { success = true, message = "删除成功！" }.ToSuccess());
    }

    [HttpGet("upload/{fileId}/{processInstanceId:int}")]
    public async Task<IActionResult> Upload([FromRoute] string fileId, [FromRoute] int processInstanceId)
    {

        var pi = _workflowManager.GetById(processInstanceId);
        if (pi == null) throw new NotFoundProcessInstanceException(processInstanceId);

        var user = User.Identity?.Name;
#if DEBUG
        if (string.IsNullOrEmpty(user))
            user = "admin";
#endif


        var sFileData = await _fileManager.Get(fileId);
        var sFile = sFileData;
        var newfile = new ProcessInstance.FileAttach
        {
            FileId = sFile.Id,
            FileName = sFile.Name,
            FileType = FileType.附件,
            CreateUser = user
        };
        try
        {
            pi.Files.Add(newfile);
            _context.Add(newfile);
            _context.SaveChanges();

            _logger.LogWarning(new EventId(900), $"上传的Upload{fileId}{sFile.Name}，属于工作流实例{processInstanceId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传文件失败");
            return BadRequest(new { success = false, message = "上传失败！" }.ToSuccess());
        }
        return Ok(new FileAttachSubmit
        {
            FileId = sFile.Id,
            FileName = sFile.Name
        }.ToSuccess());
    }


    /// <summary>
    ///     获取工作流实例的数目
    /// </summary>
    /// <param name="searcher">查询参数</param>
    /// <returns></returns>
    [HttpGet("count")]
    [ProducesDefaultResponseType(typeof(SwfResult<int>))]
    public async Task<IActionResult> Count([FromQuery] ProcessInstanceSearcher searcher)
    {
        var result = await _processInstanceStore.CountAsync(searcher, User, _workflowPermissionManager);
        return Ok(result.ToSuccess());
    }
}