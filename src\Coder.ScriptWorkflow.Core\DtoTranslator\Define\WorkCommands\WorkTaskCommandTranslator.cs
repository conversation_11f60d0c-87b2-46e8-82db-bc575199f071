﻿using System;
using System.Linq;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Coder.ScriptWorkflow.WorkTaskCommands;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.WorkCommands;

/// <summary>
/// </summary>
internal abstract class WorkTaskCommandTranslator
{
    private static readonly PreviousWorkTaskCommandTranslator PreviousWorkTaskCommandTranslator = new();
    private static readonly ScriptWorkTaskCommandTranslator ScriptWorkTaskCommandTranslator = new();

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public static WorkTaskCommandTranslator GetBy(WorkTaskCommandSubmit submit)
    {
        switch (submit)
        {
            case PreviousCommandSubmit a:
                return PreviousWorkTaskCommandTranslator;
            case WorkTaskScriptCommandSubmit b:
                return ScriptWorkTaskCommandTranslator;
            default:
                throw new NotImplementedException(submit.Type + "没有任何转换器。");
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public static WorkTaskCommandSubmit TranslateToViewModel(WorkTaskCommand command)
    {
        switch (command)
        {
            case PreviousWorkTaskCommand a:
                return PreviousWorkTaskCommandTranslator.ToViewModel(a);
            case WorkTaskScriptCommand b:
                return ScriptWorkTaskCommandTranslator.ToViewModel(b);
            default:
                throw new NotImplementedException(command.GetType().Name + "没有任何转换器。");
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="workTask"></param>
    /// <returns></returns>
    public virtual WorkTaskCommand FillTo(WorkTaskCommandSubmit submit, WorkTask workTask)
    {
        var command = FindBy(submit, workTask);
        command.Name = submit.Name;
        command.Order = submit.Order;

        return command;
    }

    /// <summary>
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    public virtual WorkTaskCommandSubmit ToViewModel(WorkTaskCommand command)
    {
        var result = CreateViewModel();
        result.Name = command.Name;
        result.Id = command.Id;
        result.Order = command.Order;
        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="worktask"></param>
    /// <returns></returns>
    private WorkTaskCommand FindBy(WorkTaskCommandSubmit submit, WorkTask worktask)
    {
        if (submit.Id == 0) return CreateEntity();
        return worktask.Commands.FirstOrDefault(_ => _.Id == submit.Id) ?? CreateEntity();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected abstract WorkTaskCommand CreateEntity();

    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected abstract WorkTaskCommandSubmit CreateViewModel();
}