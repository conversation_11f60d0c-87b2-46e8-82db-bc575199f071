﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;

namespace Coder.ScriptWorkflow.Stores;

public interface IGlobalVariableStore
{
    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GlobalVariable> GetAsync(int id);

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    Task DeleteAsync(int id);

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate(GlobalVariable workTask);

    /// <summary>
    ///     保存保存
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<IEnumerable<GlobalVariable>> ListAsync(string name = null);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GlobalVariable>> ListAllAsync(GlobalVariableEnv env);
}