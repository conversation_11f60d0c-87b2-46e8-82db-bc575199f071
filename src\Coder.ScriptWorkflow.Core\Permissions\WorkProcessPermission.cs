﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Coder.Member;

namespace Coder.ScriptWorkflow.Permissions;

/// <summary>
///     工作流成权限设置。
/// </summary>
public class WorkProcessPermission
{
    private IList<PermissionPerformer> _performers;

    /// <summary>
    /// </summary>
    public int Id { get; set; }


    /// <summary>
    ///     执行者，谁能创建
    /// </summary>
    public virtual IList<PermissionPerformer> Performers
    {
        get
        {
            if (_performers == null) _performers = new List<PermissionPerformer>();

            return _performers;
        }
        set => _performers = value;
    }

    /// <summary>
    ///     流程名称
    /// </summary>
    public string ProcessName { get; set; }

    /// <summary>
    ///     流程管理员,用','逗号隔开。
    /// </summary>
    public string ManageRoles { get; set; }

    /// <summary>
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public bool IsManager(ClaimsPrincipal user)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));
        if (!string.IsNullOrWhiteSpace(ManageRoles))
        {
            foreach (var role in ManageRoles.Split(','))
                if (user.IsInRole(role))
                    return true;
        }
        else
        {
            if (user.Identity.Name == "admin") return true;
        }

        return false;
    }

    public bool IsPerformer(ClaimsPrincipal user)
    {
        var orgs = user.Claims.Where(_ => _.Type == UserDefined.ClaimOrg).Select(_ => _.Value);
        foreach (var p in Performers)
            switch (p.Type)
            {
                case PerformerType.Org:
                    if (orgs.Contains(p.Name))
                        return true;
                    break;
                case PerformerType.User:
                    if (p.Name == user.Identity.Name) return true;
                    break;
                case PerformerType.Role:
                    if (user.IsInRole(p.Name))
                        return true;
                    break;
            }

        return false;
    }
}