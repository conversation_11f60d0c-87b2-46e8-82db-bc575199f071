﻿using System;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.UnitTest.WorkflowManagers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NiL.JS.Core;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Example.请假;

/// <summary>
/// </summary>
public class Test
{
    private static UserViewModel Creator = new UserViewModel()
    {
        UserName = "creator",
    };
    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();

        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options => { options.UseSqlite($"Data Source={dbFile}.db;"); });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();
        scope.Dispose();
        return sp;
    }

    [Fact]
    public void Retry()
    {
        /*
         * form 定义
declare class formClass
{
请假人:String
开始日期:Date
结束日期:Date
总天数:number
总经理:Boolean
主管部门:Boolean
}

         */
        var sp = Sp();

        var workSubmit = 工作流创建.Create();
        var workflowDefinedManager = sp.GetRequiredService<WorkflowDefinedManager>();
        workflowDefinedManager.Save(workSubmit);

        var workflowManager = sp.GetRequiredService<WorkflowManager>();

        var processInstance = workflowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = workSubmit.Name }, "user");
        var start = new DateTimeOffset(DateTime.Today);
        var end = start.AddDays(4);
        processInstance.MergeForm(new ProcessInstanceFormSubmit
        {
            Form = ToJSON(start, end)
        });
        var startResult = workflowManager.Start(processInstance,Creator);
        Assert.True(startResult.Success, startResult.Message);

        var draft = startResult.WorkActivities.First();
        Assert.Equal(processInstance.Creator, draft.DisposeUser);
        var result = workflowManager.Resolve(draft.WorkActivityId, new WorkflowResolveSubmit
        {
            Command = "提交",
            Comment = "test"
        },Creator);

        //主管部门审批
        Assert.True(result.Success, result.Message);
        var supervisorAuthId = result.WorkActivities.First();
        var supervisorAuth = workflowManager.GetWorkActivityById(supervisorAuthId.WorkActivityId);
        Assert.Equal(supervisorAuth.WorkTask.Name, 工作流创建.主管部门审批);

        var formData = GetFromForm(supervisorAuth.ProcessInstance.Form);
        var startDate =formData["开始日期"].Value.ToString();
        var endDate = formData["结束日期"].Value.ToString();
        Assert.Equal(start.ToString("yyyy-MM-ddT00:00:00Z"), startDate);
        Assert.Equal(end.ToString("yyyy-MM-ddT00:00:00Z"), endDate);
        //同意后到总经理
        var managerResult = workflowManager.Resolve(supervisorAuthId.WorkActivityId, new WorkflowResolveSubmit
        {
            Command = "同意",
            Comment = "test"
        },Creator);
        Assert.Equal(工作流创建.总经理审批, managerResult.WorkActivities.First().WorkTaskName);
        Assert.True(managerResult.Success, managerResult.Message);
    }

    /// <summary>
    /// </summary>
    /// <param name="form"></param>
    /// <returns></returns>
    private static JSValue GetFromForm(string form)
    {
        var script = $"var j=JSON.parse('{form}',JSON.dateParser)";
        Context ctx = new();
        ctx.Eval(script);
        return ctx.GetVariable("j");
    }

    private static string ToJSON(DateTimeOffset start, DateTimeOffset end)
    {
        //var startStr = $"{start.Year},{start.Month},{start.Day},{start.Hour},{start.Minute},{start.Second}";
        //var endStr = $"{end.Year},{end.Month},{end.Day},{end.Hour},{end.Minute},{end.Second}";
        var startStr = start.ToString("yyyy-MM-ddTHH:mm:ss");
        var endStr = end.ToString("yyyy-MM-ddTHH:mm:ss");
        Context ctx = new();
        ctx.Eval($@"var a={{
开始日期:new Date('{startStr}'),
结束日期:new Date('{endStr}')
}}
var json=JSON.stringify(a)
");
        var s = (string)ctx.GetVariable("json").Value;
        return s;
    }
}