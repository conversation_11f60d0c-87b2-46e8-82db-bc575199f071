# ScriptWorkflow 工作流引擎设计文档

## 1. 项目概述

### 1.1 项目简介
ScriptWorkflow 是一个基于 .NET 9.0 的可编程工作流引擎，支持通过 JavaScript 脚本定义复杂的业务流程。该系统采用插件化架构，提供了灵活的扩展机制和强大的脚本执行环境。

### 1.2 核心特性
- **脚本驱动**: 使用 JavaScript 定义工作流逻辑
- **插件化架构**: 支持自定义插件扩展功能
- **多数据库支持**: 支持 SQLite、MySQL、SQL Server、达梦数据库
- **RESTful API**: 提供完整的 REST API 接口
- **可视化设计**: 支持图形化工作流设计
- **权限控制**: 细粒度的权限管理机制
- **审计追踪**: 完整的操作日志和审计功能

### 1.3 技术栈
- **后端框架**: ASP.NET Core 9.0
- **数据访问**: Entity Framework Core 9.0
- **脚本引擎**: JavaScript (V8)
- **数据库**: SQLite/MySQL/SQL Server/达梦
- **API文档**: Swagger/OpenAPI
- **测试框架**: xUnit + FluentAssertions + Moq

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "表示层 (Presentation Layer)"
        API[Web API Controllers]
        Swagger[Swagger UI]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        WM[WorkflowManager]
        PM[PluginManager]
        SM[ScriptManager]
        Auth[权限管理]
    end
    
    subgraph "脚本执行层 (Script Execution Layer)"
        SE[脚本引擎]
        Plugins[插件系统]
        Context[执行上下文]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        Repo[仓储模式]
        UOW[工作单元]
        EF[Entity Framework]
    end
    
    subgraph "数据存储层 (Data Storage Layer)"
        SQLite[(SQLite)]
        MySQL[(MySQL)]
        MSSQL[(SQL Server)]
        DM[(达梦数据库)]
    end
    
    API --> WM
    API --> PM
    WM --> SE
    WM --> Auth
    SE --> Plugins
    SE --> Context
    WM --> Repo
    Repo --> UOW
    UOW --> EF
    EF --> SQLite
    EF --> MySQL
    EF --> MSSQL
    EF --> DM
```

### 2.2 分层架构

#### 2.2.1 表示层 (Presentation Layer)
- **Web API Controllers**: RESTful API 接口
- **模型验证**: 请求/响应模型验证
- **异常处理**: 全局异常处理机制
- **API版本控制**: 支持多版本API

#### 2.2.2 业务逻辑层 (Business Logic Layer)
- **WorkflowManager**: 工作流核心管理器
- **PluginManager**: 插件管理器
- **权限管理**: 基于角色的访问控制
- **配置管理**: 统一配置管理

#### 2.2.3 脚本执行层 (Script Execution Layer)
- **JavaScript引擎**: V8 脚本执行引擎
- **插件系统**: 可扩展的插件架构
- **执行上下文**: 脚本执行环境管理

#### 2.2.4 数据访问层 (Data Access Layer)
- **仓储模式**: 统一的数据访问接口
- **工作单元**: 事务管理
- **Entity Framework**: ORM 框架

## 3. 核心领域模型

### 3.1 工作流定义 (WorkProcess)

```csharp
/// <summary>
/// 工作流程定义类，包含工作流的所有配置信息和基本属性
/// </summary>
public class WorkProcess
{
    /// <summary>
    /// 工作流唯一标识符
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 工作流名称（唯一）
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; }
    
    /// <summary>
    /// 工作流描述
    /// </summary>
    public string Description { get; set; }
    
    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
    
    /// <summary>
    /// 表单设计JSON
    /// </summary>
    public string FormDesign { get; set; }
    
    /// <summary>
    /// 全局脚本代码
    /// </summary>
    public string GlobalScript { get; set; }
    
    /// <summary>
    /// 启用的插件列表
    /// </summary>
    public IList<string> Plugins { get; set; }
    
    /// <summary>
    /// 工作流节点集合
    /// </summary>
    public virtual ICollection<Node> Nodes { get; set; }
}
```

### 3.2 流程实例 (ProcessInstance)

```csharp
/// <summary>
/// 工作流实例 - 工作流定义的具体执行实例
/// </summary>
public class ProcessInstance
{
    /// <summary>
    /// 实例唯一标识符
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 工单号（业务唯一标识）
    /// </summary>
    public string Number { get; set; }
    
    /// <summary>
    /// 实例标题
    /// </summary>
    public string Subject { get; set; }
    
    /// <summary>
    /// 关联的工作流定义
    /// </summary>
    public virtual WorkProcess WorkProcess { get; set; }
    
    /// <summary>
    /// 实例状态
    /// </summary>
    public ProcessInstanceStatus Status { get; set; }
    
    /// <summary>
    /// 创建者
    /// </summary>
    public string Creator { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreateTime { get; set; }
    
    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTimeOffset? FinishTime { get; set; }
    
    /// <summary>
    /// 表单数据JSON
    /// </summary>
    public string Form { get; set; }
    
    /// <summary>
    /// 工作活动集合
    /// </summary>
    public virtual ICollection<WorkActivity> WorkActivities { get; set; }
}
```

### 3.3 工作活动 (WorkActivity)

```csharp
/// <summary>
/// 工作活动 - 工作流中的具体执行单元
/// </summary>
public class WorkActivity
{
    /// <summary>
    /// 活动唯一标识符
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 关联的流程实例
    /// </summary>
    public virtual ProcessInstance ProcessInstance { get; set; }
    
    /// <summary>
    /// 关联的工作任务节点
    /// </summary>
    public virtual WorkTask WorkTask { get; set; }
    
    /// <summary>
    /// 活动状态
    /// </summary>
    public WorkActivityStatus Status { get; set; }
    
    /// <summary>
    /// 分配的执行者
    /// </summary>
    public virtual IEnumerable<Performer> AssignPerformers { get; set; }
    
    /// <summary>
    /// 实际处理用户
    /// </summary>
    public string DisposeUser { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? DisposeTime { get; set; }
    
    /// <summary>
    /// 处理意见
    /// </summary>
    public string Comment { get; set; }
    
    /// <summary>
    /// 执行命令JSON
    /// </summary>
    public string Command { get; set; }
}
```

## 4. 工作流执行引擎

### 4.1 执行流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API层
    participant WM as WorkflowManager
    participant SE as 脚本引擎
    participant DB as 数据库
    
    Client->>API: 创建工作流实例
    API->>WM: CreateProcessInstance
    WM->>DB: 保存实例
    WM->>SE: 执行启动脚本
    SE->>WM: 返回执行结果
    WM->>DB: 创建工作活动
    WM->>API: 返回创建结果
    API->>Client: 返回响应
    
    Client->>API: 处理工作活动
    API->>WM: ResolveWorkActivity
    WM->>SE: 执行任务脚本
    SE->>WM: 返回执行结果
    WM->>SE: 执行决策脚本
    SE->>WM: 返回下一节点
    WM->>DB: 更新活动状态
    WM->>DB: 创建新活动
    WM->>API: 返回处理结果
    API->>Client: 返回响应
```

### 4.2 脚本执行上下文

```csharp
/// <summary>
/// 工作流执行上下文 - 提供脚本执行环境
/// </summary>
public interface IWorkflowContext
{
    /// <summary>
    /// 当前流程实例
    /// </summary>
    ProcessInstance ProcessInstance { get; }
    
    /// <summary>
    /// 当前工作活动
    /// </summary>
    WorkActivity CurrentWorkActivity { get; }
    
    /// <summary>
    /// 当前节点
    /// </summary>
    Node CurrentNode { get; }
    
    /// <summary>
    /// 全局脚本上下文
    /// </summary>
    GlobalScriptContext GlobalContext { get; }
    
    /// <summary>
    /// 可用插件列表
    /// </summary>
    IList<IPlugin> Plugins { get; }
    
    /// <summary>
    /// 日志管理器
    /// </summary>
    IWorkflowLogManager Logger { get; }
    
    /// <summary>
    /// 调试器管理器
    /// </summary>
    IDebuggerManager Debugger { get; }
}
```

## 5. 插件系统架构

### 5.1 插件接口定义

```csharp
/// <summary>
/// 插件基础接口
/// </summary>
public interface IPlugin
{
    /// <summary>
    /// 插件名称（在JavaScript中的变量名）
    /// </summary>
    string Name { get; }
    
    /// <summary>
    /// 获取插件对象实例
    /// </summary>
    object GetObject(IWorkflowContext context);
    
    /// <summary>
    /// 释放插件资源
    /// </summary>
    void Dispose(object o);
    
    /// <summary>
    /// 获取JavaScript类型定义
    /// </summary>
    string GetJsDefined();
}
```

### 5.2 插件注册机制

```csharp
/// <summary>
/// 插件注册器 - 管理插件的注册、发现和依赖解析
/// </summary>
public class PluginRegistry
{
    /// <summary>
    /// 自动发现插件
    /// </summary>
    public PluginRegistry AutoDiscoverPlugins()
    {
        var assemblies = AppDomain.CurrentDomain.GetAssemblies();
        foreach (var assembly in assemblies)
        {
            var pluginTypes = assembly.GetTypes()
                .Where(t => typeof(IPlugin).IsAssignableFrom(t) && !t.IsAbstract);
                
            foreach (var type in pluginTypes)
            {
                RegisterPlugin(type);
            }
        }
        return this;
    }
    
    /// <summary>
    /// 注册插件类型
    /// </summary>
    public PluginRegistry RegisterPlugin(Type pluginType)
    {
        var descriptor = CreatePluginDescriptor(pluginType);
        _registeredPlugins[descriptor.Name] = descriptor;
        
        // 注册到DI容器
        _services.AddTransient(typeof(IPlugin), pluginType);
        return this;
    }
}
```

## 6. 数据访问层设计

### 6.1 仓储模式

```csharp
/// <summary>
/// 通用仓储接口
/// </summary>
public interface IRepository<TEntity> where TEntity : class
{
    /// <summary>
    /// 查询接口
    /// </summary>
    IQueryable<TEntity> Query { get; }
    
    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    Task<TEntity?> GetByIdAsync(object id, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 添加实体
    /// </summary>
    Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新实体
    /// </summary>
    Task<TEntity> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除实体
    /// </summary>
    Task DeleteAsync(TEntity entity, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 分页查询
    /// </summary>
    Task<PagedResult<TEntity>> GetPagedAsync(
        Expression<Func<TEntity, bool>>? predicate = null,
        int pageNumber = 1,
        int pageSize = 10,
        CancellationToken cancellationToken = default);
}
```

### 6.2 工作单元模式

```csharp
/// <summary>
/// 工作单元接口 - 管理事务和数据一致性
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 获取仓储实例
    /// </summary>
    IRepository<TEntity> GetRepository<TEntity>() where TEntity : class;
    
    /// <summary>
    /// 开始事务
    /// </summary>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 提交事务
    /// </summary>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 回滚事务
    /// </summary>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 保存所有变更
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
```

## 7. API层设计

### 7.1 控制器基类

```csharp
/// <summary>
/// API控制器基类 - 提供统一的响应格式和错误处理
/// </summary>
[ApiController]
[EnableCors]
[Authorize]
public abstract class ApiControllerBase : ControllerBase
{
    protected readonly ILogger Logger;

    protected ApiControllerBase(ILogger logger)
    {
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 返回成功响应
    /// </summary>
    protected IActionResult Success<T>(T data, string? message = null)
    {
        var response = new SuccessResponse<T>
        {
            Success = true,
            Data = data,
            Message = message ?? "操作成功",
            Timestamp = DateTimeOffset.UtcNow
        };
        return Ok(response);
    }

    /// <summary>
    /// 返回错误响应
    /// </summary>
    protected IActionResult Error(string message, string? errorCode = null)
    {
        var response = new ErrorResponse
        {
            Success = false,
            ErrorCode = errorCode ?? "API_ERROR",
            Message = message,
            RequestId = HttpContext.TraceIdentifier,
            Timestamp = DateTimeOffset.UtcNow
        };
        return BadRequest(response);
    }
}
```

### 7.2 API版本控制

```csharp
/// <summary>
/// 工作流管理控制器 V1
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/workflows")]
[Tags("Workflow")]
public class WorkflowV1Controller : ApiControllerBase
{
    /// <summary>
    /// 创建工作流实例
    /// </summary>
    [HttpPost("create")]
    [ProducesResponseType(typeof(SuccessResponse<CreateProcessInstanceResult>), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    public async Task<IActionResult> CreateProcessInstance([FromBody] CreateProcessInstanceRequest request)
    {
        return await ExecuteAsync(async () =>
        {
            var result = await _workflowManager.CreateProcessInstanceAsync(request);
            return result;
        }, nameof(CreateProcessInstance));
    }
}
```

## 8. 配置管理系统

### 8.1 配置结构

```json
{
  "Database": {
    "DbType": "SQLITE",
    "ConnectionString": "Data Source=workflow.db",
    "CommandTimeoutSeconds": 30,
    "MaxPoolSize": 100,
    "EnableSensitiveDataLogging": false
  },
  "Workflow": {
    "FileSystemHost": "http://localhost/fs",
    "ScriptExecutionTimeoutMs": 30000,
    "EnableDebugMode": false,
    "MaxConcurrentWorkflowInstances": 100
  },
  "Plugins": {
    "EnableAutoDiscovery": true,
    "DefaultPlugins": [
      {
        "Name": "Console",
        "Enabled": true,
        "Configuration": {}
      }
    ]
  }
}
```

### 8.2 配置类定义

```csharp
/// <summary>
/// 工作流配置
/// </summary>
public class WorkflowConfiguration
{
    /// <summary>
    /// 文件系统主机地址
    /// </summary>
    public string FileSystemHost { get; set; } = string.Empty;
    
    /// <summary>
    /// 脚本执行超时时间（毫秒）
    /// </summary>
    public int ScriptExecutionTimeoutMs { get; set; } = 30000;
    
    /// <summary>
    /// 是否启用调试模式
    /// </summary>
    public bool EnableDebugMode { get; set; } = false;
    
    /// <summary>
    /// 最大并发工作流实例数
    /// </summary>
    public int MaxConcurrentWorkflowInstances { get; set; } = 100;
}
```

## 9. 异常处理机制

### 9.1 异常层次结构

```csharp
/// <summary>
/// 工作流基础异常
/// </summary>
public abstract class WorkflowException : Exception
{
    protected WorkflowException(string message) : base(message) { }
    protected WorkflowException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 工作流定义异常
/// </summary>
public class WorkflowDefinedException : WorkflowException
{
    public string Module { get; set; } = string.Empty;
    
    public WorkflowDefinedException(string message) : base(message) { }
    public WorkflowDefinedException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 工作流验证异常
/// </summary>
public class WorkflowValidationException : WorkflowException
{
    public Dictionary<string, string[]> ValidationErrors { get; set; } = new();
    
    public WorkflowValidationException(string message) : base(message) { }
    public WorkflowValidationException(string message, Dictionary<string, string[]> validationErrors) : base(message)
    {
        ValidationErrors = validationErrors;
    }
}
```

### 9.2 全局异常处理

```csharp
/// <summary>
/// 全局异常处理器
/// </summary>
public class ExceptionHandler
{
    private readonly ILogger _logger;
    private readonly bool _isDevelopment;

    public ExceptionHandler(ILogger logger, bool isDevelopment)
    {
        _logger = logger;
        _isDevelopment = isDevelopment;
    }

    /// <summary>
    /// 处理异常并返回标准化响应
    /// </summary>
    public ErrorResponse HandleException(Exception exception, string requestId)
    {
        _logger.LogError(exception, "Unhandled exception occurred. RequestId: {RequestId}", requestId);

        return exception switch
        {
            WorkflowValidationException validationEx => CreateValidationErrorResponse(validationEx, requestId),
            WorkflowPermissionException permissionEx => CreatePermissionErrorResponse(permissionEx, requestId),
            WorkflowNotFoundException notFoundEx => CreateNotFoundErrorResponse(notFoundEx, requestId),
            WorkflowDefinedException definedEx => CreateDefinedErrorResponse(definedEx, requestId),
            _ => CreateGenericErrorResponse(exception, requestId)
        };
    }
}
```

## 10. 测试策略

### 10.1 测试架构

```mermaid
graph TB
    subgraph "测试层次"
        UT[单元测试]
        IT[集成测试]
        PT[性能测试]
        ST[压力测试]
    end
    
    subgraph "测试工具"
        xUnit[xUnit Framework]
        Moq[Moq Mocking]
        FA[FluentAssertions]
        NBomber[NBomber Load Testing]
    end
    
    subgraph "测试覆盖"
        Core[核心业务逻辑]
        API[API接口]
        Data[数据访问层]
        Plugins[插件系统]
    end
    
    UT --> Core
    IT --> API
    PT --> Data
    ST --> Plugins
```

### 10.2 测试基础设施

```csharp
/// <summary>
/// 测试基类 - 提供统一的测试环境
/// </summary>
public abstract class TestFixtureBase : IDisposable
{
    protected IServiceProvider ServiceProvider { get; private set; }
    protected ApplicationDbContext DbContext { get; private set; }

    protected TestFixtureBase()
    {
        var services = new ServiceCollection();
        ConfigureServices(services);
        ServiceProvider = services.BuildServiceProvider();
        
        // 使用内存数据库
        DbContext = ServiceProvider.GetRequiredService<ApplicationDbContext>();
        DbContext.Database.EnsureCreated();
        
        SeedTestData();
    }

    /// <summary>
    /// 配置测试服务
    /// </summary>
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // 配置内存数据库
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid():N}"));
            
        // 配置工作流服务
        services.AddScriptWorkflowServices(Configuration);
    }
}
```

## 11. 部署和运维

### 11.1 部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器]
    end
    
    subgraph "应用服务层"
        API1[API服务实例1]
        API2[API服务实例2]
        API3[API服务实例3]
    end
    
    subgraph "数据存储层"
        DB[(主数据库)]
        Cache[(Redis缓存)]
        Files[(文件存储)]
    end
    
    subgraph "监控层"
        Logs[日志收集]
        Metrics[指标监控]
        Health[健康检查]
    end
    
    LB --> API1
    LB --> API2
    LB --> API3
    
    API1 --> DB
    API2 --> DB
    API3 --> DB
    
    API1 --> Cache
    API2 --> Cache
    API3 --> Cache
    
    API1 --> Logs
    API2 --> Logs
    API3 --> Logs
```

### 11.2 监控和日志

```csharp
/// <summary>
/// 健康检查配置
/// </summary>
public static class HealthCheckExtensions
{
    public static IServiceCollection AddWorkflowHealthChecks(this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddDbContextCheck<ApplicationDbContext>("database")
            .AddCheck<PluginHealthCheck>("plugins")
            .AddCheck<ScriptEngineHealthCheck>("script-engine");
            
        return services;
    }
}
```

## 12. 安全考虑

### 12.1 认证和授权

```csharp
/// <summary>
/// 权限管理器
/// </summary>
public class WorkflowPermissionManager
{
    /// <summary>
    /// 检查创建权限
    /// </summary>
    public async Task<bool> HasCreatePermissionAsync(ClaimsPrincipal user, string workflowName)
    {
        // 实现权限检查逻辑
        return await CheckPermissionAsync(user, "workflow.create", workflowName);
    }
    
    /// <summary>
    /// 检查读取权限
    /// </summary>
    public async Task<bool> HasReadPermissionAsync(ProcessInstance instance, ClaimsPrincipal user)
    {
        // 实现权限检查逻辑
        return await CheckPermissionAsync(user, "workflow.read", instance.Id.ToString());
    }
}
```

### 12.2 脚本安全

```csharp
/// <summary>
/// 脚本安全管理器
/// </summary>
public class ScriptSecurityManager
{
    /// <summary>
    /// 验证脚本安全性
    /// </summary>
    public bool ValidateScript(string script)
    {
        // 检查危险函数调用
        var dangerousPatterns = new[]
        {
            @"eval\s*\(",
            @"Function\s*\(",
            @"require\s*\(",
            @"import\s*\("
        };
        
        return !dangerousPatterns.Any(pattern => 
            Regex.IsMatch(script, pattern, RegexOptions.IgnoreCase));
    }
}
```

## 13. 性能优化

### 13.1 缓存策略

```csharp
/// <summary>
/// 工作流缓存管理器
/// </summary>
public class WorkflowCacheManager
{
    private readonly IMemoryCache _cache;
    
    /// <summary>
    /// 缓存工作流定义
    /// </summary>
    public async Task<WorkProcess> GetWorkflowAsync(string name)
    {
        return await _cache.GetOrCreateAsync($"workflow:{name}", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30);
            return await _workProcessStore.GetByNameAsync(name);
        });
    }
}
```

### 13.2 数据库优化

```csharp
/// <summary>
/// 查询优化服务
/// </summary>
public class QueryOptimizationService
{
    /// <summary>
    /// 分析查询性能
    /// </summary>
    public async Task<QueryPerformanceInfo> AnalyzeQueryPerformanceAsync(string query)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            await _context.Database.ExecuteSqlRawAsync($"EXPLAIN {query}");
            stopwatch.Stop();
            
            return new QueryPerformanceInfo
            {
                Query = query,
                ExecutionTime = stopwatch.Elapsed,
                IsOptimized = stopwatch.ElapsedMilliseconds < 1000
            };
        }
        catch (Exception ex)
        {
            return new QueryPerformanceInfo
            {
                Query = query,
                ExecutionTime = stopwatch.Elapsed,
                IsOptimized = false,
                ErrorMessage = ex.Message
            };
        }
    }
}
```

## 14. 扩展和定制

### 14.1 自定义插件开发

```csharp
/// <summary>
/// 自定义插件示例
/// </summary>
[Plugin(Name = "CustomPlugin", Description = "自定义插件示例", Version = "1.0.0")]
public class CustomPlugin : IPlugin, IPluginInitializable
{
    public string Name => "CustomPlugin";

    public void Initialize(Dictionary<string, object> configuration)
    {
        // 插件初始化逻辑
    }

    public object GetObject(IWorkflowContext context)
    {
        return new CustomPluginObject(context);
    }

    public void Dispose(object o)
    {
        // 资源清理
    }

    public string GetJsDefined()
    {
        return @"
interface CustomPluginClass {
    doSomething(param: string): string;
}
declare var CustomPlugin: CustomPluginClass;
        ";
    }
}
```

### 14.2 工作流扩展点

```csharp
/// <summary>
/// 工作流扩展接口
/// </summary>
public interface IWorkflowExtension
{
    /// <summary>
    /// 实例创建前
    /// </summary>
    Task OnInstanceCreatingAsync(ProcessInstance instance);
    
    /// <summary>
    /// 实例创建后
    /// </summary>
    Task OnInstanceCreatedAsync(ProcessInstance instance);
    
    /// <summary>
    /// 活动开始前
    /// </summary>
    Task OnActivityStartingAsync(WorkActivity activity);
    
    /// <summary>
    /// 活动完成后
    /// </summary>
    Task OnActivityCompletedAsync(WorkActivity activity);
}
```

---

## 附录

### A. 术语表

- **WorkProcess**: 工作流定义，描述业务流程的模板
- **ProcessInstance**: 流程实例，工作流定义的具体执行实例
- **WorkActivity**: 工作活动，流程中的具体执行单元
- **WorkTask**: 工作任务，流程节点的具体实现
- **Plugin**: 插件，扩展工作流功能的组件
- **Script**: 脚本，使用JavaScript编写的业务逻辑

### B. 参考资料

- [ASP.NET Core 文档](https://docs.microsoft.com/aspnet/core)
- [Entity Framework Core 文档](https://docs.microsoft.com/ef/core)
- [JavaScript V8 引擎](https://v8.dev/)
- [工作流模式](https://www.workflowpatterns.com/)

### C. 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2025-01-30 | 初始版本 |

---

*本文档由 ScriptWorkflow 开发团队维护，最后更新时间：2025年1月30日*
