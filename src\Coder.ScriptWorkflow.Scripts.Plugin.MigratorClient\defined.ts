﻿declare const jsDatahPusher: jsDatahPusherClass;


/**
 * jsDatahPusherClass
 */
interface jsDatahPusherClass {
    /**
     * 更新数据，dataObject必须带有ParimaryKey的值，如果不知道可以再迁移系统中找到相关配置
     * @param connection 数据库链接名称
     * @param table 数据表名称
     * @param dataObject 对象，不需要更新的字段，可以不设置。
     */
    Update(connection: string, table: string, dataObject: any): void;

    /**
     * 新增一条数据,
     * @param connection 数据库链接名称
     * @param table 数据表名称
     * @param dataObject 对象，不需要更新的字段，可以不设置。
     */
    Insert(connection: string, table: string, dataObject: any): Array<any>;


}