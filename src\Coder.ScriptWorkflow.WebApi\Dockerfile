# 请参阅 https://aka.ms/customizecontainer 以了解如何自定义调试容器，以及 Visual Studio 如何使用此 Dockerfile 生成映像以更快地进行调试。

# 此阶段用于在快速模式(默认为调试配置)下从 VS 运行时
FROM zhcoder-docker-registry.com:8000/library/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080

# 此阶段用于生成服务项目
FROM zhcoder-docker-registry.com:8000/builder/dotnet_sdk:6_and_8_and_9 AS build
ARG BUILD_CONFIGURATION=Release

WORKDIR /src

COPY ["Coder.ScriptWorkflow.WebApi/Coder.ScriptWorkflow.WebApi.csproj", "Coder.ScriptWorkflow.WebApi/"]
COPY ["Coder.ScriptWorkflow.Abstractions/Coder.ScriptWorkflow.Abstractions.csproj", "Coder.ScriptWorkflow.Abstractions/"]
COPY ["Coder.ScriptWorkflow.Core/Coder.ScriptWorkflow.Core.csproj", "Coder.ScriptWorkflow.Core/"]
COPY ["Coder.ScriptWorkflow.Migrations.DM/Coder.ScriptWorkflow.Migrations.DM.csproj", "Coder.ScriptWorkflow.Migrations.DM/"]
COPY ["Coder.ScriptWorkflow.Stores.Ef/Coder.ScriptWorkflow.Stores.Ef.csproj", "Coder.ScriptWorkflow.Stores.Ef/"]
COPY ["Coder.ScriptWorkflow.Migrations.Mssql/Coder.ScriptWorkflow.Migrations.Mssql.csproj", "Coder.ScriptWorkflow.Migrations.Mssql/"]
COPY ["Coder.ScriptWorkflow.Migrations.Mysql/Coder.ScriptWorkflow.Migrations.Mysql.csproj", "Coder.ScriptWorkflow.Migrations.Mysql/"]
COPY ["Coder.ScriptWorkflow.Migrations.Sqlite/Coder.ScriptWorkflow.Migrations.Sqlite.csproj", "Coder.ScriptWorkflow.Migrations.Sqlite/"]
COPY ["Coder.ScriptWorkflow.Performers.CoderMembers/Coder.ScriptWorkflow.Performers.CoderMembers.csproj", "Coder.ScriptWorkflow.Performers.CoderMembers/"]
COPY ["Coder.VBenMenu.Interceptor/Coder.VBenMenu.Interceptor.csproj", "Coder.VBenMenu.Interceptor/"]
COPY ["Coder.VBenNotify.Interceptor/Coder.VBenNotify.Interceptor.csproj", "Coder.VBenNotify.Interceptor/"]
COPY ["Plugins/Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient/Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient.csproj", "Plugins/Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient/"]
COPY ["Plugins/ScriptWorkflow.Scripts.Plugin.JsHttpClient/Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient.csproj", "Plugins/ScriptWorkflow.Scripts.Plugin.JsHttpClient/"]
COPY ["Plugins/ScriptWorkflow.Scripts.Plugin.Member/Coder.ScriptWorkflow.Scripts.Plugin.Member.csproj", "Plugins/ScriptWorkflow.Scripts.Plugin.Member/"]
COPY ["Plugins/ScriptWorkflow.Scripts.Plugin.MySql/Coder.ScriptWorkflow.Scripts.Plugin.MySql.csproj", "Plugins/ScriptWorkflow.Scripts.Plugin.MySql/"]
COPY ["nuget.config","nuget.config"]
RUN dotnet restore "./Coder.ScriptWorkflow.WebApi/Coder.ScriptWorkflow.WebApi.csproj"   --configfile="./nuget.config"
COPY . .
WORKDIR "/src/Coder.ScriptWorkflow.WebApi"
RUN dotnet build "./Coder.ScriptWorkflow.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/build

# 此阶段用于发布要复制到最终阶段的服务项目
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Coder.ScriptWorkflow.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# 此阶段在生产中使用，或在常规模式下从 VS 运行时使用(在不使用调试配置时为默认值)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Coder.ScriptWorkflow.WebApi.dll"]