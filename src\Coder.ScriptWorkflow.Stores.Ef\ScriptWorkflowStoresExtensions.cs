﻿using Coder.ScriptWorkflow.Mapping;
using Coder.ScriptWorkflow.Mapping.Assigns;
using Coder.ScriptWorkflow.Mapping.WorkProcessPermissions;
using Coder.ScriptWorkflow.Mapping.WorkTaskCommands;
using Coder.ScriptWorkflow.NumberGenerators;
using Coder.ScriptWorkflow.Stores;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public static class ScriptWorkflowStoresExtensions
{
    /// <summary>
    /// </summary>
    /// <typeparam name="TDbContext"></typeparam>
    /// <param name="configAction"></param>
    /// <returns></returns>
    public static WorkflowOptions AddEfStores<TDbContext>(this WorkflowOptions configAction)
        where TDbContext : DbContext
    {
        configAction.Services
            .AddTransient<ITransactionFactory, TransactionFactory<TDbContext>>()
            .AddScoped<IWorkProcessStore, WorkProcessStore<TDbContext>>()
            .AddScoped<IWorkActivityStore, WorkActivityStore<TDbContext>>()
            .AddScoped<IProcessInstanceStore, ProcessInstanceStore<TDbContext>>()
            .AddScoped<IWorkTaskStore, WorkTaskStore<TDbContext>>()
            .AddScoped<IAssignStore, AssignStore<TDbContext>>()
            .AddScoped<IGlobalScriptStore, GlobalScriptStore<TDbContext>>()
            .AddScoped<IGlobalVariableStore,GlobalVariableStore<TDbContext>>()
            .AddScoped<INodeStore, NodeStore<TDbContext>>()
            .AddScoped<ILoggerStore, LoggerStore<TDbContext>>()
            .AddScoped<ITagStore, TagStore<TDbContext>>()
            .AddScoped<IFileStoreQuery, FileStoreQueryStore>()
            .AddScoped<IProcessInstanceDistributionStore, ProcessInstanceDistributionStore<TDbContext>>()
            .AddScoped<IWorkProcessPermissionStore, WorkProcessPermissionStore<TDbContext>>()
            .AddScoped<IUserSettingStore, UserSettingStore<TDbContext>>();
        return configAction;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="prefix">表明前序，默认 </param>
    /// <param name="lengthOfEveryDay">每天的工作流对应的前序</param>
    /// <param name="isSqlite"></param>
    /// <returns></returns>
    public static ModelBuilder AddEfModels(this ModelBuilder builder, string prefix = "swf",
        int lengthOfEveryDay = 6,
        bool isSqlite = false)
    {
        //节点
        builder.ApplyConfiguration(new NodeMapping(prefix));
        builder.ApplyConfiguration(new StartNodeMapping());
        builder.ApplyConfiguration(new EndNodeMapping());


        //定义
        builder.ApplyConfiguration(new WorkProcessMapping(prefix, isSqlite));
        builder.ApplyConfiguration(new WorkProcessPermissionMapping(prefix));
        builder.ApplyConfiguration(new PermissionPerformerMapping(prefix));
        builder.ApplyConfiguration(new WorkTaskMapping());

        //WorkTask and WorkProcessTask scripts
        builder.ApplyConfiguration(new ScriptDefinedMapping(prefix));
        //WorkTask Commands
        builder.ApplyConfiguration(new WorkTaskCommandMapping(prefix));
        builder.ApplyConfiguration(new WorkTaskCommandScriptMapping());
        builder.ApplyConfiguration(new PreviousWorkTaskCommandMapping());

        //实例
        builder.ApplyConfiguration(new ProcessInstanceMapping(prefix, new NumberFrom20200101Generator()));

        builder.ApplyConfiguration(new ProcessInstanceTagMapping(prefix));
        builder.ApplyConfiguration(new ProcessInstanceDistributionMapping(prefix, isSqlite));
        builder.ApplyConfiguration(new TagMapping(prefix));
        builder.ApplyConfiguration(new WorkActivityMapping(prefix));


        builder.ApplyConfiguration(new AssignMapping(prefix));
        builder.ApplyConfiguration(new AssignScriptMapping());
        builder.ApplyConfiguration(new UsersAssignerMapping());


        //connector inherit node
        builder.ApplyConfiguration(new DecisionMapping());
        builder.ApplyConfiguration(new ScriptDecisionMapping());
        builder.ApplyConfiguration(new ConditionSettingMapping(prefix));
        builder.ApplyConfiguration(new ConditionDecisionMapping());

        //日志
        builder.ApplyConfiguration(new WorkflowLogMapping(prefix));
        //附件
        builder.ApplyConfiguration(new FileAttachMapping(prefix));

        //全局脚本
        builder.ApplyConfiguration(new GlobalScriptItemMapping(prefix));
        builder.ApplyConfiguration(new GlobalVariableMapping(prefix));

        builder.ApplyConfiguration(new SwfSettingMapping(prefix));
        builder.ApplyConfiguration(new SwfMakeTagsSettingMapping());

        return builder;
    }
}