<?xml version="1.0"?>

<doc>
	<assembly>
		<name>Coder.ScriptWorkflow.WebApi</name>
	</assembly>
	<members>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController">
			<summary>
				工作流定义接口
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.#ctor(Coder.ScriptWorkflow.WorkflowDefinedManager,Coder.ScriptWorkflow.Stores.IWorkProcessPermissionStore)">
			<summary>
			</summary>
			<param name="workflowPermissionManager"></param>
			<param name="workProcessPermissionStore"></param>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.Submit(Coder.ScriptWorkflow.ViewModels.Defined.WorkProcessSubmit)">
			<summary>
				保存工作流程定义对象
			</summary>
			<param name="submit"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.GetByName(System.String,Coder.ScriptWorkflow.Stores.IWorkProcessStore)">
			<summary>
				通过工作流名称获取工作流生效最新的定义。
			</summary>
			<param name="name">工作流名称</param>
			<param name="workProcessStore">工作流数据访问</param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.GetById(System.Int32,Coder.ScriptWorkflow.Stores.IWorkProcessStore)">
			<summary>
				通过工作流ID称获取工作流定义。
			</summary>
			<param name="id"></param>
			<param name="workProcessStore">workProcessStore</param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.Delete(System.Int32)">
			<summary>
				删除一个工作流定义
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.ListByName(System.String,Coder.ScriptWorkflow.Stores.IWorkProcessStore)">
			<summary>
				列出一个工作下的多个版本
			</summary>
			<param name="name"></param>
			<param name="workProcessStore"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.ListDistinct(Coder.ScriptWorkflow.Stores.IWorkProcessStore)">
			<summary>
			</summary>
			<param name="workProcessStore"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.Publish(System.Int32)">
			<summary>
				开启一个工作定义。创建新的工作流实例的时候，会自动采用
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.Disable(System.Int32)">
			<summary>
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.ExistWorkProcessName(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
			<summary>
			</summary>
			<param name="name"></param>
			<param name="version"></param>
			<param name="notIncludeId"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.ExistTask(System.String,System.Int32,System.Nullable{System.Int32})">
			<summary>
			</summary>
			<param name="name"></param>
			<param name="workProcessId"></param>
			<param name="notIncludeId"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.CheckVersion(System.Int32,System.Int32)">
			<summary>
				检查version是否存在。
			</summary>
			<param name="id"></param>
			<param name="version"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.ListForPage(Coder.ScriptWorkflow.ViewModels.WorkProcessListSearcher,Coder.ScriptWorkflow.Stores.IWorkProcessStore)">
			<summary>
				流程定义分页查询
			</summary>
			<param name="search"></param>
			<param name="workProcessStore"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.DefinedController.CountForPage(Coder.ScriptWorkflow.ViewModels.WorkProcessListSearcher,Coder.ScriptWorkflow.Stores.IWorkProcessStore)">
			<summary>
				流程定义数量查询
			</summary>
			<param name="search"></param>
			<param name="workProcessStore"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.Form">
			<summary>

			</summary>
		</member>
		<member name="P:Coder.ScriptWorkflow.WebApi.Controllers.Form.String">
			<summary>

			</summary>
		</member>
		<member name="P:Coder.ScriptWorkflow.WebApi.Controllers.Form.Int32">
			<summary>

			</summary>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.PerformerController">
			<summary>
				工作流定义接口
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PerformerController.#ctor(Coder.ScriptWorkflow.Performers.PerformerManager)">
			<summary>
				/
			</summary>
			<param name="performerManager"></param>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.PerformerController.GetUsers(System.String,System.String)">
			<summary>

			</summary>
			<param name="performerType"></param>
			<param name="key"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController">
			<summary>
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.#ctor(Coder.ScriptWorkflow.Stores.IWorkProcessPermissionStore,Coder.Members.Clients.IUserClient,Coder.Members.Clients.IRoleClient)">
			<summary>
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.List(Coder.ScriptWorkflow.ViewModels.Permissions.WorkProcessPermissionSearch)">
			<summary>
			</summary>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.CountForPage(Coder.ScriptWorkflow.ViewModels.Permissions.WorkProcessPermissionSearch)">
			<summary>
			</summary>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.Update(Coder.ScriptWorkflow.ViewModels.Permissions.WorkProcessPermissionSubmit)">
			<summary>
			</summary>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.PerformerList(Coder.ScriptWorkflow.PerformerType)">
			<summary>
			</summary>
			<param name="performerType"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.PerformerList(Coder.ScriptWorkflow.PerformerType,System.String)">
			<summary>
			</summary>
			<param name="performerType"></param>
			<param name="name"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.VerifyList(Coder.ScriptWorkflow.PerformerType,System.String)">
			<summary>
			</summary>
			<param name="performerType"></param>
			<param name="code"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.Delete(System.Int32)">
			<summary>
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.PermissionController.Get(System.Int32)">
			<summary>
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.PluginController">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.PluginController.GetJSDefined(System.IServiceProvider)">
			<summary>
				获取js的提示方法。
			</summary>
			<param name="sp"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.PluginController.GetWorkProcess(System.Int32,Coder.ScriptWorkflow.Stores.IWorkProcessStore,System.IServiceProvider)">
			<summary>

			</summary>
			<param name="id"></param>
			<param name="workProcessStore"></param>
			<param name="sp"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController">
			<summary>
			</summary>
		</member>
		<member name="F:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController._processInstanceStore">
			private readonly HttpSimpleFileManager _fileManager;
		</member>
		<member name="F:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.WorkflowManager">
			<summary>
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.#ctor(Coder.ScriptWorkflow.WorkflowManager,Coder.ScriptWorkflow.Stores.IWorkActivityStore,Coder.ScriptWorkflow.Stores.IProcessInstanceStore,Coder.Members.Clients.IUserClient,Coder.FileSystem.HttpClient.HttpSimpleFileManager)">
			<summary>
			</summary>
			<param name="workflowManager"></param>
			<param name="workActivityStore"></param>
			<param name="processInstanceStore"></param>
			<param name="userClient"></param>
			<param name="fileManager"></param>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Get(System.Int32)">
			<summary>
				获取工作流实例
			</summary>
			<param name="id">工作流实例id</param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.GetByName(System.String)">
			<summary>
			</summary>
			<param name="name"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.List(Coder.ScriptWorkflow.ViewModels.ProcessInstanceSearcher)">
			<summary>
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.ExportProcessInstance(Coder.ScriptWorkflow.ViewModels.ProcessInstanceSearcher)">
			<summary>
				导出我的工单
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Count(Coder.ScriptWorkflow.ViewModels.ProcessInstanceSearcher)">
			<summary>
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.ExportProcessInstanceDetail(Coder.ScriptWorkflow.ViewModels.ProcessInstanceSearcher)">
			<summary>
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Get(System.String)">
			<summary>
			</summary>
			<param name="number"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.GetByWorkActionResult(System.Int32)">
			<summary>
				获取工作活动
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.ListWorkActivity(Coder.ScriptWorkflow.ViewModels.WorkActivitySearcher)">
			<summary>
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.CountWorkActivity(Coder.ScriptWorkflow.ViewModels.WorkActivitySearcher)">
			<summary>
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Create(Coder.ScriptWorkflow.ViewModels.CreateProcessInstanceSubmit)">
			<summary>
			</summary>
			<param name="submit"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Resolve(System.Int32,Coder.ScriptWorkflow.ViewModels.WorkflowResolveSubmit)">
			<summary>
				解决一个工作活动
			</summary>
			<param name="id"></param>
			<param name="model"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Suspend(System.Int32,Coder.ScriptWorkflow.ViewModels.SuspendCommentSubmit)">
			<summary>
				挂起一个工作流实例
			</summary>
			<param name="processInstanceId"></param>
			<param name="commentSubmit"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Resume(System.Int32)">
			<summary>
				恢复工作流实例
			</summary>
			<param name="processInstanceId"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.List(System.Int32)">
			<summary>
				列出一个工作活动
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Cancel(System.Int32)">
			<summary>
				取消工作活动
			</summary>
			<param name="processInstanceId"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Accept(System.Int32,System.String)">
			<summary>
				接受工作活动
			</summary>
			<param name="workActivityId"></param>
			<param name="user"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Abandon(System.Int32)">
			<summary>
				放弃工作活动
			</summary>
			<param name="workActivityId"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Start(System.Int32)">
			<summary>
				启动一个工作流实例
			</summary>
			<param name="processInstanceId"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.FormSave(System.Int32,Coder.ScriptWorkflow.ViewModels.ProcessInstanceForm)">
			<summary>
			</summary>
			<param name="id"></param>
			<param name="jobj"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.SaveSubject(System.Int32,Coder.ScriptWorkflow.ViewModels.ProcessInstanceSubject,coder.workbench.service.Core.Clients.IWorkbenchClient)">
			<summary>
			</summary>
			<param name="processInstanceId"></param>
			<param name="obj"></param>
			<param name="client"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.GetCommands(System.Int32,Coder.ScriptWorkflow.Stores.IWorkActivityStore)">
			<summary>
				获取命令列表
			</summary>
			<param name="workActivityId"></param>
			<param name="store"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.Upload(Microsoft.AspNetCore.Http.IFormFile,System.Int32)">
			<summary>
				上传附件
			</summary>
			<param name="file"></param>
			<param name="processInstanceId"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.DeleteFile(System.Int32,System.Int32)">
			<summary>
				删除附件
			</summary>
			<param name="id"></param>
			<param name="processInstanceId"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.TryResolve(System.Int32,Coder.ScriptWorkflow.ViewModels.NextWorkTaskUserSubmit)">
			<summary>
			</summary>
			<param name="workActivityId"></param>
			<param name="model"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.GetFilesByProcessInstanceId(System.Int32,Coder.ScriptWorkflow.Stores.IProcessInstanceStore)">
			<summary>
				根据流程实例ID获取上传的附件
			</summary>
			<param name="processInstanceId"></param>
			<param name="store"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowController.GetFileById(System.String)">
			<summary>
				根据文件ID获取文件
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowLogController">
			<summary>
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowLogController.#ctor(Coder.ScriptWorkflow.Stores.ILoggerStore)">
			<summary>
			</summary>
			<param name="loggerStore"></param>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowLogController.List(Coder.ScriptWorkflow.ViewModels.WorkflowLogSearcher)">
			<summary>
				列出相关
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowLogController.Count(Coder.ScriptWorkflow.ViewModels.WorkflowLogSearcher)">
			<summary>
				log的·1数目
			</summary>
			<param name="searcher"></param>
			<returns></returns>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Controllers.WorkflowLogController.Get(System.Int32)">
			<summary>
			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Data.Seed">
			<summary>

			</summary>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Data.Seed.Init(System.IServiceProvider,System.Boolean)">
			<summary>

			</summary>
			<param name="sp"></param>
			<param name="dropAndCreate">tru 用于测试，每次调用都会drop table</param>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Filtters.CoderBadRequest">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Filtters.CoderBadRequest.#ctor(Microsoft.AspNetCore.Mvc.ActionContext)">
			<summary>

			</summary>
			<param name="context"></param>
		</member>
		<member name="P:Coder.ScriptWorkflow.WebApi.Filtters.CoderBadRequest.Errors">
			<summary>

			</summary>
		</member>
		<member name="P:Coder.ScriptWorkflow.WebApi.Filtters.CoderBadRequest.Message">
			<summary>

			</summary>
		</member>
		<member name="P:Coder.ScriptWorkflow.WebApi.Filtters.CoderBadRequest.Success">
			<summary>

			</summary>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute">
			<summary>

			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute.#ctor(Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider,Microsoft.Extensions.Logging.ILogger{Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute})">
			<summary>

			</summary>
			<param name="hostingEnvironment"></param>
			<param name="modelMetadataProvider"></param>
			<param name="logger"></param>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Filtters.CustomExceptionFilterAttribute.OnException(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
			<summary>

			</summary>
			<param name="context"></param>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Program">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Program.Main(System.String[])">
			<summary>
			</summary>
			<param name="args"></param>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Program.CreateHostBuilder(System.String[])">
			<summary>
			</summary>
			<param name="args"></param>
			<returns></returns>
		</member>
		<member name="T:Coder.ScriptWorkflow.WebApi.Startup">
			<summary>
			</summary>
		</member>
		<member name="M:Coder.ScriptWorkflow.WebApi.Startup.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
			<summary>
			</summary>
			<param name="configuration"></param>
		</member>
		<member name="P:Coder.ScriptWorkflow.WebApi.Startup.Configuration">
			<summary>
			</summary>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
			</summary>
			<param name="services"></param>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Startup.OnConfigDbContext(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
			</summary>
			<param name="services"></param>
			<exception cref="T:System.Configuration.ConfigurationErrorsException"></exception>
			<exception cref="T:System.Exception"></exception>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Startup.ConsulSetup(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
			<summary>
			</summary>
			<param name="services"></param>
		</member>
		<member
			name="M:Coder.ScriptWorkflow.WebApi.Startup.Configure(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
			<summary>
			</summary>
			<param name="app"></param>
			<param name="env"></param>
		</member>
		<member name="T:ScriptWorkflow.WebApi.Controllers.JsHttpClientController">
			<summary>
				Debug才有
			</summary>
		</member>
		<member
			name="M:ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostForm(Coder.ScriptWorkflow.WebApi.Controllers.Form)">
			<summary>

			</summary>
			<param name="form"></param>
			<returns></returns>
		</member>
		<member name="M:ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PostJSON(Newtonsoft.Json.Linq.JObject)">
			<summary>

			</summary>
			<param name="form"></param>
			<returns></returns>
		</member>
		<member name="M:ScriptWorkflow.WebApi.Controllers.JsHttpClientController.PutJson(Newtonsoft.Json.Linq.JObject)">
			<summary>

			</summary>
			<param name="form"></param>
			<returns></returns>
		</member>
		<member
			name="M:ScriptWorkflow.WebApi.Controllers.JsHttpClientController.Query(Coder.ScriptWorkflow.WebApi.Controllers.Form)">
			<summary>

			</summary>
			<param name="form"></param>
			<returns></returns>
		</member>
		<member name="M:ScriptWorkflow.WebApi.Controllers.JsHttpClientController.JsonDelete(System.Int32)">
			<summary>

			</summary>
			<param name="id"></param>
			<returns></returns>
		</member>
		<member name="M:ScriptWorkflow.WebApi.Controllers.JsHttpClientController.ListJson">
			<summary>

			</summary>
			<returns></returns>
		</member>
	</members>
</doc>