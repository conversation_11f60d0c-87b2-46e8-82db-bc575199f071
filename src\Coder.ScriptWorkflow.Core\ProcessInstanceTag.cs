﻿namespace Coder.ScriptWorkflow;

/// <summary>
///     实例Tag
/// </summary>
public class ProcessInstanceTag
{
    /// <summary>
    ///     id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public virtual Tag Tag { get; set; }

    /// <summary>
    ///     颜色
    /// </summary>
    public string Color { get; set; }

    /// <summary>
    ///     工作实例
    /// </summary>
    public virtual ProcessInstance ProcessInstance { get; set; }

    /// <summary>
    ///     是否能够删除。
    /// </summary>
    public bool CanDelete { get; set; } = false;
}