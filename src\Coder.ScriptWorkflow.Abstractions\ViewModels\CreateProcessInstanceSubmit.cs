﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     创建工作流实例（工单）
/// </summary>
public class CreateProcessInstanceSubmit
{
    /// <summary>
    ///     主题
    /// </summary>

    public string Subject { get; set; }

    /// <summary>
    /// </summary>
    public string Form { get; set; }

    /// <summary>
    /// </summary>
    [Required]
    public string WorkProcessName { get; set; }

    /// <summary>
    /// </summary>
    public int? WorkProcessId { get; set; }

    /// <summary>
    ///     是否自动启动
    /// </summary>
    public bool Start { get; set; }

    /// <summary>
    /// </summary>
    public IList<FileAttachSubmit> Files { get; set; }

    /// <summary>
    ///     创建用户。只有在调试模式下，这个提交这个用户才有用，否则会采用当前登录用户
    /// </summary>
    public string CreateUser { get; set; }

    /// <summary>
    ///     审批优先级别
    /// </summary>
    public Priority Priority { get; set; } = Priority.Normal;
    /// <summary>
    /// 是否为调试
    /// </summary>
    public bool? IsDebug { get; set; }
}

