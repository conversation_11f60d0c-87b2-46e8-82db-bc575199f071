﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.ScriptWorkflow.Migrations.Mssql.Migrations
{
    /// <inheritdoc />
    public partial class init3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "swf_assigner",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AssignScopeType = table.Column<int>(type: "int", nullable: false),
                    Discriminator = table.Column<string>(type: "nvarchar(21)", maxLength: 21, nullable: false),
                    Script = table.Column<string>(type: "nvarchar(3000)", maxLength: 3000, nullable: true),
                    Performers = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_assigner", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "swf_global_variable",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Variable = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    Env = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_global_variable", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "swf_globalScript",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Script = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_globalScript", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "swf_PI_Tags",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Number = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_PI_Tags", x => x.Id);
                },
                comment: "工作流程标记");

            migrationBuilder.CreateTable(
                name: "swf_scripts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Script = table.Column<string>(type: "nvarchar(max)", maxLength: 6000, nullable: true),
                    Discriminator = table.Column<string>(type: "nvarchar(34)", maxLength: 34, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_scripts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "swf_swf_setting",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false, comment: "id")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Script = table.Column<string>(type: "text", nullable: true, comment: "脚本"),
                    Discriminator = table.Column<string>(type: "nvarchar(21)", maxLength: 21, nullable: false),
                    Plugins = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_swf_setting", x => x.Id);
                },
                comment: "工作流实例");

            migrationBuilder.CreateTable(
                name: "swf_workflowLog",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LogLevel = table.Column<int>(type: "int", nullable: false),
                    ProcessInstanceId = table.Column<int>(type: "int", nullable: false, comment: "工单id"),
                    ProcessInstanceNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "工单号"),
                    Version = table.Column<int>(type: "int", nullable: false, comment: "工作流定义版本"),
                    WorkProcessName = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true, comment: "工作流名称"),
                    NodeName = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true, comment: "节点名称"),
                    WorkActivityId = table.Column<int>(type: "int", nullable: true),
                    Type = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: false),
                    Content = table.Column<string>(type: "text", nullable: true),
                    PluginName = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    CreateTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_workflowLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "swf_workProcessPermission",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProcessName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ManageRole = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_workProcessPermission", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "swf_workProcess",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Icon = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "图标"),
                    Comment = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "备注"),
                    Abbr = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "简称"),
                    LogLevel = table.Column<int>(type: "int", nullable: false),
                    FormDesign = table.Column<string>(type: "text", nullable: true),
                    FormManageDesign = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UpdateTimeOffset = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    Version = table.Column<int>(type: "int", nullable: false),
                    OnCompleteId = table.Column<int>(type: "int", nullable: true),
                    OnCancelId = table.Column<int>(type: "int", nullable: true),
                    OnStartId = table.Column<int>(type: "int", nullable: true),
                    Enable = table.Column<bool>(type: "bit", nullable: false),
                    Prefix = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Configurations = table.Column<string>(type: "text", nullable: true),
                    GlobalScript = table.Column<string>(type: "text", nullable: true),
                    FormTypeScriptDefined = table.Column<string>(type: "text", nullable: true),
                    Plugins = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Creator = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CanBeDeleteWorkActivityCount = table.Column<int>(type: "int", nullable: false),
                    Group = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_workProcess", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_workProcess_swf_scripts_OnCancelId",
                        column: x => x.OnCancelId,
                        principalTable: "swf_scripts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_workProcess_swf_scripts_OnCompleteId",
                        column: x => x.OnCompleteId,
                        principalTable: "swf_scripts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_workProcess_swf_scripts_OnStartId",
                        column: x => x.OnStartId,
                        principalTable: "swf_scripts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "swf_PermissionPerformer",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    WorkProcessPermissionId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_PermissionPerformer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_PermissionPerformer_swf_workProcessPermission_WorkProcessPermissionId",
                        column: x => x.WorkProcessPermissionId,
                        principalTable: "swf_workProcessPermission",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "swf_node",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    Auto = table.Column<bool>(type: "bit", nullable: false),
                    NextNodeId = table.Column<int>(type: "int", nullable: true),
                    WorkProcessId = table.Column<int>(type: "int", nullable: true),
                    Position = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Discriminator = table.Column<string>(type: "nvarchar(21)", maxLength: 21, nullable: false),
                    MatchDescription = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Script = table.Column<string>(type: "text", nullable: true, comment: "执行脚本"),
                    ElseNodeId = table.Column<int>(type: "int", nullable: true),
                    ElseDescription = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    ConditionDecision_Script = table.Column<string>(type: "text", nullable: true, comment: "执行脚本"),
                    NextTaskPerformers = table.Column<int>(type: "int", nullable: true),
                    AssignerId = table.Column<int>(type: "int", nullable: true),
                    CanGiveUp = table.Column<bool>(type: "bit", nullable: true),
                    FormDesign = table.Column<string>(type: "text", nullable: true),
                    WorkActivityCompleteScriptId = table.Column<int>(type: "int", nullable: true),
                    WorkTaskCompleteScriptId = table.Column<int>(type: "int", nullable: true),
                    WorkTaskStartScriptId = table.Column<int>(type: "int", nullable: true),
                    SuggestionComment = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ExtendInfo = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_node", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_node_swf_assigner_AssignerId",
                        column: x => x.AssignerId,
                        principalTable: "swf_assigner",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_node_swf_node_ElseNodeId",
                        column: x => x.ElseNodeId,
                        principalTable: "swf_node",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_node_swf_node_NextNodeId",
                        column: x => x.NextNodeId,
                        principalTable: "swf_node",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_node_swf_scripts_WorkActivityCompleteScriptId",
                        column: x => x.WorkActivityCompleteScriptId,
                        principalTable: "swf_scripts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_node_swf_scripts_WorkTaskCompleteScriptId",
                        column: x => x.WorkTaskCompleteScriptId,
                        principalTable: "swf_scripts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_node_swf_scripts_WorkTaskStartScriptId",
                        column: x => x.WorkTaskStartScriptId,
                        principalTable: "swf_scripts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_node_swf_workProcess_WorkProcessId",
                        column: x => x.WorkProcessId,
                        principalTable: "swf_workProcess",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "swf_processInstance",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IsDebug = table.Column<bool>(type: "bit", nullable: false, comment: "是否处于debug模式"),
                    IsDelete = table.Column<bool>(type: "bit", nullable: false, comment: "是否已经删除"),
                    WorkActivityCount = table.Column<int>(type: "int", nullable: false),
                    WorkProcessId = table.Column<int>(type: "int", nullable: true),
                    Priority = table.Column<int>(type: "int", nullable: false, defaultValue: 100),
                    Subject = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Creator = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    FinishTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    StartTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Number = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    SuspendTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SuspendComment = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    Form = table.Column<string>(type: "json", nullable: true),
                    CreatorName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_processInstance", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_processInstance_swf_workProcess_WorkProcessId",
                        column: x => x.WorkProcessId,
                        principalTable: "swf_workProcess",
                        principalColumn: "Id");
                },
                comment: "工作流实例");

            migrationBuilder.CreateTable(
                name: "swf_condition_decision",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NodeId = table.Column<int>(type: "int", nullable: true),
                    MatchValue = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "匹配的字符串"),
                    Description = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    ConditionDecisionId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_condition_decision", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_condition_decision_swf_node_ConditionDecisionId",
                        column: x => x.ConditionDecisionId,
                        principalTable: "swf_node",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_condition_decision_swf_node_NodeId",
                        column: x => x.NodeId,
                        principalTable: "swf_node",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "swf_WorkTaskCommand",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Order = table.Column<int>(type: "int", nullable: false),
                    Discriminator = table.Column<string>(type: "nvarchar(34)", maxLength: 34, nullable: false),
                    WorkTaskId = table.Column<int>(type: "int", nullable: true),
                    Script = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_WorkTaskCommand", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_WorkTaskCommand_swf_node_WorkTaskId",
                        column: x => x.WorkTaskId,
                        principalTable: "swf_node",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "swf_files",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    FileId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FileType = table.Column<int>(type: "int", nullable: false),
                    CreateUser = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ProcessInstanceId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_files", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_files_swf_processInstance_ProcessInstanceId",
                        column: x => x.ProcessInstanceId,
                        principalTable: "swf_processInstance",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "swf_PI_Distribution",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ReadTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreateTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    UserName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    UserRealName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    ProcessInstanceId = table.Column<int>(type: "int", nullable: true),
                    HasRead = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_PI_Distribution", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId",
                        column: x => x.ProcessInstanceId,
                        principalTable: "swf_processInstance",
                        principalColumn: "Id");
                },
                comment: "工作流程分发记录");

            migrationBuilder.CreateTable(
                name: "swf_PI_ProcessInstanceTags",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TagId = table.Column<int>(type: "int", nullable: true),
                    Color = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    ProcessInstanceId = table.Column<int>(type: "int", nullable: true),
                    CanDelete = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_PI_ProcessInstanceTags", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_PI_ProcessInstanceTags_swf_PI_Tags_TagId",
                        column: x => x.TagId,
                        principalTable: "swf_PI_Tags",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_PI_ProcessInstanceTags_swf_processInstance_ProcessInstanceId",
                        column: x => x.ProcessInstanceId,
                        principalTable: "swf_processInstance",
                        principalColumn: "Id");
                },
                comment: "工作流程标记");

            migrationBuilder.CreateTable(
                name: "swf_WorkActivity",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Priority = table.Column<int>(type: "int", nullable: false, defaultValue: 100),
                    WorkTaskId = table.Column<int>(type: "int", nullable: true),
                    AssignPerformers = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    AssignTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TaskCreatingGroup = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    DisposeUser = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DisposeUserName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DisposeTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Command = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    TimeSpan = table.Column<DateTime>(type: "datetime2", rowVersion: true, nullable: true),
                    ProcessInstanceId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_WorkActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_WorkActivity_swf_node_WorkTaskId",
                        column: x => x.WorkTaskId,
                        principalTable: "swf_node",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_swf_WorkActivity_swf_processInstance_ProcessInstanceId",
                        column: x => x.ProcessInstanceId,
                        principalTable: "swf_processInstance",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_swf_condition_decision_ConditionDecisionId",
                table: "swf_condition_decision",
                column: "ConditionDecisionId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_condition_decision_NodeId",
                table: "swf_condition_decision",
                column: "NodeId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_files_ProcessInstanceId",
                table: "swf_files",
                column: "ProcessInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_AssignerId",
                table: "swf_node",
                column: "AssignerId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_ElseNodeId",
                table: "swf_node",
                column: "ElseNodeId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_NextNodeId",
                table: "swf_node",
                column: "NextNodeId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_WorkActivityCompleteScriptId",
                table: "swf_node",
                column: "WorkActivityCompleteScriptId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_WorkProcessId",
                table: "swf_node",
                column: "WorkProcessId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_WorkTaskCompleteScriptId",
                table: "swf_node",
                column: "WorkTaskCompleteScriptId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_WorkTaskStartScriptId",
                table: "swf_node",
                column: "WorkTaskStartScriptId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_PermissionPerformer_WorkProcessPermissionId",
                table: "swf_PermissionPerformer",
                column: "WorkProcessPermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_PI_Distribution_ProcessInstanceId",
                table: "swf_PI_Distribution",
                column: "ProcessInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_PI_ProcessInstanceTags_ProcessInstanceId",
                table: "swf_PI_ProcessInstanceTags",
                column: "ProcessInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_PI_ProcessInstanceTags_TagId",
                table: "swf_PI_ProcessInstanceTags",
                column: "TagId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_processInstance_Number",
                table: "swf_processInstance",
                column: "Number");

            migrationBuilder.CreateIndex(
                name: "IX_swf_processInstance_WorkProcessId",
                table: "swf_processInstance",
                column: "WorkProcessId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_WorkActivity_ProcessInstanceId",
                table: "swf_WorkActivity",
                column: "ProcessInstanceId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_WorkActivity_WorkTaskId",
                table: "swf_WorkActivity",
                column: "WorkTaskId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_workflowLog_ProcessInstanceNumber",
                table: "swf_workflowLog",
                column: "ProcessInstanceNumber");

            migrationBuilder.CreateIndex(
                name: "IX_swf_workProcess_Name_Version",
                table: "swf_workProcess",
                columns: new[] { "Name", "Version" },
                unique: true,
                filter: "[Name] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_swf_workProcess_OnCancelId",
                table: "swf_workProcess",
                column: "OnCancelId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_workProcess_OnCompleteId",
                table: "swf_workProcess",
                column: "OnCompleteId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_workProcess_OnStartId",
                table: "swf_workProcess",
                column: "OnStartId");

            migrationBuilder.CreateIndex(
                name: "IX_swf_workProcessPermission_ProcessName",
                table: "swf_workProcessPermission",
                column: "ProcessName");

            migrationBuilder.CreateIndex(
                name: "IX_swf_WorkTaskCommand_WorkTaskId",
                table: "swf_WorkTaskCommand",
                column: "WorkTaskId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "swf_condition_decision");

            migrationBuilder.DropTable(
                name: "swf_files");

            migrationBuilder.DropTable(
                name: "swf_global_variable");

            migrationBuilder.DropTable(
                name: "swf_globalScript");

            migrationBuilder.DropTable(
                name: "swf_PermissionPerformer");

            migrationBuilder.DropTable(
                name: "swf_PI_Distribution");

            migrationBuilder.DropTable(
                name: "swf_PI_ProcessInstanceTags");

            migrationBuilder.DropTable(
                name: "swf_swf_setting");

            migrationBuilder.DropTable(
                name: "swf_WorkActivity");

            migrationBuilder.DropTable(
                name: "swf_workflowLog");

            migrationBuilder.DropTable(
                name: "swf_WorkTaskCommand");

            migrationBuilder.DropTable(
                name: "swf_workProcessPermission");

            migrationBuilder.DropTable(
                name: "swf_PI_Tags");

            migrationBuilder.DropTable(
                name: "swf_processInstance");

            migrationBuilder.DropTable(
                name: "swf_node");

            migrationBuilder.DropTable(
                name: "swf_assigner");

            migrationBuilder.DropTable(
                name: "swf_workProcess");

            migrationBuilder.DropTable(
                name: "swf_scripts");
        }
    }
}
