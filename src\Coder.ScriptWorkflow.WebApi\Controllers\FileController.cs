﻿using System;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Services;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流文件管理接口
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize(Roles = "admin")]
public class FileController : Controller
{
    private readonly ProcessInstanceFileService _fileService;
    private readonly ILogger<FileController> _logger;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="fileService">流程实例文件服务</param>
    /// <param name="logger">日志记录器</param>
    public FileController(
        ProcessInstanceFileService fileService,
        ILogger<FileController> logger)
    {
        _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    ///     获取文件
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <param name="fileName">文件名</param>
    /// <param name="fileId">文件ID</param>
    /// <returns>文件内容</returns>
    [HttpGet("{processInstanceId}/{fileId}")]
    public async Task<IActionResult> Get([FromRoute] int processInstanceId, [FromQuery] string fileName,
        [FromRoute] string fileId)
    {
        var result = await _fileService.GetFileAsync(processInstanceId, fileId, User, fileName);

        if (!result.Success)
            return result.ErrorCode switch
            {
                400 => BadRequest(result.ErrorMessage),
                403 => Ok(new ProcessInstanceViewModel
                {
                    Success = true,
                    Message = result.ErrorMessage
                }.ToSuccess()),
                404 => NotFound(result.ErrorMessage),
                _ => StatusCode(result.ErrorCode, result.ErrorMessage)
            };

        var downloadInfo = result.Data;
        return File(downloadInfo.Stream, downloadInfo.ContentType, downloadInfo.FileName);
    }


    /// <summary>
    ///     删除文件
    /// </summary>
    /// <param name="fileId">文件ID</param>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{processInstanceId}/{fileId}")]
    public async Task<IActionResult> Delete([FromRoute] string fileId, [FromRoute] int processInstanceId)
    {
        var result = await _fileService.DeleteFileAsync(processInstanceId, fileId, User);

        if (!result.Success)
            return result.ErrorCode switch
            {
                400 => BadRequest(result.ErrorMessage),
                403 => Forbid(),
                404 => NotFound(result.ErrorMessage),
                _ => StatusCode(result.ErrorCode, result.ErrorMessage)
            };

        return Ok(new { code = 0, message = "请求成功", data = result.Data });
    }

    /// <summary>
    ///     获取附件列表
    /// </summary>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <returns>附件列表</returns>
    [HttpGet("list/{processInstanceId}")]
    public async Task<IActionResult> List([FromRoute] int processInstanceId)
    {
        var result = await _fileService.GetAttachmentListAsync(processInstanceId, User);

        if (!result.Success)
            return result.ErrorCode switch
            {
                400 => BadRequest(result.ErrorMessage),
                403 => Forbid(),
                404 => NotFound(result.ErrorMessage),
                _ => StatusCode(result.ErrorCode, result.ErrorMessage)
            };

        return Ok(result.Data);
    }

    /// <summary>
    ///     上传文件
    /// </summary>
    /// <param name="submit">上传请求</param>
    /// <param name="processInstanceId">流程实例ID</param>
    /// <returns>上传结果</returns>
    [HttpPost("upload/{processInstanceId:int}")]
    public async Task<IActionResult> Upload(IFormFile file, [FromRoute] int processInstanceId)
    {
        var submit = new UploadFileAttachment();
        submit.File = file;
        foreach (var key in Request.Form.Keys)
        {
            var value = Request.Form[key];
            submit.Claims.Add(new UploadFileAttachment.FileClaim
            {
                Name = key,
                Value = value
            });
        }

        var result = await _fileService.UploadFileAsync(processInstanceId, submit, User);

        if (!result.Success)
            return result.ErrorCode switch
            {
                400 => BadRequest(result.ErrorMessage),
                403 => Forbid(),
                404 => NotFound(result.ErrorMessage),
                _ => StatusCode(result.ErrorCode, result.ErrorMessage)
            };

        return Ok(result.Data.ToSuccess());
    }
}