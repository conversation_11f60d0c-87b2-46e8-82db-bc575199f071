#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base


WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
RUN dotnet nuget add source http://***********:8081/repository/npm-group/index.json  -n publish 
WORKDIR /src
COPY ["Coder.ScriptWorkflow.WebApi/Coder.ScriptWorkflow.WebApi.csproj", "Coder.ScriptWorkflow.WebApi/"]
COPY ["Coder.ScriptWorkflow.Abstractions/Coder.ScriptWorkflow.Abstractions.csproj", "Coder.ScriptWorkflow.Abstractions/"]
COPY ["Coder.ScriptWorkflow.Core/Coder.ScriptWorkflow.Core.csproj", "Coder.ScriptWorkflow.Core/"]
COPY ["Coder.ScriptWorkflow.Migrations.Mssql/Coder.ScriptWorkflow.Migrations.Mssql.csproj", "Coder.ScriptWorkflow.Migrations.Mssql/"]
COPY ["Coder.ScriptWorkflow.Stores.Ef/Coder.ScriptWorkflow.Stores.Ef.csproj", "Coder.ScriptWorkflow.Stores.Ef/"]
COPY ["Coder.ScriptWorkflow.Migrations.Mysql/Coder.ScriptWorkflow.Migrations.Mysql.csproj", "Coder.ScriptWorkflow.Migrations.Mysql/"]
COPY ["Coder.ScriptWorkflow.Migrations.Sqlite/Coder.ScriptWorkflow.Migrations.Sqlite.csproj", "Coder.ScriptWorkflow.Migrations.Sqlite/"]
COPY ["Coder.ScriptWorkflow.Performers.CoderMembers/Coder.ScriptWorkflow.Performers.CoderMembers.csproj", "Coder.ScriptWorkflow.Performers.CoderMembers/"]
COPY ["Coder.ScriptWorkflow.Scripts.Plugin.MigratorClient/Coder.ScriptWorkflow.Scripts.Plugin.MigratorClient.csproj", "Coder.ScriptWorkflow.Scripts.Plugin.MigratorClient/"]
COPY ["Coder.VBenMenu.Interceptor/Coder.VBenMenu.Interceptor.csproj", "Coder.VBenMenu.Interceptor/"]
COPY ["Interceptors/ScriptWorkflow.CoderWorkBench.Interceptor/Coder.ScriptWorkflow.CoderWorkBench.Interceptor.csproj", "Interceptors/ScriptWorkflow.CoderWorkBench.Interceptor/"]
COPY ["Plugins/Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient/Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient.csproj", "Plugins/Coder.ScriptWorkflow.Scripts.Plugin.JsOrgClient/"]
COPY ["Plugins/ScriptWorkflow.Scripts.Plugin.JsHttpClient/Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient.csproj", "Plugins/ScriptWorkflow.Scripts.Plugin.JsHttpClient/"]
COPY ["Plugins/Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient/Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient.csproj", "Plugins/Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient/"]
COPY ["Plugins/ScriptWorkflow.Scripts.Plugin.Member/Coder.ScriptWorkflow.Scripts.Plugin.Member.csproj", "Plugins/ScriptWorkflow.Scripts.Plugin.Member/"]
COPY ["Plugins/ScriptWorkflow.Scripts.Plugin.MySql/Coder.ScriptWorkflow.Scripts.Plugin.MySql.csproj", "Plugins/ScriptWorkflow.Scripts.Plugin.MySql/"]
RUN dotnet restore "Coder.ScriptWorkflow.WebApi/Coder.ScriptWorkflow.WebApi.csproj"
COPY . .
WORKDIR "/src/Coder.ScriptWorkflow.WebApi"
RUN dotnet build "Coder.ScriptWorkflow.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Coder.ScriptWorkflow.WebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Coder.ScriptWorkflow.WebApi.dll"]