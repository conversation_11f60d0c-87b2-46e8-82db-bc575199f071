﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using Coder.ScriptWorkflow.Stores;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.Logger;

/// <summary>
///     工作流流程日志系统。
/// </summary>
public class WorkflowLogManager : IDisposable
{
    private readonly Queue<WorkflowLog> _cache = new();
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// </summary>
    /// <param name="serviceProvider"></param>
    public WorkflowLogManager(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        Flush();
    }


    private StringBuilder MakeContent(Exception ex)
    {
        var sb = new StringBuilder();
        var i = 0;
        var current = ex;
        while (current != null)
        {
            sb.AppendLine(current.Message);
            sb.AppendLine(current.StackTrace);
            current = current.InnerException;
            i++;
            if (i > 5) break;
        }

        return sb;
    }


    /// <summary>
    ///     所有脚本错误。
    /// </summary>
    /// <param name="ex"></param>
    internal void LogError(JsRuntimeException ex)
    {
        var context = ex.Context;
        if (context.ProcessInstance.WorkProcess.LogLevel > LogLevel.Error)
            return;
        var sb = MakeContent(ex);

        var log = new WorkflowLog
        {
            NodeName = ex.NodeName,
            Content = sb.ToString(),
            LogLevel = LogLevel.Error,
            PluginName = ex.PluginName,
            Type = ex.PluginErrorStack != null ? WorkflowLogType.Plugin : WorkflowLogType.Script
        };

        if (context.CurrentWorkActivity != null)
            log.SetWorkActivity(context.CurrentWorkActivity);
        else
            log.SetProcessInstance(context.ProcessInstance);

        Log(log);
    }


    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="type"></param>
    /// <param name="ex"></param>
    /// <param name="nodeName">nodeName如果为空 会尝试 workflowContext.currentNode1获取 </param>
    public void LogError(IWorkflowContext workflowContext, WorkflowLogType type, Exception ex,
        [AllowNull] string nodeName = null)
    {
        var wlog = new WorkflowLog();
        if (workflowContext.CurrentWorkActivity != null)
            wlog.SetWorkActivity(workflowContext.CurrentWorkActivity);
        else
            wlog.SetProcessInstance(workflowContext.ProcessInstance);
        wlog.LogLevel = LogLevel.Error;
        wlog.Content = MakeContent(ex).ToString();
        wlog.Type = type;
        wlog.NodeName = nodeName;

        _cache.Enqueue(wlog);
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="type"></param>
    /// <param name="text"></param>
    public void LogError(IWorkflowContext context, WorkflowLogType type, string text)
    {
        Log(context, type, text, LogLevel.Error);
    }


    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="type"></param>
    /// <param name="text"></param>
    public void LogInfo(IWorkflowContext context, WorkflowLogType type, string text)
    {
        Log(context, type, text, LogLevel.Information);
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="type"></param>
    /// <param name="text"></param>
    public void LogInfo(ProcessInstance processInstance, WorkflowLogType type, string text)
    {
        Log(processInstance, type, text, LogLevel.Information);
    }


    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="type"></param>
    /// <param name="text"></param>
    public void LogDebug(ProcessInstance processInstance, WorkflowLogType type, string text)
    {
        Log(processInstance, type, text, LogLevel.Debug);
    }

    public void LogDebug(IWorkflowContext contet, WorkflowLogType type, string text)
    {
        Log(contet, type, text, LogLevel.Debug);
    }


    public void Log(WorkflowLog log)
    {
        if (log == null) throw new ArgumentNullException(nameof(log));
        _cache.Enqueue(log);
    }

    public void LogTagInQuery(WorkflowLogType type, string content, LogLevel logLevel)
    {
        var wlog = new WorkflowLog();

        wlog.LogLevel = logLevel;
        wlog.Content = content;
        wlog.Type = type;

        _cache.Enqueue(wlog);
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="type"></param>
    /// <param name="content"></param>
    /// <param name="logLevel"></param>
    private void Log(IWorkflowContext context, WorkflowLogType type, string content, LogLevel logLevel)
    {
        if (context.ProcessInstance.WorkProcess.LogLevel > logLevel)
            return;

        var wlog = new WorkflowLog();
        if (context.CurrentWorkActivity == null)
            wlog.SetProcessInstance(context.ProcessInstance);
        else
            wlog.SetWorkActivity(context.CurrentWorkActivity);

        wlog.LogLevel = logLevel;
        wlog.Content = content;
        wlog.Type = type;
        if (wlog.NodeName == null && context.CurrentNode != null) wlog.NodeName = context.CurrentNode.Name;
        _cache.Enqueue(wlog);
    }

    /// <summary>
    /// </summary>
    /// <param name="pi"></param>
    /// <param name="type"></param>
    /// <param name="content"></param>
    /// <param name="logLevel"></param>
    private void Log(ProcessInstance pi, WorkflowLogType type, string content, LogLevel logLevel)
    {
        if (pi.WorkProcess.LogLevel > logLevel)
            return;

        var wlog = new WorkflowLog();
        wlog.SetProcessInstance(pi);
        wlog.LogLevel = logLevel;
        wlog.Content = content;
        wlog.Type = type;
        _cache.Enqueue(wlog);
    }

    /// <summary>
    /// </summary>
    public void Flush()
    {
        if (_cache.Count == 0)
            return;
        using var scope = _serviceProvider.CreateScope();
        var store = scope.ServiceProvider.GetRequiredService<ILoggerStore>();

        while (_cache.TryDequeue(out var log))
            if (log != null) //ToDO: 不知道什么时候会插入 空1log
                store.AddOrUpdate(log);


        store.SaveChangesAsync().Wait();
        scope.Dispose();
    }
}