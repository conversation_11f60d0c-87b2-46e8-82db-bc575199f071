﻿using System;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class WorkflowDefinedException : WorkflowException
{
    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="module"></param>
    public WorkflowDefinedException(string message, string module) : base(message)
    {
        this.Module = module ?? throw new ArgumentNullException(nameof(module));
    }

    public WorkflowDefinedException(string message) : base(message)
    {

    }
    /// <summary>
    /// 出错得地方。
    /// </summary>
    public string Module { get; set; }
    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="innerException"></param>
    public WorkflowDefinedException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public override string Message
    {
        get
        {
            if (string.IsNullOrEmpty(this.Module))
                return base.Message;
            return $"模块【{this.Module}】。出错原因:{base.Message}";
        }

    }
}