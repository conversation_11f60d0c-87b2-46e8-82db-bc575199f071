using System;

namespace Coder.ScriptWorkflow.Services;

/// <summary>
/// 文件操作结果
/// </summary>
public class FileOperationResult
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public int ErrorCode { get; set; }

    /// <summary>
    /// 数据
    /// </summary>
    public object Data { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="message">消息</param>
    /// <returns>成功结果</returns>
    public static FileOperationResult CreateSuccess(object data = null, string message = "操作成功")
    {
        return new FileOperationResult
        {
            Success = true,
            Data = data,
            ErrorMessage = message
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="data">数据</param>
    /// <returns>失败结果</returns>
    public static FileOperationResult CreateFailure(string errorMessage, int errorCode = 500, object data = null)
    {
        return new FileOperationResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode,
            Data = data
        };
    }
}

/// <summary>
/// 泛型文件操作结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class FileOperationResult<T> : FileOperationResult
{
    /// <summary>
    /// 强类型数据
    /// </summary>
    public new T Data { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="message">消息</param>
    /// <returns>成功结果</returns>
    public static FileOperationResult<T> CreateSuccess(T data, string message = "操作成功")
    {
        return new FileOperationResult<T>
        {
            Success = true,
            Data = data,
            ErrorMessage = message
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="data">数据</param>
    /// <returns>失败结果</returns>
    public new static FileOperationResult<T> CreateFailure(string errorMessage, int errorCode = 500, T data = default)
    {
        return new FileOperationResult<T>
        {
            Success = false,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode,
            Data = data
        };
    }
}