﻿//refe
var WorkActivityStatus = {
    等待处理: 0,
    处理中: 1,
    完成: 2,
    取消: 3
};

var ProcessInstanceStatus = {
    草拟中: 0,
    审批中: 1,
    已完成: 2,
    取消: 3,
    挂起: 4
};
/**
 * 执行者类型
 */
var PerformerType = {
    user: 0,
    role: 1,
    org: 2
};

/**
 * 执行者对象，
 */
function PerformerClass() {

    /**显示名称 */
    this.name = "";
    this.type = 0;
    this.key = "";

}

/**
 * 扩展日期转换。
 * @param {any} key
 * @param {any} value
 */
JSON.dateParser = function(key, value) {
    var reISO = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2}(?:\.\d*))(?:Z|(\+|-)([\d|:]*))?$/;
    var reMsAjax = /^\/Date\((d|-|.*)\)[\/|\\]$/;
    if (typeof value === "string") {
        var a = reISO.exec(value);
        if (a)
            return new Date(value);
        a = reMsAjax.exec(value);
        if (a) {
            var b = a[1].split(/[-+,.]/);
            return new Date(b[0] ? +b[0] : 0 - +b[1]);
        }
    }
    return value;
};

var TagColor = {
    "pink": "pink",
    "red": "red",
    "orange": "orange",
    "green": "green",
    "cyan": "cyan",
    "blue": "blue",
    "purple": "purple",
    "primary": "#108ee9",
    "info": "#87d068",
    "warn": "f50"

};