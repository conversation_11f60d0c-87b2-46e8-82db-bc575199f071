﻿using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Clients
{

    public interface IProcessInstanceClient
    {
        Task<SwfResult<IEnumerable<ProcessInstanceViewModel>>> GetProcessInstanceViewModelList(string[] numbers);
        Task<SwfResult<int>> CountByNumbers(string[] numbers);
        Task<SwfResult<IEnumerable<ProcessInstanceViewModel>>> GetProcessInstanceViewModelListByIds(int[] ids);
        Task<SwfResult<ProcessInstanceViewModel>> GetProcessInstanceViewModelByNumber(string number);
        Task<SwfResult<ProcessInstanceViewModel>> GetProcessInstanceViewModelById(int id);

        Task<SwfResult<IEnumerable<ProcessInstanceFileViewModel>>> GetFilesByProcessInstanceId(int processInstanceId);
        Task<SwfResult<object>> DeleteFile(string fileId, int processInstanceId);
        Task<SwfResult<FileAttachSubmit>> Upload(string fileId, int processInstanceId);
    }
}
