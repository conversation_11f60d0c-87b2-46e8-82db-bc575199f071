﻿using Coder.ScriptWorkflow.Scripts.Plugins;
using oa.contract.service.Clients;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient;

public class JsHttpContractPlugin : IPlugin
{
    private readonly IContractHttpClient _client;
    private readonly IWorkloadHttpClient _workloadClient;

    /// <summary>
    /// </summary>
    /// <param name="client"></param>
    public JsHttpContractPlugin(IContractHttpClient client, IWorkloadHttpClient workloadClient)
    {
        _client = client;
        _workloadClient = workloadClient;
    }

    /// <summary>
    /// </summary>
    public string Name { get; set; } = "jsContract";

    public object GetObject(IWorkflowContext context)
    {
        return new ContractHttpClient(_client, context, _workloadClient);
    }

    public void Dispose(object o)
    {
    }


    public string GetJsDefined()
    {
        var stream = typeof(JsHttpContractPlugin).Assembly
            .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.JsContractClient.defined.d.ts");
        if (stream == null)
            throw new ArgumentNullException("内置文件不存在");

        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}