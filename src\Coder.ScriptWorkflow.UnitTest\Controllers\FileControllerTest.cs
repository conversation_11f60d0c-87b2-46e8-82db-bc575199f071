using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Services;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.WebApi.Controllers;
using Coder.Storage;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Controllers;

/// <summary>
/// FileController单元测试
/// </summary>
public class FileControllerTest
{
    private readonly Mock<ProcessInstanceFileService> _mockFileService;
    private readonly Mock<ILogger<FileController>> _mockLogger;
    private readonly FileController _controller;
    private readonly ClaimsPrincipal _testUser;

    public FileControllerTest()
    {
        _mockFileService = new Mock<ProcessInstanceFileService>();
        _mockLogger = new Mock<ILogger<FileController>>();
        _controller = new FileController(_mockFileService.Object, _mockLogger.Object);
        
        // 创建测试用户
        _testUser = new ClaimsPrincipal(new ClaimsIdentity(new[]
        {
            new Claim(ClaimTypes.Name, "testuser"),
            new Claim(ClaimTypes.Role, "admin")
        }, "test"));
        
        // 设置控制器的用户上下文
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = _testUser }
        };
    }

    [Fact]
    public async Task Get_WhenFileExists_ReturnsFile()
    {
        // Arrange
        var processInstanceId = 1;
        var fileId = "test-file-id";
        var fileName = "test.pdf";
        
        var stream = new MemoryStream();
        var downloadInfo = new FileDownloadInfo
        {
            Stream = stream,
            ContentType = "application/pdf",
            FileName = fileName
        };
        
        var successResult = FileOperationResult<FileDownloadInfo>.CreateSuccess(downloadInfo);
        
        _mockFileService
            .Setup(x => x.GetFileAsync(processInstanceId, fileId, _testUser, fileName))
            .ReturnsAsync(successResult);

        // Act
        var result = await _controller.Get(processInstanceId, fileName, fileId);

        // Assert
        var fileResult = Assert.IsType<FileStreamResult>(result);
        Assert.Equal("application/pdf", fileResult.ContentType);
        Assert.Equal(fileName, fileResult.FileDownloadName);
    }

    [Fact]
    public async Task Get_WhenFileNotFound_ReturnsNotFound()
    {
        // Arrange
        var processInstanceId = 1;
        var fileId = "non-existent-file";
        
        var failureResult = FileOperationResult<FileDownloadInfo>.CreateFailure("文件不存在", 404);
        
        _mockFileService
            .Setup(x => x.GetFileAsync(processInstanceId, fileId, _testUser, null))
            .ReturnsAsync(failureResult);

        // Act
        var result = await _controller.Get(processInstanceId, null, fileId);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
        Assert.Equal("文件不存在", notFoundResult.Value);
    }

    [Fact]
    public async Task Delete_WhenFileExists_ReturnsOk()
    {
        // Arrange
        var processInstanceId = 1;
        var fileId = "test-file-id";
        
        var successResult = FileOperationResult.CreateSuccess(new { success = true, message = "删除成功" });
        
        _mockFileService
            .Setup(x => x.DeleteFileAsync(processInstanceId, fileId, _testUser))
            .ReturnsAsync(successResult);

        // Act
        var result = await _controller.Delete(fileId, processInstanceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        dynamic responseData = okResult.Value;
        Assert.Equal(0, responseData.code);
        Assert.Equal("请求成功", responseData.message);
    }

    //[Fact]
    //public async Task Upload_WhenValidFile_ReturnsOk()
    //{
    //    // Arrange
    //    var processInstanceId = 1;
    //    var fileName = "test.txt";
    //    var fileContent = "test content";
        
    //    var mockFile = new Mock<IFormFile>();
    //    mockFile.Setup(f => f.FileName).Returns(fileName);
    //    mockFile.Setup(f => f.OpenReadStream()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes(fileContent)));
        
    //    var uploadRequest = new UploadFileAttachment
    //    {
    //        File = mockFile.Object,
    //        Claims = new List<UploadFileAttachment.FileClaim>()
    //    };
        
    //    var fileAttachResult = new FileAttachSubmit
    //    {
    //        FileId = "uploaded-file-id",
    //        FileName = fileName
    //    };
        
    //    var successResult = FileOperationResult<FileAttachSubmit>.CreateSuccess(fileAttachResult);
        
    //    _mockFileService
    //        .Setup(x => x.UploadFileAsync(processInstanceId, uploadRequest, _testUser))
    //        .ReturnsAsync(successResult);

    //    // Act
    //    var result = await _controller.Upload(uploadRequest, processInstanceId);

    //    // Assert
    //    var okResult = Assert.IsType<OkObjectResult>(result);
    //    Assert.NotNull(okResult.Value);
    //}

    [Fact]
    public async Task List_WhenAttachmentsExist_ReturnsOk()
    {
        // Arrange
        var processInstanceId = 1;
        var attachments = new Newtonsoft.Json.Linq.JArray();
        
        var successResult = FileOperationResult<Newtonsoft.Json.Linq.JArray>.CreateSuccess(attachments);
        
        _mockFileService
            .Setup(x => x.GetAttachmentListAsync(processInstanceId, _testUser))
            .ReturnsAsync(successResult);

        // Act
        var result = await _controller.List(processInstanceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(attachments, okResult.Value);
    }
}