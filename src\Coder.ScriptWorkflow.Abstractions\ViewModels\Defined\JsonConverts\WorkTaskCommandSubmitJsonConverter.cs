﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;

public class WorkTaskCommandSubmitJsonConverter : JsonCreationConverter<WorkTaskCommandSubmit>
{
    public const string WorkTaskCommandScriptType = "Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands.WorkTaskScriptCommandSubmit, Coder.ScriptWorkflow.Abstractions";
    public const string PreviousCommandType = "Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands.PreviousCommandSubmit, Coder.ScriptWorkflow.Abstractions";

    protected override WorkTaskCommandSubmit Create(Type objectType, JObject jObject)
    {
        if (jObject == null) throw new ArgumentNullException("jObject");
        var typeName = jObject["$type"].Value<string>();
        switch (typeName)
        {
            case WorkTaskCommandScriptType:
                return new WorkTaskScriptCommandSubmit();
            case PreviousCommandType:
                return new PreviousCommandSubmit();
            default:
                throw new ArgumentOutOfRangeException("$type", jObject["$type"].Value<string>() + " not matcher.");
        }
    }
}