﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     工作流提交
/// </summary>
public class WorkflowResolveSubmit : CustomerNextWorkTaskSetting, IJsonFormSubmit
{
    /// <summary>
    ///     处理建议，如果form 里面优质，优先采用form的数据。
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    ///     命令
    /// </summary>
    [Required]
    public string Command { get; set; }

    /// <summary>
    ///     工作流主题
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    ///     选址审批日期。
    /// </summary>
    /// <remarks>
    ///     OA系统的要求，可以选址的审批事件。
    /// </remarks>
    public DateTime? DisposeTime { get; set; }


    /// <summary>
    ///     推进工作流的用户，只有在admin的情况下，这个值才有用。
    /// </summary>
    public string ResolveUser { get; set; }

    /// <summary>
    ///     优先级别
    /// </summary>
    public Priority Priority { get; set; } = Priority.Normal;

    public string[] Distributions { get; set; }

    /// <summary>
    ///     流程实例中附带数据。
    /// </summary>
    public string Form { get; set; }

    /// <summary>
    ///     根据本form的内容，把提交的内置对象设置到本对象的
    ///     DisposeTime和comment
    /// </summary>
    public JObject FillSelfByJsonForm()
    {
        if (string.IsNullOrWhiteSpace(Form)) return new JObject();
        var jsonObject = JObject.Parse(Form);
        
        if (string.IsNullOrEmpty(Comment))
            Comment = JsonFormSubmitHelper.ToString(FormVariableDefines.WorkActivity.Comment, jsonObject);

        if (jsonObject.TryGetValue(FormVariableDefines.WorkActivity.DisposeTime, out var disposeTime))
        {
            var timeStr = disposeTime.Value<string>();
            if (string.IsNullOrWhiteSpace(timeStr))
            {
                DisposeTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
            }
            else
            {
                var submitDate = DateTime.Parse(timeStr);
                DisposeTime = new DateTime(submitDate.Year, submitDate.Month, submitDate.Day, submitDate.Hour, submitDate.Minute, submitDate.Second);
            }
        }

        var performer = JsonFormSubmitHelper.ToString(FormVariableDefines.WorkActivity.NextDisposeUser, jsonObject);
        if (performer != null)
            Performers = new[] { performer };


        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Priority, out var jValuePriority))

        {
            var number = Convert.ToInt32(jValuePriority.Value<int>());
            Priority = (Priority)number;
        }

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Subject, out var jsSubjectToken))
        {
            var subject = jsSubjectToken.Value<string>();
            Subject = subject;
        }

        if (jsonObject.TryGetValue(FormVariableDefines.ProcessInstance.Cc, out var ccFormData))
        {
            if (ccFormData.Type == JTokenType.Array)
            {
                var jsArray = (JArray)ccFormData;
                Distributions = jsArray.Select(_ => _.Value<string>()).ToArray();
            }
            else
            {
                Distributions = new[] { ccFormData.Value<string>() };
            }
        }

        return jsonObject;
    }
}