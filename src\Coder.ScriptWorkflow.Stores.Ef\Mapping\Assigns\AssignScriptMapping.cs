﻿using Coder.ScriptWorkflow.Assigners;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.Assigns;

/// <summary>
/// </summary>
internal class AssignScriptMapping : IEntityTypeConfiguration<ScriptAssigner>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ScriptAssigner> builder)
    {
        builder.HasBaseType<Assigner>();
        builder.Property(_ => _.Script).HasMaxLength(3000);
    }
}