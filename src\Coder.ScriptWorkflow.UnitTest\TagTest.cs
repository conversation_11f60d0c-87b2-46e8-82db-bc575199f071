﻿using Xunit;

namespace Coder.ScriptWorkflow.UnitTest;

public class TagTest
{
    [Fact]
    public void TestTagGetDisposeName()
    {
        var exp = Tag.GetDataAfterColon(Tag.DisposeUserNameKey + "userName");
        Assert.Equal("userName", exp);
    }

    [Fact]
    public void TestTagSetDisposeName()
    {

        string org = "org:部门/子部门1";
        var t=Tag.GetDataAfterColon(org);
        Assert.Equal("部门/子部门1", t);
    }
}