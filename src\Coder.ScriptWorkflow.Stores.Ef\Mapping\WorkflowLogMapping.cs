﻿using System;
using Coder.ScriptWorkflow.Logger;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Coder.ScriptWorkflow.Mapping;

internal class WorkflowLogMapping : IEntityTypeConfiguration<WorkflowLog>
{
    /// <summary>
    /// </summary>
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public WorkflowLogMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    public void Configure(EntityTypeBuilder<WorkflowLog> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.ToTable($"{_prefix}_workflowLog");

        builder.Property(_ => _.WorkProcessName).HasMaxLength(64).HasComment("工作流名称");
        builder.Property(_ => _.ProcessInstanceId).HasComment("工单id");
        builder.Property(_ => _.ProcessInstanceNumber).HasMaxLength(20).HasComment("工单号");
        builder.Property(_ => _.Version).HasComment("工作流定义版本");
        builder.Property(_ => _.NodeName).HasMaxLength(64).HasComment("节点名称");

        builder.Property(_ => _.WorkActivityId);
        builder.Property(_ => _.Type)
            .HasConversion<EnumToStringConverter<WorkflowLogType>>()
            .HasMaxLength(64);
        builder.Property(_ => _.Content).HasColumnType("text");
        builder.Property(_ => _.CreateTime);
        builder.Property(_ => _.PluginName).HasMaxLength(30);

        builder.HasIndex(_ => _.ProcessInstanceNumber);
    }
}