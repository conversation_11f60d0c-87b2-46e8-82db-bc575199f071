﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Stores;
using Coder.WebHttpClient;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流维护
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
public class WorkflowMaintenanceController : ControllerBase
{
    private readonly ILogger<WorkflowMaintenanceController> _logger;
    private readonly ILoggerStore _loggerStore;
    private readonly IProcessInstanceStore _processInstanceStore;
    private readonly ITransactionFactory _transactionFactory;
    private readonly IWorkActivityStore _workActivityStore;

    public WorkflowMaintenanceController(
        IWorkActivityStore workActivityStore,
        IProcessInstanceStore processInstanceStore,
        ITransactionFactory transactionFactory,
        ILoggerStore loggerStore,
        ILogger<WorkflowMaintenanceController> logger)
    {
        _transactionFactory = transactionFactory ?? throw new ArgumentNullException(nameof(transactionFactory));
        _loggerStore = loggerStore ?? throw new ArgumentNullException(nameof(loggerStore));
        _workActivityStore = workActivityStore ?? throw new ArgumentNullException(nameof(workActivityStore));
        _processInstanceStore = processInstanceStore ?? throw new ArgumentNullException(nameof(processInstanceStore));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    ///     工单已闭单状态改为未完成，工作处理人退回到提单环节
    /// </summary>
    [HttpGet("get-process-back-first")]
    [ProducesResponseType(typeof(ResponseMessage), 404)]
    public async Task<IActionResult> GetProcessingBackFirstAsync([FromQuery] string number)
    {
        if (string.IsNullOrWhiteSpace(number)) return Ok(new { success = false, message = "请先输入工单号" });

        try
        {
            var result = ValidateProcessInstance(number);
            if (result.ValidationResult != null) return result.ValidationResult;

            var query = _workActivityStore.WorkActivities
                .Where(workActivity => workActivity.ProcessInstance.Id == result.ProcessInstance.Id
                                       && workActivity.Status == WorkActivityStatus.Complete)
                .OrderBy(_ => _.Id)
                .FirstOrDefault();

            if (query == null) return Ok(new { success = false, message = $"{number} 没有找到相关的已完成活动" });

            return await ExecuteWorkflowOperationAsync(number, result.ProcessInstance, query,
                (pi, wa) =>
                {
                    pi.ProcessingByCompleted();
                    wa.ProcessingByCompleted();
                },
                "执行成功；工单状态已改为未完成，退回到提单环节");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理工单 {Number} 退回到第一环节时发生异常", number);
            return Ok(new { success = false, message = $"{number} 执行失败，请联系管理员！" });
        }
    }

    /// <summary>
    ///     工单已闭单状态改为未完成，工作处理人回退到最后一环节
    /// </summary>
    [HttpGet("get-process-back-last")]
    [ProducesResponseType(typeof(ResponseMessage), 404)]
    public async Task<IActionResult> GetProcessingBackLastAsync([FromQuery] string number)
    {
        if (string.IsNullOrWhiteSpace(number)) return Ok(new { success = false, message = "请先输入工单号" });

        try
        {
            var result = ValidateProcessInstance(number);
            if (result.ValidationResult != null) return result.ValidationResult;

            var query = _workActivityStore.WorkActivities
                .Where(workActivity => workActivity.ProcessInstance.Id == result.ProcessInstance.Id
                                       && workActivity.Status == WorkActivityStatus.Complete)
                .OrderByDescending(_ => _.Id)
                .FirstOrDefault();

            if (query == null) return Ok(new { success = false, message = $"{number} 没有找到相关的已完成活动" });

            return await ExecuteWorkflowOperationAsync(number, result.ProcessInstance, query,
                (pi, wa) =>
                {
                    pi.ProcessingByCompleted();
                    wa.ProcessingByCompleted();
                },
                "执行成功；工单已闭单状态改为未完成，工作处理人回退到最后一环节");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理工单 {Number} 退回到最后环节时发生异常", number);
            return Ok(new { success = false, message = $"{number} 执行失败，请联系管理员！" });
        }
    }

    /// <summary>
    ///     工单未完成状态改为已闭单，提单处理人直接完成
    /// </summary>
    [HttpGet("get-completed-first")]
    [ProducesResponseType(typeof(ResponseMessage), 404)]
    public async Task<IActionResult> GetCompletedFirstAsync([FromQuery] string number)
    {
        if (string.IsNullOrWhiteSpace(number)) return Ok(new { success = false, message = "请先输入工单号" });

        try
        {
            var result = ValidateProcessInstanceForCompletionAsync(number);
            if (result.ValidationResult != null) return result.ValidationResult;

            var query = _workActivityStore.WorkActivities
                .Where(workActivity => workActivity.ProcessInstance.Id == result.ProcessInstance.Id
                                       && workActivity.Status == WorkActivityStatus.Processing)
                .OrderBy(activity => activity.Id)
                .FirstOrDefault();

            if (query == null) return Ok(new { success = false, message = $"{number} 没有找到相关的处理中活动" });

            return await ExecuteWorkflowOperationAsync(number, result.ProcessInstance, query,
                (pi, wa) =>
                {
                    pi.CompletedByProcessing();
                    wa.CompletedByProcessing();
                },
                "执行成功；工单未完成状态改为已闭单，提单处理人直接完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理工单 {Number} 完成操作时发生异常", number);
            return Ok(new { success = false, message = $"{number} 执行失败，请联系管理员！" });
        }
    }

    private (ProcessInstance ProcessInstance, IActionResult ValidationResult) ValidateProcessInstance(string number)
    {
        var processInstance = _processInstanceStore.Get(number);
        if (processInstance == null) return (null, Ok(new { success = false, message = $"{number} 不存在" }));

        if (processInstance.Status == ProcessInstanceStatus.Processing)
            return (null, Ok(new { success = false, message = "流程正在运行中，请按流程继续运行！" }));

        if (processInstance.Status == ProcessInstanceStatus.Cancel)
            return (null, Ok(new { success = false, message = $"{number} 已取消，不能修改工单状态" }));

        return (processInstance, null);
    }

    private (ProcessInstance ProcessInstance, IActionResult ValidationResult)
        ValidateProcessInstanceForCompletionAsync(string number)
    {
        var processInstance = _processInstanceStore.Get(number);
        if (processInstance == null) return (null, Ok(new { success = false, message = $"{number} 不存在" }));

        if (processInstance.Status == ProcessInstanceStatus.Completed)
            return (null, Ok(new { success = false, message = $"{number} 已完成，不用执行该操作！" }));

        if (processInstance.Status == ProcessInstanceStatus.Cancel)
            return (null, Ok(new { success = false, message = $"{number} 已取消，不能修改工单状态" }));

        return (processInstance, null);
    }

    private async Task<IActionResult> ExecuteWorkflowOperationAsync(
        string number,
        ProcessInstance processInstance,
        WorkActivity workActivity,
        Action<ProcessInstance, WorkActivity> operation,
        string successMessage)
    {
        using var transManager = _transactionFactory.BeginTransactions();
        try
        {
            operation(processInstance, workActivity);

            _processInstanceStore.AddOrUpdate(processInstance);
            await _processInstanceStore.SaveChangesAsync().ConfigureAwait(false);

            _workActivityStore.AddOrUpdate(workActivity);
            await _workActivityStore.SaveChangesAsync().ConfigureAwait(false);

            transManager.Commit();

            _logger.LogInformation("工单 {Number} 操作成功：{Message}", number, successMessage);
            return Ok(new { success = true, message = $"{number} {successMessage}" });
        }
        catch (Exception ex)
        {
            transManager.Rollback();
            _logger.LogError(ex, "工单 {Number} 操作失败", number);
            throw;
        }
    }
}