﻿using Coder.Token.Clients;
using Microsoft.Extensions.DependencyInjection;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using Coder.Authentication;
using Array = NiL.JS.BaseLibrary.Array;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient;

public class JsHttpClient
{
    private IServiceProvider _serviceProvider;
    private readonly IHttpClientFactory _httpClientFactory;
    private HttpClient _client;
    private static DateTime? _expireTime;
    private static string _token;

    private static void InitClient(HttpClient httpClient, IServiceProvider sp)
    {
        if (sp == null) return;
        if (_expireTime == null || (_expireTime.Value - DateTime.Now).TotalMinutes <= 5 || _token == null)
        {
            var tokenClient = sp.GetRequiredService<ITokenBuilderClient>();
            var result = tokenClient.CreateTokenAsync(new CreateTokenSubmit()).Result;
#if NET6_0
                _token = result.Token;
                _expireTime = result.ExpireTime;
#else
            _token = result.Data.Token;
            _expireTime = result.Data.ExpireTime.DateTime;
#endif

        }


        if (httpClient.DefaultRequestHeaders.Contains("Authorization"))
            httpClient.DefaultRequestHeaders.Remove("Authorization");
        httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + _token);
    }

    public JsHttpClient(IHttpClientFactory factory, IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _httpClientFactory = factory ?? throw new ArgumentNullException(nameof(factory));
    }

    public JsHttpClient(HttpClient client)
    {
        _client = client ?? throw new ArgumentNullException(nameof(client));
    }

    public HttpClient GetClient()
    {
        var result = _client ??= _httpClientFactory.CreateClient();
        InitClient(_client, _serviceProvider);
        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="path"></param>
    /// <param name="jObject"></param>
    /// <returns></returns>
    public JSValue GetJson(string path, JSValue jObject = null)
    {
        if (path == null) throw new ArgumentNullException(nameof(path));

        var client = GetClient();
        HttpResponseMessage result = null;
        if (jObject != null)
        {
            var queryString = ToQueryString(jObject);
            result = client.GetAsync(path + "?" + queryString).Result;
        }
        else
        {
            result = client.GetAsync(path).Result;
        }

        var body = result.Content.ReadAsStringAsync().Result;
        if (result.StatusCode == HttpStatusCode.OK) return JSON.parse(body);

        throw new JsHttpPluginException($"httpCode:{result.StatusCode},body：{body}");
    }

    public JSValue PostJson(string path, JSValue jObject)
    {
        if (path == null) throw new ArgumentNullException(nameof(path));
        if (jObject == null) throw new ArgumentNullException(nameof(jObject));


        var client = GetClient();

        var json = JSON.stringify(jObject);

        var jsonContent = new StringContent(json, Encoding.UTF8, "application/json");
        var result = client.PostAsync(path, jsonContent).Result;

        var body = result.Content.ReadAsStringAsync().Result;
        if (result.StatusCode == HttpStatusCode.OK) return JSON.parse(body);

        throw new JsHttpPluginException($"httpCode:{result.StatusCode},body：{body}");
    }

    public JSValue PutJson(string path, JSValue jObject)
    {
        if (path == null) throw new ArgumentNullException(nameof(path));
        if (jObject == null) throw new ArgumentNullException(nameof(jObject));

        var client = GetClient();

        var json = JSON.stringify(jObject);

        var jsonContent = new StringContent(json, Encoding.UTF8, "application/json");
        var result = client.PutAsync(path, jsonContent).Result;

        var body = result.Content.ReadAsStringAsync().Result;
        if (result.StatusCode == HttpStatusCode.OK) return JSON.parse(body);

        throw new JsHttpPluginException($"httpCode:{result.StatusCode},body：{body}");
    }

    public JSValue Delete(string path)
    {
        if (System.String.IsNullOrEmpty(path)) throw new ArgumentNullException(nameof(path));

        var client = GetClient();
        var result = client.DeleteAsync(path).Result;

        var body = result.Content.ReadAsStringAsync().Result;
        if (result.StatusCode == HttpStatusCode.OK) return JSON.parse(body);

        throw new JsHttpPluginException($"httpCode:{result.StatusCode},body：{body}");
    }

    /// <summary>
    /// </summary>
    /// <param name="url"></param>
    /// <param name="jsValue"></param>
    /// <returns></returns>
    public JSValue PostForm(string url, JSValue jsValue)
    {
        if (url == null) throw new ArgumentNullException(nameof(url));
        if (jsValue == null) throw new ArgumentNullException(nameof(jsValue));

        using var client = _httpClientFactory.CreateClient();
        var dict = new Dictionary<string, string>();
        foreach (var jObj in jsValue)
        {
            if (jObj.Value.Value == null) continue;
            var value = "";
            if (jObj.Value.Value is Date date)
                value = date.ToDateTime().ToString("O");
            else
                value = jObj.Value.Value.ToString();
            dict.Add(jObj.Key, value);
        }

        var httpResponseMessage = client.PostAsync(url, new FormUrlEncodedContent(dict)).Result;

        return JSON.parse($@"{{
code:{(int)httpResponseMessage.StatusCode},
body:{httpResponseMessage.Content.ReadAsStringAsync().Result}

}}");
    }

    private static string ToQueryString(JSValue jobject)
    {
        var result = new List<string>();


        foreach (var keyValuePair in jobject)
            if (keyValuePair.Value.Value is Array jsArray)
            {
                foreach (var jsValueArray in jsArray)
                {
                    var val = JsValueConvert.ToJSON(jsValueArray.Value);
                    result.Add(keyValuePair.Key + "=" + val);
                }
            }
            else
            {
                var val = JsValueConvert.ToJSON(keyValuePair.Value);
                result.Add(keyValuePair.Key + "=" + val);
            }


        return string.Join("&", result);
    }

    private static IDictionary<string, string> ToDictionary(JSValue jobject)
    {
        var dict = new Dictionary<string, string>();
        foreach (var keyValuePair in jobject)
        {
            var val = JsValueConvert.ToJSON(keyValuePair.Value);

            dict.Add(keyValuePair.Key, val);
        }

        return dict;
    }
}