﻿using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Newtonsoft.Json;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel;

/// <summary>
/// </summary>
public class ProcessInstanceFormTest
{
    [Fact]
    public void Test()
    {
        var formSubmit = new ProcessInstanceFormSubmit();
        var a = new
        {
            PI_subject = "subject"
        };
        formSubmit.Form = JsonConvert.SerializeObject(a);
        formSubmit.FillSelfByJsonForm();


    }
}