﻿using Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient.HumanResource;
using Newtonsoft.Json;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using Org.BouncyCastle.Asn1;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Org.BouncyCastle.Math.EC.ECCurve;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient
{
    public class HumanResourceHttpFactory
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly HumResourceConfig _config;


        public HumanResourceHttpFactory( IHttpClientFactory httpClientFactory, HumResourceConfig config)
        {
            _httpClientFactory = httpClientFactory;
            _config = config;
        }

        public async Task PostAsync(HumanResourceSubmit submit, string url)
        {
            var client = _httpClientFactory.CreateClient();
            var baseUrl = _config.Url;
            if (!baseUrl.EndsWith("/"))
                baseUrl += "/";
            url = baseUrl + url;
            var submitStr = JsonConvert.SerializeObject(submit);
            var content = new StringContent(submitStr, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(url, content);
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
            //需要增加调用记录
            Console.WriteLine(result);
        }


        public async Task<string> GetAsync(string userName, string url)
        {
            var client = _httpClientFactory.CreateClient();
            var baseUrl = _config.Url;
            if (!baseUrl.EndsWith("/"))
                baseUrl += "/";
            url = baseUrl + url;
            url = url + "?userName=" + userName;
            var response = await client.GetAsync(url);
            var result = await response.Content.ReadAsStringAsync();
            //需要增加调用记录
            Console.WriteLine(result);
            return result;
        }
    }
}
