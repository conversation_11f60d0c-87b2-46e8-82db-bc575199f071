﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IProcessInstanceStore
{
    /// <summary>
    /// </summary>
    IQueryable<ProcessInstance> ProcessInstances { get; }


    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    ProcessInstance GetById(int id);

    /// <summary>
    /// </summary>
    /// <param name="instance"></param>
    void AddOrUpdate([NotNull] ProcessInstance instance);

    /// <summary>
    ///     保存更改
    /// </summary>
    void SaveChanges();

    /// <summary>
    ///     获取number
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    [return: MaybeNull]
    ProcessInstance Get(string number);

    /// <summary>
    ///     检查是否有工作实例正在执行
    /// </summary>
    /// <param name="wp"></param>
    /// <returns></returns>
    int CountProcessingWorkflow(int wp);

    /// <summary>
    ///     删除Instance
    /// </summary>
    /// <param name="instance"></param>
    void Remove(IEnumerable<ProcessInstance> instance);

    /// <summary>
    /// </summary>
    /// <param name="instance"></param>
    void Remove(ProcessInstance instance);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    Task<IEnumerable<InstanceListItemViewModel>> ListAsync(ProcessInstanceSearcher searcher, ClaimsPrincipal currentUser, WorkflowPermissionManager permissionManager);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    Task<int> CountAsync(ProcessInstanceSearcher searcher, ClaimsPrincipal currentUser, WorkflowPermissionManager permissionManager);

    Task<IEnumerable<ProcessInstance>> ListByNumbersAsync(List<string> numbers);

    Task<IEnumerable<ProcessInstance>> ListByIdsAsync(List<int> ids);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    Task<int> CountByNumbersAsync(List<string> numbers);

    /// <summary>
    ///     获取与ProcessInstance 相关的Tag
    /// </summary>
    /// <param name="workflowContextProcessInstance"></param>
    /// <returns></returns>
    Task<IEnumerable<ProcessInstanceTag>> GetTagsAsync(ProcessInstance workflowContextProcessInstance);

    /// <summary>
    ///     只是在持久层删除tag。然后没人任何操作
    /// </summary>
    /// <param name="item"></param>
    void RemoveTag(ProcessInstanceTag item);

    /// <summary>
    /// </summary>
    /// <param name="newProcessInstanceTag"></param>
    void AddOrUpdate(ProcessInstanceTag newProcessInstanceTag);

    /// <summary>
    ///     是否有工单
    /// </summary>
    /// <param name="workProcessId"></param>
    /// <returns></returns>
    Task<bool> HasProcessInstance(int workProcessId);

    /// <summary>
    /// </summary>
    /// <param name="pi"></param>
    /// <param name="tag"></param>
    /// <returns></returns>
    ProcessInstanceTag GetTagByTagName(ProcessInstance pi, string tag);

    /// <summary>
    /// </summary>
    /// <param name="piIds"></param>
    /// <returns></returns>
    Task<IEnumerable<ProcessInstanceTagViewModel>> GetTagsByIdsAsync(int[] piIds);

    /// <summary>
    ///     删除文件记录
    /// </summary>
    /// <param name="file"></param>
    void RemoveFile(ProcessInstance.FileAttach file);

    Task<int> CountAsync(string creator, ProcessInstanceStatus[] status);

    Task SaveChangesAsync();
}