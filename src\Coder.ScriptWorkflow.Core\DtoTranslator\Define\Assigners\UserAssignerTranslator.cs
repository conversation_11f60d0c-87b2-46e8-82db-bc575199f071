﻿using System.Linq;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.Assigners;

/// <summary>
/// </summary>
public class UserAssignerTranslator : AssignerTranslator
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected override Assigner CreateEntity()
    {
        return new UsersAssigner();
    }

    protected override AssignSubmit CreateViewModel()
    {
        return new UsersAssignerSubmit();
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="traget"></param>
    protected override void FillToEntity(AssignSubmit src, Assigner traget)
    {
        var assignSrc = (UsersAssignerSubmit)src;
        var assignTarget = (UsersAssigner)traget;

        assignTarget.Performers = assignSrc.Performers.Select(_ => new Performer
        {
            Key = _.Key,
            Name = _.Name,
            Type = _.Type
        }).ToList();
        //assignTarget.PerformerType = assignSrc.PerformerType;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="traget"></param>
    protected override void FillToViewModel(Assigner src, AssignSubmit traget)
    {
        var srcAssign = (UsersAssigner)src;
        var targetAssign = (UsersAssignerSubmit)traget;


        targetAssign.Performers = srcAssign.Performers.Select(_ => new PerformerSubmit
        {
            Key = _.Key,
            Name = _.Name,
            Type = _.Type
        }).ToList();
    }

    /// <summary>
    /// </summary>
    /// <param name="assigner"></param>
    /// <returns></returns>
    protected override bool TypeIsMatch(Assigner assigner)
    {
        return assigner is UsersAssigner;
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public override bool Validate(AssignSubmit submit, out string errorMessage)
    {
        var userAssignerSubmit = (UsersAssignerSubmit)submit;
        errorMessage = null;

        if (!userAssignerSubmit.Performers.Any())
        {
            errorMessage = "执行者不能为空。";
            return false;
        }

        return true;
    }
}