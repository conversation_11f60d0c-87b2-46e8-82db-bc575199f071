﻿using Xunit;

namespace Coder.ScriptWorkflow.UnitTest;

/// <summary>
/// </summary>
public class WorkActivityTest
{
    /// <summary>
    /// </summary>
    [Fact]
    public void Resolve()
    {
        var wp = new WorkProcess("name");
        var wt = new WorkTask("t", wp);

        var target = new WorkActivity(new ProcessInstance(wp, "createUser"), wt, Priority.Normal);

        target.AssignTo("one", "一个人");

        Assert.Equal("一个人", target.DisposeUserName);
        Assert.Equal("one", target.DisposeUser);
        Assert.NotNull(target.AssignTime);
    }
}