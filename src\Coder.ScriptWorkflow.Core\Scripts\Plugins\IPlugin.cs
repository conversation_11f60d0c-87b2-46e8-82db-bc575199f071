﻿namespace Coder.ScriptWorkflow.Scripts.Plugins;

/// <summary>
///     插件
/// </summary>
public interface IPlugin
{
    /// <summary>
    ///     名称,注入到js运行环境的名称。
    /// </summary>
    public string Name { get; }

    /// <summary>
    ///     创建
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context);

    /// <summary>
    ///     deposit
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined();
}