﻿using Coder.ScriptWorkflow.Nodes;

namespace Coder.ScriptWorkflow.Decisions;

/// <summary>
///     判断器
/// </summary>
public abstract class Decision : Node
{
    /// <inheritdoc />

    public override string Name
    {
        get
        {
            if (string.IsNullOrEmpty(base.Name)) base.Name = $"判定-{Id}";

            return base.Name;
        }
        set => base.Name = value;
    }


    /// <summary>
    ///     匹配说明
    /// </summary>
    public string MatchDescription { get; set; }
}