﻿using Coder.Migrator.Clients;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Microsoft.Extensions.DependencyInjection;

namespace Coder.ScriptWorkflow.Scripts.Plugin.MigratorClient;

/// <summary>
/// </summary>
public class JsDataPusherPlugin : IPlugin
{
    private readonly IServiceProvider _dataClient;

    /// <summary>
    /// </summary>
    /// <param name="serviceProvider"></param>
    public JsDataPusherPlugin(IServiceProvider serviceProvider)
    {
        _dataClient = serviceProvider;
    }

    /// <summary>
    /// </summary>
    public string Name { get; set; } = "jsDataPusher";

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        var a = _dataClient.GetService<IDataClient>();
        var loggerManager = _dataClient.GetRequiredService<WorkflowLogManager>();

        return new DataClient(a, loggerManager, context);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        var stream = typeof(JsDataPusherPlugin).Assembly
            .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.MigratorClient.defined.ts");
        if (stream == null)
            throw new Exception("文件没有增却内嵌");
        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}