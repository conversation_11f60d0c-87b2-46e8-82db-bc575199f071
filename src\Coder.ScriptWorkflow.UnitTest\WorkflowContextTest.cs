﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Microsoft.Extensions.DependencyInjection;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest;

public class WorkflowContextTest
{
    private readonly string DateFormat = "yyyy-MM-ddTHH:mm:ss";
    private static readonly UserViewModel AdminUserViewModel = new()
    {
        Name = "管理员",
        UserName = "admin"
    };
    [Fact]
    public void TestAddTag()
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();

        var workflowContext = new WorkflowContext(
            new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            new ProcessInstance(wp, "user"), new List<WorkTask>
            {
                (WorkTask)connectionAction.NextNode,
                (WorkTask)connectionAction.ElseNode
            }, new IPlugin[0], new List<ScriptTagInfo>
            {
                new()
                {
                    ProcessInstanceTagId = 1,
                    TagName = "processing"
                }
            }, new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);
        var context = new Context(workflowContext.JavascriptGlobalContext);

        var script = @"
addTag('leo处理中','red')
removeTag('processing')
";

        context.Eval(script);

        var tag = workflowContext.Tags.First(_ => _.TagName == "leo处理中");
        Assert.Equal(TagChanged.Added, tag.TagStatus);

        tag = workflowContext.Tags.First(_ => _.TagName == "processing");
        Assert.Equal(TagChanged.Removed, tag.TagStatus);
    }


    [Theory]
    [InlineData("user", PerformerType.User)]
    [InlineData("role", PerformerType.Role)]
    [InlineData("org", PerformerType.Org)]
    public void TestGlobalSettings_PerformerType(string key, PerformerType performerType)
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(
            new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()),
            new ProcessInstance(wp, "user"), new List<WorkTask>
            {
                (WorkTask)connectionAction.NextNode,
                (WorkTask)connectionAction.ElseNode
            }, new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);
        var context = new Context(workflowContext.JavascriptGlobalContext);
        context.Eval($"var f=PerformerType.{key}");
        var target = (int)context.GetVariable("f").Value;
        Assert.Equal((int)performerType, target);
    }

    [Theory]
    [InlineData("草拟中", ProcessInstanceStatus.Created)]
    [InlineData("审批中", ProcessInstanceStatus.Processing)]
    [InlineData("已完成", ProcessInstanceStatus.Completed)]
    [InlineData("取消", ProcessInstanceStatus.Cancel)]
    [InlineData("挂起", ProcessInstanceStatus.Suspend)]
    public void TestGlobalSettings_ProcessInstanceStatus(string key, ProcessInstanceStatus status)
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()), new ProcessInstance(wp, "user"), new List<WorkTask>
        {
            (WorkTask)connectionAction.NextNode,
            (WorkTask)connectionAction.ElseNode
        }, new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);
        var context = new Context(workflowContext.JavascriptGlobalContext);
        context.Eval($"var f=ProcessInstanceStatus.{key}");
        var target = (int)context.GetVariable("f").Value;
        Assert.Equal((int)status, target);
    }

    [Theory]
    [InlineData("等待处理", WorkActivityStatus.UnAssign)]
    [InlineData("处理中", WorkActivityStatus.Processing)]
    [InlineData("完成", WorkActivityStatus.Complete)]
    [InlineData("取消", WorkActivityStatus.CloseByAdmin)]
    public void TestGlobalSettings_WorkActivityStatus(string key, WorkActivityStatus status)
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()), new ProcessInstance(wp, "user"), new List<WorkTask>
        {
            (WorkTask)connectionAction.NextNode,
            (WorkTask)connectionAction.ElseNode
        }, new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);
        var context = new Context(workflowContext.JavascriptGlobalContext);
        context.Eval($"var f=WorkActivityStatus.{key}");
        var target = (int)context.GetVariable("f").Value;
        Assert.Equal((int)status, target);
    }

    [Fact]
    public void TestJsonDateFormat()
    {
        var wp = new WorkProcess("wp");
        var connectionAction = new BoolScriptDecision
        {
            NextNode = new WorkTask("NextNode", wp),
            ElseNode = new WorkTask("otherNode", wp)
        };
        var service = new ServiceCollection();
        var sp = service.BuildServiceProvider();
        var workflowContext = new WorkflowContext(new GlobalScriptContext(new List<GlobalScriptItem>(), new List<GlobalVariable>()), new ProcessInstance(wp, "user"), new List<WorkTask>
        {
            (WorkTask)connectionAction.NextNode,
            (WorkTask)connectionAction.ElseNode
        }, new IPlugin[0], new List<ScriptTagInfo>(), new WorkflowLogManager(sp), new DebuggerManager(new EmptyDebuggerPusher()), AdminUserViewModel);

        var now = DateTimeOffset.Now;
        var jsonDate = ToJSON(now);
        var script = $"var form=JSON.parse('{jsonDate}',JSON.dateParser)";
        var ctx = new Context(workflowContext.JavascriptGlobalContext);
        ctx.Eval(script);
        var form = ctx.GetVariable("form");

        var starttDate = (Date)form["开始日期"].Value;
        Assert.Equal(now.ToString(DateFormat), starttDate.ToDateTime().ToString(DateFormat));
    }

    private string ToJSON(DateTimeOffset start)
    {
        //var startStr = $"{start.Year},{start.Month},{start.Day},{start.Hour},{start.Minute},{start.Second}";
        //var endStr = $"{end.Year},{end.Month},{end.Day},{end.Hour},{end.Minute},{end.Second}";
        var startStr = start.ToString("o");

        var ctx = new Context();
        ctx.Eval($@"var a={{
开始日期:new Date('{startStr}')
}}
var json=JSON.stringify(a)
");
        var s = (string)ctx.GetVariable("json").Value;
        return s;
    }
}