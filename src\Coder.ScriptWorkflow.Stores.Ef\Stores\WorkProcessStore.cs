﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Coder.ScriptWorkflow.Stores;

/// <inheritdoc />
public class WorkProcessStore<T> : IWorkProcessStore where T : DbContext
{
    private readonly T _dbContext;
    private IDbContextTransaction _transaction;

    /// <inheritdoc />
    public WorkProcessStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    /// <inheritdoc />
    public IQueryable<WorkProcess> WorkProcesses => _dbContext.Set<WorkProcess>();

    /// <inheritdoc />
    public void AddOrUpdate(WorkProcess workProcess)
    {
        _dbContext.Update(workProcess);
    }

    /// <inheritdoc />
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <inheritdoc />
    public void Delete(WorkProcess entity)
    {
        if (entity.OnStart != null)
            _dbContext.Remove((object)entity.OnStart);
        if (entity.OnComplete != null)
            _dbContext.Remove((object)entity.OnComplete);
        _dbContext.Remove(entity);
    }

    /// <inheritdoc />
    public WorkProcess Get(int id)
    {
        return WorkProcesses.FirstOrDefault(_ => _.Id == id);
    }

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public WorkProcess GetByEffectName(string name)
    {
        var wf = WorkProcesses.Where(_ => _.Enable && _.Name == name).OrderByDescending(_ => _.Version)
            .FirstOrDefault();
        return wf;
    }

    /// <inheritdoc />
    public IEnumerable<WorkProcess> ListByName(string name)
    {
        return WorkProcesses.Where(_ => _.Name == name).ToList();
    }

    /// <inheritdoc />
    public bool Exist(string workProcessName, int? version, int? notIncludeId)
    {
        var id = notIncludeId ?? 0;
        return WorkProcesses.Any(_ => _.Name == workProcessName && _.Id != id && (version == null || version.Value == _.Version));
    }

    /// <inheritdoc />
    public WorkProcess GetByNameVersion(string name, int version)
    {
        return WorkProcesses.FirstOrDefault(_ => _.Version == version && _.Name == name);
    }

    /// <inheritdoc />
    Task<int> IWorkProcessStore.Count(WorkProcessListSearcher searcher)
    {
        return Count(searcher);
    }

    /// <inheritdoc />
    public bool ExistNumberPrefix(string submitPrefix, string submitName, int id)
    {
        return WorkProcesses.Any(_ => _.Prefix == submitPrefix && _.Name != submitName && (id == 0 || _.Id != id));
    }


    /// <inheritdoc />
    public Task<WorkProcessScript> GetScriptAsync(int id)
    {
        return _dbContext.Set<WorkProcessScript>().FirstOrDefaultAsync(_ => _.Id == id);
    }

    /// <inheritdoc />
    public void SaveScript(WorkProcessScript scriptSubmit)
    {
        _dbContext.Update(scriptSubmit);
    }

    /// <summary>
    ///     分页查询数据
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    public Task<IEnumerable<WorkProcess>> List(WorkProcessListSearcher searcher)
    {
        var query = QueryList(searcher);
        return searcher.ForPager(query.OrderByDescending(f => f.UpdateTimeOffset))
            .ToListAsync().ContinueWith(task => (IEnumerable<WorkProcess>)task.Result);
    }

    /// <inheritdoc />
    public void TryBeginTransaction()
    {
        _transaction = _dbContext.Database.CurrentTransaction != null
            ? null
            : _dbContext.Database.BeginTransaction();
    }

    /// <inheritdoc />
    public void Commit()
    {
        _transaction?.Commit();
    }

    /// <inheritdoc />
    public void Rollback()
    {
        _transaction?.Rollback();
    }

    /// <summary>
    ///     查询数据总量
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    public Task<int> Count(WorkProcessListSearcher searcher)
    {
        var query = QueryList(searcher);
        return query.CountAsync();
    }

    /// <inheritdoc />
    public IQueryable<WorkProcess> QueryList(WorkProcessListSearcher search)
    {
        if (search == null) throw new ArgumentNullException(nameof(search));
        var query = _dbContext.Set<WorkProcess>()
            .Where(_ => (string.IsNullOrEmpty(search.Name) || _.Name.Contains(search.Name))
                        && (search.IsAdmin || search.Names.Any(n => n == _.Name))
                        && (search.Enable == null || _.Enable == search.Enable)
            );
        return query;
    }
}