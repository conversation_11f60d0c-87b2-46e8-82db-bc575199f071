﻿using System;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers.GetNextWorkActivityUsers;

/*
 * 重复进入之后，上一次的处理用户Default=true
 */
public class ScriptAssignerFirstEntry
{
    private static IServiceProvider CreateServiceProvider()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options => { options.UseSqlite($"Data Source={dbFile}.db;"); });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();
        scope.Dispose();
        return sp;
    }

    private static WorkProcess SimpleAuth(IServiceProvider sp)
    {
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();

        var workProcess = new WorkProcess("流程-测试")
        {
            Enable = true
        };
        wpStore.AddOrUpdate(workProcess);
        wpStore.SaveChangesAsync().Wait();

        wpStore.SaveChangesAsync().Wait();

        var draf = new WorkTask("草拟", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = @"return processInstance.Creator"
            }
        };
        draf.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "提交"
        });


        var auth1 = new WorkTask("审批1", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return ['user1','user2']"
            }
        };
        auth1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意",
            Script = "processInstance.Form.auth1='同意'"
        });
        auth1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "拒绝",
            Script = "processInstance.Form.auth1='拒绝'"
        });


        wtStore.AddOrUpdate(draf);
        wtStore.AddOrUpdate(auth1);

        wtStore.SaveChangesAsync().Wait();
        var auth1Decision = new BoolScriptDecision(new EndNode(workProcess), draf)
        {
            Script = "return processInstance.Form.auth1=='同意'"
        };
        auth1.NextNode = auth1Decision;
        wtStore.AddOrUpdate(auth1);
        wtStore.SaveChangesAsync().Wait();

        var startNode = new StartNode(draf, workProcess);
        draf.NextNode = auth1;
        wtStore.AddOrUpdate(startNode);
        wtStore.AddOrUpdate(draf);
        wtStore.AddOrUpdate(auth1);
        wtStore.SaveChangesAsync().Wait();

        return workProcess;
    }

    private static UserViewModel Creator = new UserViewModel()
    {
        UserName = "creator",
    };
    /// <summary>
    ///     当再次进入的时候，前一次的user 为Default=true
    /// </summary>
    [Fact]
    public void TestNextUsers()
    {
        var sp = CreateServiceProvider();
        var wp = SimpleAuth(sp);
        var workflowManager = sp.GetRequiredService<WorkflowManager>();
        var startResult = workflowManager.Start(new ProcessInstance(wp, "creator"), Creator);
        var workActivityId = startResult.WorkActivities.First().WorkActivityId;
        var submit = new WorkflowResolveSubmit
        {
            Command = "提交"
        };
        var nextUserResult = workflowManager.Resolve(workActivityId,submit, Creator, true);
        Assert.True(nextUserResult.Success);

        Assert.Equal(2, nextUserResult.WorkActivities.Count());

        var user1 = nextUserResult.WorkActivities.First(_ => _.DisposeUser == "user1");
        Assert.Equal("user1", user1.DisposeUser);


        var user2 = nextUserResult.WorkActivities.First(_ => _.DisposeUser == "user2");
        Assert.Equal("user2", user2.DisposeUser);
    }
}