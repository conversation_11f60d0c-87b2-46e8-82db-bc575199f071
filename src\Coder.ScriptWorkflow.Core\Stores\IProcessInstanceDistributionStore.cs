﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IProcessInstanceDistributionStore
{
    /// <summary>
    /// </summary>
    IQueryable<ProcessInstanceDistribution> ProcessInstanceDistributions { get; }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<ProcessInstanceDistribution> GetByIdAsync(int id);

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task Delete(int id);

    /// <summary>
    /// </summary>
    /// <param name="item"></param>
    void Delete(ProcessInstanceDistribution item);

    /// <summary>
    /// </summary>
    /// <param name="distribution"></param>
    /// <returns></returns>
    void Update(ProcessInstanceDistribution distribution);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <param name="userName"></param>
    /// <returns></returns>
    ProcessInstanceDistribution GetByUser(int processInstance, string userName);

    /// <summary>
    /// </summary>
    /// <param name="processInstance"></param>
    /// <returns></returns>
    IEnumerable<ProcessInstanceDistribution> FindBy(ProcessInstance processInstance);
}