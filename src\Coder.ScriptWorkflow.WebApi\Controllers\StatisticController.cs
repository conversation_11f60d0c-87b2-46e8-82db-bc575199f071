﻿using System.Threading.Tasks;
using Coder.ScriptWorkflow.Cache;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     统计接口
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
public class StatisticController : Controller
{
    private readonly StatisticCache _cache;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="cache"></param>
    public StatisticController(StatisticCache cache)
    {
        _cache = cache;
    }
    /// <summary>
    /// 获取当前登录用户 在途工单/总工单。
    /// </summary>
    /// <param name="store"></param>
    /// <param name="forceBuild"></param>
    /// <returns></returns>
    [HttpGet("my-orders")]
    public async Task<IActionResult> MyWorks([FromServices] IProcessInstanceStore store, [FromQuery] bool? forceBuild)
    {
        var processInstanceTotal = await _cache.TryOnProcessing(User.Identity.Name, store, forceBuild ?? false);
        var total = await _cache.ProcessInstanceAllCount(User.Identity.Name, store, forceBuild ?? false);

        return Ok(new
        {
            processing = processInstanceTotal,
            total
        }.ToSuccess());
    }
}