﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流定义接口
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize]
public class PerformerController : Controller
{
    private readonly PerformerManager _performerManager;

    /// <summary>
    ///     /
    /// </summary>
    /// <param name="performerManager"></param>
    public PerformerController(PerformerManager performerManager)
    {
        _performerManager = performerManager;
    }

    /// <summary>
    /// </summary>
    /// <param name="performerType"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("get/{performerType}/{key}")]
    [ProducesDefaultResponseType(typeof(IEnumerable<AssignUser>))]
    public IActionResult GetUsers(string performerType, string key)
    {
        IEnumerable<AssignUser> users = null;
        switch (performerType.ToLower())
        {
            case "org":
                users = _performerManager.GetByOrgs(new[] { key }).Result;
                break;
            case "role":
                users = _performerManager.GetByRoles(new[] { key }).Result;
                break;
            case "user":
                users = _performerManager.GetByUserNamesAsync(new[] { key }).Result;
                break;
        }

        return Ok(users.ToSuccess());
    }

    /// <summary>
    ///     查询
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("search")]
    [ProducesDefaultResponseType(typeof(IEnumerable<Performer>))]
    public IActionResult Searcher([FromQuery] PerformerSearcher searcher)
    {
        var result = _performerManager.SearchAsync(searcher).Result;

        return Ok(result.ToSuccess());
    }


    /// <summary>
    ///     查询
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("count")]
    [ProducesDefaultResponseType(typeof(IEnumerable<Performer>))]
    public IActionResult Count([FromQuery] PerformerSearcher searcher)
    {
        var r = _performerManager.CountAsync(searcher).Result;
        return Ok(r.ToSuccess());
    }
}