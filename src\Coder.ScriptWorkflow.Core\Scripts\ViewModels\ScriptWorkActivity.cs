﻿namespace Coder.ScriptWorkflow.Scripts.ViewModels;

/// <summary>
/// </summary>
public class ScriptWorkActivity
{
    /// <summary>
    /// </summary>
    public ScriptWorkActivity()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="wa"></param>
    /// <summary>
    ///     处理时间
    /// </summary>
    public NiL.JS.BaseLibrary.Date? DisposeTime { get; set; }

    /// <summary>
    ///     处理人中文名
    /// </summary>
    public string DisposeUserName { get; set; }

    /// <summary>
    ///     命令
    /// </summary>
    public string Command { get; set; }

    /// <summary>
    /// </summary>
    public string WorkTaskName { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     工作活动状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// </summary>
    public string DisposeUser { get; set; }

    /// <summary>
    /// </summary>
    public string Comment { get; set; }
}