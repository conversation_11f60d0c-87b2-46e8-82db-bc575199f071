﻿using System;
using System.IO;
using Coder.ScriptWorkflow.Scripts.Plugins;

namespace Coder.ScriptWorkflow.Scripts.Plugin.MySql;

/// <summary>
/// </summary>
public class MysqlPlugin : IPlugin
{
    private readonly string _name = "mysql";

    /// <summary>
    /// </summary>
    public string Name
    {
        get => _name;
        set => throw new ArgumentOutOfRangeException(nameof(value), "mysql 插件不支持自定义变量名称");
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(IWorkflowContext context)
    {
        return new MysqlExecute(context);
    }

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o)
    {
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined()
    {
        var stream = typeof(MysqlPlugin).Assembly
            .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.MySql.define.d.ts");
        using var reader = new StreamReader(stream);
        var txt = reader.ReadToEnd();
        return txt;
    }
}