﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow;

/// <summary>
///     工作流实例
/// </summary>
public class ProcessInstance
{
    private IList<FileAttach> _files;
    private string _form;

    /// <summary>
    ///     工作实例
    /// </summary>
    protected ProcessInstance()
    {
        CreateTime = DateTimeOffset.Now;
        Status = ProcessInstanceStatus.Created;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess">工作流定义</param>
    /// <param name="user">创建用户</param>
    /// <exception cref="ArgumentNullException">workProcess / user 为空</exception>
    public ProcessInstance(WorkProcess workProcess, string user) : this()
    {
        WorkProcess = workProcess ?? throw new ArgumentNullException(nameof(workProcess));
        Creator = user ?? throw new ArgumentNullException(nameof(user));
        IsDebug = false;
        IsDelete = false;
    }
    /// <summary>
    /// 是否时调试实例。
    /// </summary>
    public bool IsDebug { get; set; } = false;

    public bool IsDelete { get; set; } = false;

    /// <summary>
    ///     已经走了多少个活动
    /// </summary>
    public int WorkActivityCount { get; internal set; }

    /// <summary>
    ///     获取工作流定义
    /// </summary>
    public virtual WorkProcess WorkProcess { get; protected set; }

    /// <summary>
    ///     执行优先级别
    /// </summary>
    public Priority Priority { get; set; } = Priority.Normal;

    /// <summary>
    ///     工作流实例主题
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    ///     工作流创建人
    /// </summary>
    public string Creator { get; protected set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     工作流状态
    /// </summary>
    public ProcessInstanceStatus Status { get; protected set; }

    /// <summary>
    ///     完成时间
    /// </summary>
    public DateTimeOffset? FinishTime { get; protected set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTimeOffset CreateTime { get; protected set; }

    /// <summary>
    ///     开始时间
    /// </summary>
    public DateTimeOffset? StartTime { get; protected set; }

    /// <summary>
    ///     工单编码
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    ///     挂起事件
    /// </summary>
    public DateTimeOffset? SuspendTime { get; protected set; }

    /// <summary>
    ///     挂起原因。
    /// </summary>
    public string SuspendComment { get; set; }

    /// <summary>
    ///     工作流备注
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    ///     form
    /// </summary>
    public string Form
    {
        get
        {
            if (string.IsNullOrEmpty(_form))
                return _form = "{}";
            return _form;
        }
        protected set => _form = value;
    }

    /// <summary>
    ///     文件列表
    /// </summary>
    public virtual IList<FileAttach> Files => _files ??= new List<FileAttach>();

    /// <summary>
    ///     用户显示名称。
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    ///     是否能够被删除。
    /// </summary>
    public bool CanDelete => WorkActivityCount < WorkProcess.CanBeDeleteWorkActivityCount;


    /// <summary>
    ///     取消
    /// </summary>
    public virtual void Cancel()
    {
        if (Status == ProcessInstanceStatus.Completed)
            throw new WorkflowException($"取消失败,process已经处于{Status},不能被取消.");
        Status = ProcessInstanceStatus.Cancel;
        FinishTime = DateTimeOffset.Now;
    }


    /// <summary>
    ///     工作流完成，需要执行的动作
    /// </summary>
    public virtual void Complete()
    {
        Status = ProcessInstanceStatus.Completed;
        FinishTime = DateTimeOffset.Now;
    }

    /// <summary>
    ///     挂起
    /// </summary>
    /// <param name="comment"></param>
    public virtual void Suspend(string comment = null)
    {
        if (Status == ProcessInstanceStatus.Completed
            || Status == ProcessInstanceStatus.Cancel
            || Status == ProcessInstanceStatus.Created
           )
            throw new WorkflowException($"挂起失败,process已经处于{Status},不能被挂起.");

        if (Status != ProcessInstanceStatus.Suspend)
        {
            Status = ProcessInstanceStatus.Suspend;
            SuspendTime = DateTimeOffset.Now;
            SuspendComment = comment;
        }
    }

    /// <summary>
    ///     恢复
    /// </summary>
    internal void Resume()
    {
        if (Status == ProcessInstanceStatus.Suspend)
            Status = ProcessInstanceStatus.Processing;
    }
    /// <summary>
    ///     处理完成后，重新处理
    /// </summary>
    public void ProcessingByCompleted()
    {
        if (Status == ProcessInstanceStatus.Completed)
            Status = ProcessInstanceStatus.Processing;
    }


    /// <summary>
    ///   处理中改为已完成
    /// </summary>
    public void CompletedByProcessing()
    {
        if (Status == ProcessInstanceStatus.Processing)
            Status = ProcessInstanceStatus.Completed;
    }

    /// <summary>
    ///     启动工作流实例
    /// </summary>
    /// <returns></returns>
    public virtual bool Start()
    {
        if (Status != ProcessInstanceStatus.Created)
            throw new WorkflowException($"启动失败,process已经处于{Status},不能启动.");
        Status = ProcessInstanceStatus.Processing;
        StartTime = DateTimeOffset.Now;

        return true;
    }

    /// <summary>
    ///     清除内置的变量
    /// </summary>
    /// <param name="o2"></param>
    private void HandleJsonFormData(JObject o2)
    {
        var removeKeys = new List<string>();
        foreach (var p in o2)
        foreach (var removeKeyPrefix in FormVariableDefines.FormInjectVariablePrefixNames)
            if (p.Key.StartsWith(removeKeyPrefix))
                removeKeys.Add(p.Key);

        foreach (var p in removeKeys) o2.Remove(p);
    }

    /// <summary>
    ///     合并json
    /// </summary>
    /// <param name="formSubmit"></param>
    public void MergeForm(ProcessInstanceFormSubmit formSubmit)
    {
        var jsObject = formSubmit.FillSelfByJsonForm();
        MergeForm(jsObject);
    }


    /// <summary>
    ///     合并
    /// </summary>
    /// <param name="jsonObject"></param>
    public void MergeForm(JObject jsonObject)
    {
        if (jsonObject == null) throw new ArgumentNullException(nameof(jsonObject));

        if (_form == null)
        {
            _form = JsonConvert.SerializeObject(jsonObject);
            return;
        }


        var o1 = JObject.Parse(_form);

        HandleJsonFormData(jsonObject);

        o1.Merge(jsonObject, new JsonMergeSettings
        {
            // union array values together to avoid duplicates
            MergeArrayHandling = MergeArrayHandling.Replace
        });

        _form = JsonConvert.SerializeObject(o1);
    }

    /// <summary>
    ///     覆盖
    /// </summary>
    /// <param name="json"></param>
    public void ReplaceForm(string json)
    {
        Form = json;
    }

    /// <summary>
    /// </summary>
    public class FileAttach
    {
        /// <summary>
        ///     文件id
        /// </summary>
        public int Id { get; }

        /// <summary>
        ///     文件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        ///     文件系统的id
        /// </summary>
        public string FileId { get; set; }

        /// <summary>
        ///     文件类型
        /// </summary>
        public FileType FileType { get; set; } = FileType.附件;

        /// <summary>
        ///     创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// </summary>
        public virtual ProcessInstance ProcessInstance { get; set; }
    }
}

/// <summary>
/// </summary>
public enum FileType
{
    /// <summary>
    /// </summary>
    附件,

    /// <summary>
    /// </summary>
    正文
}