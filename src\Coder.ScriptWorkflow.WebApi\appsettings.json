﻿{
  "DbType": "SQLITE",
  "ConnectionStrings": {
    "mysql": "server=mysql;port=3306;database=coder_members;user=root;password=*********;CharSet=utf8;",
    "mssql": "Server=localhost\\SQLEXPRESS;Database=coder_members;Trusted_Connection=True;",
    "redis": "redis:6379,defaultDatabase=2"
  },
  "gateway": "http://gateway:8080",


  "HumanResourceServer": "http://gateway:8080/humanResource", //访问人事服务url


  "TokenService": {
    "client": "ScriptWorkflow",
    "SecretKey": "316"
  },
  "NLog": {
    "RemoveLoggerFactoryFilter": false,
    "LokiHost": "http://loki:3100" //docker 的名字
  },

  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Warning",
      "Microsoft.AspNetCore.*": "Warning"
    }
  },
  "AllowedHosts": "*",


  "ConsulOption": {
    "ConsulServer": "http://consul:8500",
    "ServiceHost": "_HOST_", /* 要注册的服务 url */
    "ServicePort": -1, /* 要注册服务的访问端口 */
    "ServiceName": "coder_swf", /* 服务名称，集群的时候采用这个名称来获取服务 */
    "ServiceId": "coder_swf-*", /* 服务id 必须唯一，用于记录那个服务提供者出现问题 */
    "Tags": [ "工作流", "脚本" ], /* 可选 日志*/
    "HealthCheckUrl": "http://_HOST_:_PORT_/Health/Status",
    "Enable": true
  }
}