﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ProcessInstanceDistributionMapping : IEntityTypeConfiguration<ProcessInstanceDistribution>
{
    /// <summary>
    /// </summary>
    private const string DateFormat = "yyyy-MM-dd HH:mm:ss";

    /// <summary>
    ///     是否为sqlite
    /// </summary>
    private readonly bool _isSqlite;

    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    /// <param name="isSqlite">现在数据库是否Sqlite</param>
    public ProcessInstanceDistributionMapping(string prefix, bool isSqlite)
    {
        _prefix = prefix;
        _isSqlite = isSqlite;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ProcessInstanceDistribution> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();
        builder.Property(_ => _.UserName).HasMaxLength(20);
        builder.Property(_ => _.UserRealName).HasMaxLength(20);

        builder.HasOne(_ => _.ProcessInstance).WithMany().HasForeignKey("ProcessInstanceId");
        builder.Property(_ => _.HasRead);
        var createTime = builder.Property(_ => _.CreateTime);
        var readTime = builder.Property(_ => _.ReadTime);

        builder.ToTable($"{_prefix}_PI_Distribution").HasComment("工作流程分发记录");

        if (_isSqlite)
        {
            readTime.HasConversion(f => f != null ? f.Value.ToString(DateFormat) : null,
                strDate => strDate == null ? null : DateTimeOffset.Parse(strDate));
            createTime.HasConversion(f => f != DateTimeOffset.MinValue ? f.ToString(DateFormat) : null,
                strDate => strDate == null ? DateTimeOffset.MinValue : DateTimeOffset.Parse(strDate));
        }
    }
}