﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.WorkTaskCommand;

namespace Coder.ScriptWorkflow.ViewModels.WorkActivities;

/// <summary>
///     工作活动
/// </summary>
public class WorkActivityViewModel : WorkActivityListItemViewModel
{
    /// <summary>
    /// </summary>
    public WorkActivityViewModel()
    {
    }

    /// <summary>
    /// </summary>
    public NextTaskPerformers NextTaskPerformers { get; set; }

    ///// <summary>
    ///// 派发模式
    ///// </summary>
    //public AssignType AssignType { get; set; }

    /// <summary>
    ///     表单数据.
    /// </summary>
    public Object Form { get; set; }


    /// <summary>
    /// </summary>
    public IEnumerable<WorkTaskCommandViewModel> Commands { get; set; }

    /// <summary>
    ///     form设计
    /// </summary>
    public string FormDesign { get; set; }

    /// <summary>
    ///     工作任务ID
    /// </summary>
    public int WorkProcessId { get; set; }


    /// <summary>
    ///     活动分组，同一个workTask 分发多个Group的时候，都会有多个分组
    /// </summary>
    public string TaskCreatingGroup { get; set; }
}