﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.ViewModels.WorkActivities;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
internal class WorkActivityStore<T> : IWorkActivityStore where T : DbContext


{
    private static readonly WorkActivityStatus[] _processing =
    {
        WorkActivityStatus.Processing,
        WorkActivityStatus.UnAssign
    };

    private readonly T _dbContext;

    private IDbContextTransaction _transaction;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public WorkActivityStore(T dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    /// <summary>
    /// </summary>
    public IQueryable<WorkActivity> WorkActivities => _dbContext.Set<WorkActivity>();

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    public WorkActivity GetById(int id)
    {
        return WorkActivities.Include(_ => _.ProcessInstance)
            .ThenInclude(_ => _.WorkProcess)
            .Include(_ => _.WorkTask)
            .FirstOrDefault(_ => _.Id == id);
    }

    /// <summary>
    /// </summary>
    /// <param name="wa"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void AddOrUpdate(WorkActivity wa)
    {
        if (wa == null) throw new ArgumentNullException(nameof(wa));
        _dbContext.Update(wa);
    }

    /// <summary>
    /// </summary>
    /// <param name="waTaskCreatingGroup"></param>
    /// <param name="notIncludeWorkActivityId"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public IEnumerable<WorkActivity> GetByGroup(string waTaskCreatingGroup, int? notIncludeWorkActivityId = null)
    {
        if (waTaskCreatingGroup == null) throw new ArgumentNullException(nameof(waTaskCreatingGroup));

        return notIncludeWorkActivityId == null
            ? WorkActivities.Where(_ => _.TaskCreatingGroup == waTaskCreatingGroup).ToList()
            : WorkActivities.Where(_ => _.TaskCreatingGroup == waTaskCreatingGroup && _.Id != notIncludeWorkActivityId.Value);
    }

    /// <summary>
    /// </summary>
    /// <param name="was"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void AddOrUpdate(IEnumerable<WorkActivity> was)
    {
        if (was == null) throw new ArgumentNullException(nameof(was));
        _dbContext.UpdateRange(was);
    }

    /// <summary>
    /// </summary>
    public void SaveChanges()
    {
        _dbContext.SaveChanges();
    }

    /// <summary>
    /// </summary>
    /// <param name="waProcessInstance"></param>
    /// <param name="workTaskName"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public IEnumerable<WorkActivity> GetLastWorkActivity(ProcessInstance waProcessInstance, string workTaskName)
    {
        if (waProcessInstance == null) throw new ArgumentNullException(nameof(waProcessInstance));
        if (workTaskName == null) throw new ArgumentNullException(nameof(workTaskName));
        var activity = WorkActivities
            .Where(f => f.WorkTask.Name == workTaskName && f.ProcessInstance.Id == waProcessInstance.Id)
            .OrderByDescending(f => f.CreateTime)
            .FirstOrDefault();

        if (activity != null)
            return WorkActivities
                .Where(_ => _.TaskCreatingGroup == activity.TaskCreatingGroup &&
                            _.ProcessInstance.Id == waProcessInstance.Id && _.WorkTask.Name == workTaskName
                )
                .ToList();
        return new List<WorkActivity>();
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    public IEnumerable<WorkActivity> GetInProcessWorkActivity(int processInstanceId)
    {
        var was = WorkActivities.Where(_ =>
            _processing.Contains(_.Status) && _.ProcessInstance.Id == processInstanceId);
        return was;
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    public IEnumerable<WorkActivity> GetSuspendProcessWorkActivity(int processInstanceId)
    {
        var was = WorkActivities.Where(_ =>
            _.Status == WorkActivityStatus.Suspend && _.ProcessInstance.Id == processInstanceId);
        return was;
    }


    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    public IEnumerable<WorkActivity> Find(WorkActivitySearcher searcher, string currentUser)
    {
        if (searcher == null) throw new ArgumentNullException(nameof(searcher));


        var query = MakeQuery(searcher, currentUser);
        var query1 = query
            .OrderByDescending(_ => _.Priority)
            .OrderBy(_ => _.Status).ThenByDescending(_ => _.CreateTime);

        return searcher.ForPager(query1).ToList();
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser">当前登录用户</param>
    /// <returns></returns>
    public int Count(WorkActivitySearcher searcher, string currentUser)
    {
        if (searcher == null) throw new ArgumentNullException(nameof(searcher));
        var query = MakeQuery(searcher, currentUser);
        return query.Count();
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivities"></param>
    public void Remove(IEnumerable<WorkActivity> workActivities)
    {
        _dbContext.RemoveRange(workActivities);
    }

    /// <summary>
    /// </summary>
    /// <param name="workActivity"></param>
    /// <returns></returns>
    public IEnumerable<string> WasRun(WorkActivity workActivity)
    {
        if (workActivity == null) throw new ArgumentNullException(nameof(workActivity));
        var lastSameWorkTaskActivityId = _dbContext.Set<WorkActivity>()
            .Where(_ => _.ProcessInstance.Id == workActivity.ProcessInstance.Id && _.WorkTask.Id == workActivity.WorkTask.Id)
            .Min(_ => _.Id);

        if (lastSameWorkTaskActivityId == 0)
            return Array.Empty<string>();

        return _dbContext.Set<WorkActivity>().Where(_ => _.ProcessInstance.Id == workActivity.ProcessInstance.Id
                                                         && _.Id < lastSameWorkTaskActivityId)
            .Select(_ => _.WorkTask.Name).Distinct();
    }
    [return: MaybeNull]
    public Task<WorkActivity> GetByIdAsync(int id)
    {
        return _dbContext.Set<WorkActivity>().FirstOrDefaultAsync(_ => _.Id == id);
    }

    public void Delete(IEnumerable<WorkActivity> workActivities)
    {
         _dbContext.RemoveRange(workActivities);
    }

    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// </summary>
    public void TryBeginTransaction()
    {
        _transaction = _dbContext.Database.CurrentTransaction != null
            ? null
            : _dbContext.Database.BeginTransaction();
    }

    /// <summary>
    /// </summary>
    public void Commit()
    {
        _transaction?.Commit();
    }

    /// <summary>
    /// </summary>
    public void Rollback()
    {
        _transaction?.Rollback();
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    private IQueryable<WorkActivity> MakeQuery(WorkActivitySearcher searcher, string currentUser)
    {
        if (searcher == null) throw new ArgumentNullException(nameof(searcher));


        var condition = PredicateBuilder.New<WorkActivity>(_ => searcher.Statuses == null || searcher.Statuses.Length == 0 || searcher.Statuses.Contains(_.Status));

        if (searcher.WorkProcessName != null && searcher.WorkProcessName.Length != 0) condition = condition.And(_ => searcher.WorkProcessName.Contains(_.ProcessInstance.WorkProcess.Name));
        if (searcher.WorkProcessId != null) condition = condition.And(_ => _.ProcessInstance.WorkProcess.Id == searcher.WorkProcessId);

        if (!string.IsNullOrWhiteSpace(searcher.Subject)) condition = condition.And(_ => _.ProcessInstance.Subject.Contains(searcher.Subject));

        if (!string.IsNullOrWhiteSpace(searcher.Creator)) condition = condition.And(_ => _.ProcessInstance.Creator == searcher.Creator || _.ProcessInstance.CreatorName == searcher.Creator);

        if (searcher.CreateTimeStart != null) condition = condition.And(_ => searcher.CreateTimeStart <= _.ProcessInstance.CreateTime);
        if (searcher.CreateTimeEnd != null) condition = condition.And(_ => searcher.CreateTimeEnd >= _.ProcessInstance.CreateTime);
        //手机端查询专用Name、StartTime
        if (!string.IsNullOrWhiteSpace(searcher.Name)) condition = condition.And(_ => _.ProcessInstance.WorkProcess.Name.Contains(searcher.Name));
        if (searcher.StartTime != null)
        {
            var startTimeStar = DateHelp.GetDateS(searcher.StartTime);
            var startTimeEnd = DateHelp.GetDateE(searcher.StartTime);
            condition = condition.And(_ => startTimeStar <= _.ProcessInstance.CreateTime);
            condition = condition.And(_ => startTimeEnd >= _.ProcessInstance.CreateTime);
        }

        if (!string.IsNullOrEmpty(searcher.Number)) condition = condition.And(_ => _.ProcessInstance.Number == searcher.Number);

        var regexCondition = PredicateBuilder.New<WorkActivity>(false);

        if (!string.IsNullOrEmpty(currentUser)) regexCondition = regexCondition.And(_ => _.DisposeUser == currentUser);


        if (searcher.Orgs != null && searcher.Orgs.Length != 0)
        {
            var orgRegex = searcher.Orgs == null ? null : searcher.Orgs.Select(_ => $"{(int)PerformerType.Org}:{_}").ToArray();
            var pattern = string.Join('|', orgRegex);
            regexCondition.Or(_ => RegexE.IsMatch(_.AssignPerformers, pattern));
        }


        if (searcher.Roles != null && searcher.Roles.Length != 0)
        {
            var roleRegex = searcher.Roles == null ? null : searcher.Roles.Select(_ => $"{(int)PerformerType.Role}:{_}").ToArray();
            var pattern = string.Join('|', roleRegex);
            regexCondition.Or(_ => RegexE.IsMatch(_.AssignPerformers, pattern));
        }

        //if (!string.IsNullOrEmpty(currentUser))
        //{
        //    var userRegex = string.IsNullOrEmpty(currentUser) ? null : $"{(int)PerformerType.User}:{currentUser}:";
        //    regexCondition.Or(_ => RegexE.IsMatch(_.AssignPerformers, userRegex));
        //}

        condition = condition.And(regexCondition);

        var query = WorkActivities.Where(condition);
        return query;
    }
}