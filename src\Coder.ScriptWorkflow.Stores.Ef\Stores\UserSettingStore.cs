﻿using System.Threading.Tasks;
using Coder.ScriptWorkflow.Settings;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
internal class UserSettingStore<T> : IUserSettingStore where T:DbContext
{
    private readonly T _context;

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    public UserSettingStore(T context)
    {
        _context = context;
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task Delete(int id)
    {
        var entity = await GetByIdAsync(id);
        _context.Remove(entity);
    }

    public void AddOrUpdate<TSetting>(TSetting setting) where TSetting : SwfSetting
    {
        _context.Update(setting);
    }




    /// <summary>
    /// </summary>
    /// <returns></returns>
    public Task SaveChangesAsync()
    {
        return _context.SaveChangesAsync();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public Task<SwfMakeTagsSetting> GetMakeTagSetting()
    {
        return _context.Set<SwfMakeTagsSetting>().FirstOrDefaultAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task<SwfSetting> GetByIdAsync(int id)
    {
        return _context.Set<SwfSetting>().FirstOrDefaultAsync(_ => _.Id == id);
    }
}