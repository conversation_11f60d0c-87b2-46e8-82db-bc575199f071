﻿using System;
using Coder.ScriptWorkflow.Scripts.Plugin.JsHttpClient;
using Coder.ScriptWorkflow.UnitTest.Http.Helper;
using NiL.JS.Core;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Plugins;

/// <summary>
/// </summary>
public class JsHttpClientTest : IClassFixture<CoderWebAppFactory>
{
    private readonly CoderWebAppFactory _factory;

    /// <summary>
    /// </summary>
    /// <param name="factory"></param>
    public JsHttpClientTest(CoderWebAppFactory factory)
    {
        _factory = factory;
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void TestGet()
    {
        var jsHttpClient = new JsHttpClient(_factory.CreateClient());

        var con = new Context();
        con.DefineVariable("jsHttpClient")
            .Assign(GlobalContext.CurrentGlobalContext.ProxyValue(jsHttpClient));

        var code = @"var getQueryString={a:'string',b:11,c:1.2,d:[1,2,3,4]};
var a= jsHttpClient.GetJson('JsHttpClient/list-json',getQueryString);


";
        con.Eval(code);
        var aObody = con.GetVariable("a");

        Assert.Equal("string", aObody["a"].Value.ToString());
        Assert.Equal(11, Convert.ToInt32(aObody["b"].Value));
        Assert.Equal(1.2, Convert.ToDouble(aObody["c"].Value));
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void TestPostJson()
    {
        var jsHttpClient = new JsHttpClient(_factory.CreateClient());

        var con = new Context();
        con.DefineVariable("jsHttpClient")
            .Assign(GlobalContext.CurrentGlobalContext.ProxyValue(jsHttpClient));

        var code = @"var getQueryString={a:'string',b:11,c:1.2,d:[1,2,3]};
var a= jsHttpClient.PostJson('JsHttpClient/PostJSON',getQueryString);


";
        con.Eval(code);
        var aObody = con.GetVariable("a");

        Assert.Equal("string", aObody["a"].Value.ToString());
        Assert.Equal(11, Convert.ToInt32(aObody["b"].Value));
        Assert.Equal(1.2, Convert.ToDouble(aObody["c"].Value));
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void TestPutJson()
    {
        var jsHttpClient = new JsHttpClient(_factory.CreateClient());

        var con = new Context();
        con.DefineVariable("jsHttpClient")
            .Assign(GlobalContext.CurrentGlobalContext.ProxyValue(jsHttpClient));

        var code = @"var getQueryString={a:'string',b:11,c:1.2};
var a= jsHttpClient.PutJson('JsHttpClient/PutJSON',getQueryString);


";
        con.Eval(code);
        var aObody = con.GetVariable("a");

        Assert.Equal("string", aObody["a"].Value.ToString());
        Assert.Equal(11, Convert.ToInt32(aObody["b"].Value));
        Assert.Equal(1.2, Convert.ToDouble(aObody["c"].Value));
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void CallDeleteJson()
    {
        var jsHttpClient = new JsHttpClient(_factory.CreateClient());

        var con = new Context();
        con.DefineVariable("jsHttpClient")
            .Assign(con.GlobalContext.ProxyValue(jsHttpClient));

        var code = @"var a= jsHttpClient.Delete('JsHttpClient/1');";
        con.Eval(code);
        var aObody = con.GetVariable("a");
        Assert.Equal("message", aObody["message"].Value.ToString());
        Assert.Equal("True", aObody["success"].Value.ToString());
    }
}