﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.ViewModels;

public class GlobalScriptViewModel
{
    public string Name { get; set; }
    public int Id { get; set; }
}

/// <summary>
///     简易的工作流活动
/// </summary>
public class SimpleWorkActivity
{
    /// <summary>
    /// </summary>
    public string DisposeUser { get; set; }

    /// <summary>
    /// </summary>
    public string DisposeUserName { get; set; }

    /// <summary>
    /// </summary>

    public int WorkActivityId { get; set; }

    /// <summary>
    /// </summary>
    public IEnumerable<Performer> AssignPerformers { get; set; }

    /// <summary>
    ///     工作活动状态
    /// </summary>
    public WorkActivityStatus Status { get; set; }

    /// <summary>
    ///     工作任务名称
    /// </summary>
    public string WorkTaskName { get; set; }
}