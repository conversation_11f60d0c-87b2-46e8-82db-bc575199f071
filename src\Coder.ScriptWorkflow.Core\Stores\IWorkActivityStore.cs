﻿using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.ViewModels.WorkActivities;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface IWorkActivityStore
{
    /// <summary>
    /// </summary>
    IQueryable<WorkActivity> WorkActivities { get; }


    /// <summary>
    ///     通过WorkActivity‘s id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkActivity GetById(int id);


    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="wa"></param>
    void AddOrUpdate([NotNull] WorkActivity wa);


    /// <summary>
    ///     通过TaskCreating的分组
    /// </summary>
    /// <param name="waTaskCreatingGroup"></param>
    /// <param name="notIncludeWorkActivityId"></param>
    /// <returns></returns>
    IEnumerable<WorkActivity> GetByGroup(string waTaskCreatingGroup, int? notIncludeWorkActivityId = null);

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="was"></param>
    void AddOrUpdate(IEnumerable<WorkActivity> was);

    /// <summary>
    ///     flush cache
    /// </summary>
    void SaveChanges();

    /// <summary>
    ///     获取相同的WorkTaskName的上一次的工作活动。
    /// </summary>
    /// <param name="waProcessInstance"></param>
    /// <param name="workTaskName"></param>
    /// <returns></returns>
    IEnumerable<WorkActivity> GetLastWorkActivity(ProcessInstance waProcessInstance, string workTaskName);

    /// <summary>
    ///     获取还在处理中的工作活动
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    IEnumerable<WorkActivity> GetInProcessWorkActivity(int processInstanceId);

    /// <summary>
    ///     获取休眠中的工作活动
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    IEnumerable<WorkActivity> GetSuspendProcessWorkActivity(int processInstanceId);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    /// <returns></returns>
    IEnumerable<WorkActivity> Find(WorkActivitySearcher searcher, string currentUser);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentName"></param>
    /// <returns></returns>
    int Count(WorkActivitySearcher searcher, string currentName);

    /// <summary>
    ///     删除
    /// </summary>
    /// <param name="workActivities"></param>
    void Remove(IEnumerable<WorkActivity> workActivities);

    /// <summary>
    ///     确认 输入的WorkTask 已经跑过审查。
    /// </summary>
    /// <param name="workActivity"></param>
    /// <returns></returns>
    IEnumerable<string> WasRun(WorkActivity workActivity);
    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return:MaybeNull]
    Task<WorkActivity> GetByIdAsync(int id);
    /// <summary>
    /// 删除工作活动
    /// </summary>
    /// <param name="workActivities"></param>

    void Delete(IEnumerable<WorkActivity> workActivities);

    Task SaveChangesAsync();
}