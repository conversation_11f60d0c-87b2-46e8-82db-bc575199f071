﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.Authentication;
using Coder.FileSystem.Clients;
using Coder.FileSystem.ViewModels;
using Coder.Member;
using Coder.Member.Clients;
using Coder.PreventDuplicateSubmit;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.Tags;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Permissions;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Coder.ScriptWorkflow.ViewModels.WorkActivities;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;
using Coder.ScriptWorkflow.ViewModels.WorkTaskCommand;
using Coder.Token.Clients;
using Coder.Token.ViewModels;
using Coder.WebHttpClient;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流操作api
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize]
public class WorkflowController : Controller
{
    private readonly ApplicationDbContext _context;

    private readonly IHttpFileManager _fileManager;
    private readonly ILogger<WorkflowController> _logger;

    /// private readonly HttpSimpleFileManager _fileManager;
    private readonly IProcessInstanceStore _processInstanceStore;


    private readonly IWorkActivityStore _workActivityStore;

    /// <summary>
    /// </summary>
    private readonly WorkflowManager _workflowManager;

    private readonly WorkflowPermissionManager _workflowPermissionManager;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="workflowManager"></param>
    /// <param name="workActivityStore"></param>
    /// <param name="processInstanceStore"></param>
    /// <param name="fileManager"></param>
    /// <param name="context"></param>
    /// <param name="permissionManager"></param>
    /// <param name="logger"></param>
    public WorkflowController(WorkflowManager workflowManager,
        IWorkActivityStore workActivityStore,
        IProcessInstanceStore processInstanceStore,
        IHttpFileManager fileManager,
        ApplicationDbContext context,
        WorkflowPermissionManager permissionManager,
        ILogger<WorkflowController> logger
    )
    {
        _workflowManager = workflowManager;
        _workActivityStore = workActivityStore;
        _processInstanceStore = processInstanceStore;
        _fileManager = fileManager;
        _context = context;
        _workflowPermissionManager = permissionManager;
        _logger = logger;
    }

    /// <summary>
    ///     获取工作流实例
    /// </summary>
    /// <param name="id">工作流实例id</param>
    /// <returns></returns>
    [HttpGet("{id:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceViewModel>))]
    public IActionResult Get([FromRoute] int id)
    {
        var result = _processInstanceStore.GetById(id);

        if (result == null)
        {
            _logger.LogError("找不到id=" + id + "的工作流程实列。");
            return Ok();
        }

        if (!_workflowPermissionManager.HasReadPermissionAsync(result, User).Result)
            return Ok(new ProcessInstanceViewModel
            {
                Success = true,
                Message = "你无权访问本工单。"
            }.ToSuccess());

        var model = result.ToViewModel(_processInstanceStore);
        _workflowPermissionManager.SetProcessInstance(model, User);

        return Ok(model.ToSuccess());
    }

    /// <summary>
    ///     获取工作流实例
    /// </summary>
    /// <param name="number">工作流实例工单编码</param>
    /// <returns></returns>
    [HttpGet("get-by-code/{number}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceViewModel>))]
    public IActionResult GetByNumber([FromRoute] string number)
    {
        var result = _processInstanceStore.Get(number);

        if (result == null)
        {
            _logger.LogError("找不到Number=" + number + "的工作流程实列。");
            return Ok();
        }

        if (!_workflowPermissionManager.HasReadPermissionAsync(result, User).Result)
            return Ok(new ProcessInstanceViewModel
            {
                Success = true,
                Message = "你无权访问本工单。"
            }.ToSuccess());

        var model = result.ToViewModel(_processInstanceStore);
        _workflowPermissionManager.SetProcessInstance(model, User);

        return Ok(model.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [HttpGet("get-by-name/{name}")]
    [ProducesDefaultResponseType(typeof(SwfResult<WorkProcessViewModel>))]
    public async Task<IActionResult> GetByName([FromRoute] string name)
    {
        try
        {
            var result = await _workflowManager.GetByEffectNameAsync(name);
            if (result == null) result.ToError("你无权获取" + name + "工作流", -403);
            if (!_workflowPermissionManager.HasCreate(User, result.Name)) return Forbid();
            return Ok(result.ToSuccess());
        }
        catch (NotFoundWorkProcessException ex)
        {
            return NotFound(new
            {
                success = false,
                message = ex.Message
            });
        }
    }




    ///// <summary>
    /////     通过工单号获取流程实例
    ///// </summary>
    ///// <param name="number">工单号</param>
    ///// <returns></returns>
    //[HttpGet("{number}")]
    //[ProducesDefaultResponseType(typeof(ProcessInstanceViewModel))]
    //public IActionResult Get([FromRoute] string number)
    //{
    //    var result = _processInstanceStore.Get(number);
    //    if (!_workflowPermissionManager.HasReadPermissionAsync(result, User).Result)
    //        return Ok(new ProcessInstanceViewModel
    //        {
    //            Success = true,
    //            Message = "你无权访问本工单。"
    //        });
    //    return Ok(result.ToViewModel(_processInstanceStore));
    //}

    /// <summary>
    ///     对文件进行压缩。
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("pack/{id}")]
    [ProducesDefaultResponseType(typeof(SwfResult<string>))]
    public async Task<IActionResult> Pack([FromRoute] int id)
    {
        var pi = _processInstanceStore.GetById(id);
        if (pi == null) return NotFound(new { success = false, message = "没有找到id=" + id + "的工作流实例。" });
        if (!_workflowPermissionManager.HasReadPermissionAsync(pi, User).Result)
            return Ok(new ProcessInstanceViewModel
            {
                Success = true,
                Message = "你无权访问本工单。"
            });
        var result = await _fileManager.BuildPackage(new PackageMultiFileSubmit
        {
            CreateUser = User.Identity.Name,
            FileId = pi.Files.Select(_ => _.FileId).ToArray()
        });

        return Ok(result);
    }

    /// <summary>
    ///     获取工作活动
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("work-activity/{id:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<WorkActivityViewModel>))]
    public async Task<IActionResult> GetByWorkActionResult(int id)
    {
        var iem = _workActivityStore.GetById(id);
        if (iem == null) return Ok(new { success = false, message = "无法找到。" });

        var canDeposit = await _workflowPermissionManager.HasReadPermissionAsync(iem, User);
        if (!canDeposit) return Forbid();

        var viewModel = iem.ToViewModel();
        _workflowPermissionManager.SetWorkActivityPermission(viewModel, User);
        return Ok(viewModel.ToSuccess());
    }

    /// <summary>
    ///     列出所有与工作实例的工作活动
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="search"></param>
    /// <returns></returns>
    [HttpGet("list-work-activity-by-process-instance-id/{processInstanceId}")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<WorkActivityListItemViewModel>>))]
    [Authorize]
    public async Task<IActionResult> ListByProcessInstanceId([FromRoute] int processInstanceId,
        [FromQuery] ListWorkActivityByProcessInstanceSearch search)
    {
        var processInstance = _workflowManager.GetById(processInstanceId);
        var canDeposit = await _workflowPermissionManager.HasReadPermissionAsync(processInstance, User);
        if (!canDeposit) return Forbid();

        var query = _workActivityStore.WorkActivities.Where(workActivity =>
            workActivity.ProcessInstance.Id == processInstanceId &&
            (search.Statuses == null ||
             search.Statuses.Contains(workActivity.Status))
            && (search.WorkTask == workActivity.WorkTask.Name ||
                search.WorkTask == null));

        var result = query.Skip(search.GetSkip()).Take(search.GetTake()).ToList();
        var viewModel = result
            .Select(_ => _.ToListViewModel()).ToArray();
        return Ok(viewModel.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="search"></param>
    /// <returns></returns>
    [HttpGet("count-work-activity-by-process-instance-id/{processInstanceId}")]
    [Authorize]
    [ProducesDefaultResponseType(typeof(SwfResult<int>))]
    public IActionResult CountByProcessInstanceId([FromRoute] int processInstanceId,
        [FromQuery] ListWorkActivityByProcessInstanceSearch search)
    {
        var processInstance = _workflowManager.GetById(processInstanceId);
        var canDeposit = _workflowPermissionManager.HasReadPermissionAsync(processInstance, User).Result;
        if (!canDeposit) return Forbid();
        var query = _workActivityStore.WorkActivities.Where(workActivity =>
            workActivity.ProcessInstance.Id == processInstanceId &&
            (search.Statuses == null ||
             search.Statuses.Contains(workActivity.Status)) &&
            (search.WorkTask == workActivity.WorkTask.Name ||
             search.WorkTask == null));

        var result = query.Count();

        return Ok(result.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    [HttpGet("list-work-activity")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<WorkActivityListItemViewModel>>))]
    public IActionResult ListWorkActivity([FromQuery] WorkActivitySearcher searcher,
        [FromServices] WorkflowPermissionManager permissionManager)
    {
        SetPermissions(searcher, out var userName);
        var result = _workActivityStore.Find(searcher, userName);
        var viewModel = result.Select(_ => _.ToListViewModel()).ToArray();
        permissionManager.SetWorkActivitiesPermission(viewModel, User);
        return Ok(viewModel.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("count-work-activity")]
    [ProducesDefaultResponseType(typeof(SwfResult<int>))]
    public IActionResult CountWorkActivity([FromQuery] WorkActivitySearcher searcher)
    {
        SetPermissions(searcher, out var userName);
        var count = _workActivityStore.Count(searcher, userName);
        return Ok(count.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="permissionManager"></param>
    /// <param name="userClient"></param>
    /// <returns></returns>
    [HttpPost("create")]
    [ProducesDefaultResponseType(typeof(SwfResult<CreateProcessInstanceResult>))]
    [PreventDuplicateSubmit]
    [Authorize]
    public IActionResult Create([FromBody] CreateProcessInstanceSubmit submit,
        [FromServices] WorkflowPermissionManager permissionManager, [FromServices] IUserClient userClient)
    {
        var currentUser = User.ToDebuggerUser(userClient, submit.CreateUser);

        var userName = currentUser.Identity?.Name;
        var realName = currentUser.Claims.FirstOrDefault(claim => claim.Type == "real_name");

#if DEBUG
        if (string.IsNullOrEmpty(userName))
            userName = "admin";
#endif

        //检查

        var hasPermissionCreateInstance = permissionManager.HasCreate(currentUser, submit.WorkProcessName);

        if (!hasPermissionCreateInstance)
        {
            var msg = "你无权创建" + submit.WorkProcessName + "的流程。";
            return Ok(new CreateProcessInstanceResult
            {
                Success = false,
                Message = msg
            }.ToError(msg, -403));
        }


        var processInstance = _workflowManager.Create(submit, userName, realName?.Value);

        var result = new CreateProcessInstanceResult
        {
            ProcessInstanceStatus = processInstance.Status,
            Success = true,
            ProcessInstanceId = processInstance.Id,
            Message = "成功创建"
        };
        if (submit.Start)
        {
            var startResult = _workflowManager.Start(processInstance.Id, User.ToUserViewModel());
            processInstance = _workflowManager.GetById(processInstance.Id);
            result.ProcessInstanceStatus = processInstance.Status;
            result.StartResult = startResult;

            if (startResult.Success)
            {
                result.Message = "成功创建并启动";
            }
            else
            {
                result.Message += "但启动出现错误。错误内容是：" + startResult.Message;
                result.Success = false;
                result.ToError("失败。", -1);
            }
        }

        result.CreateUser = currentUser.Identity?.Name;
        return Ok(result.ToSuccess("成功创建"));
    }


    /// <summary>
    ///     推进到下一个工作活动
    /// </summary>
    /// <param name="id"></param>
    /// <param name="model"></param>
    /// <param name="workTaskStore"></param>
    /// <param name="userClient"></param>
    /// <returns></returns>
    [HttpPost("resolve/{id:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ResolveResult>))]
    [PreventDuplicateSubmit]
    public virtual IActionResult Resolve([FromRoute] int id,
        [FromBody] WorkflowResolveSubmit model, [FromServices] IWorkTaskStore workTaskStore,
        [FromServices] IUserClient userClient)
    {
        var currentUser = User.ToDebuggerUser(userClient, model.ResolveUser);

        try
        {
            var workActivity = _workflowManager.GetWorkActivityById(id);

            _workflowPermissionManager.CanResolve(workActivity, currentUser, out var vm);

            if (!vm.CanDisposeWorkActivity)
                return Ok(new ResolveResult
                {
                    Success = false,
                    Message = "工作活动已经被处理或你无权处理。"
                });

            var resolveResult = _workflowManager.Resolve(workActivity, model, currentUser.ToUserViewModel());
            return Ok(resolveResult.ToSuccess());
        }
        catch (WorkflowDefinedException ex)
        {
            return Ok(new { success = false, message = "设置错误:" + ex.Message }.ToSuccess());
        }
        catch (WorkflowException ex)
        {
            return Ok(new { success = false, message = ex.Message }.ToSuccess());
        }
    }

    /// <summary>
    ///     挂起一个工作流实例
    /// </summary>
    /// <param name="processInstanceId">工作流实例id</param>
    /// <param name="commentSubmit">挂起备注</param>
    /// <param name="userClient"></param>
    /// <returns></returns>
    [HttpPut("suspend/{processInstanceId:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceResolveResult>))]
    public IActionResult Suspend([FromRoute] int processInstanceId, [FromBody] SuspendCommentSubmit commentSubmit,
        [FromServices] IUserClient userClient)
    {
        var currentUser = User.ToDebuggerUser(userClient, commentSubmit.SuspendUser);

        var order = _workflowManager.Suspend(processInstanceId, commentSubmit.Comment, currentUser.ToUserViewModel());
        if (!_workflowPermissionManager.HasSuspend(currentUser, order))
            return Json(new ProcessInstanceResolveResult
            {
                Message = "你无权挂起工单。"
            }.ToSuccess());
        return Json(
            new ProcessInstanceResolveResult
            {
                Success = true,
                Message = "挂起成功",
                Status = order.Status
            }.ToSuccess());
    }

    /// <summary>
    ///     恢复工作流实例
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    [HttpPut("resume/{processInstanceId:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceResolveResult>))]
    public IActionResult Resume([FromRoute] int processInstanceId)
    {
        var order = _workflowManager.Resume(processInstanceId, User.ToUserViewModel());
        if (!_workflowPermissionManager.HasSuspend(User, order))
            return Json(new ProcessInstanceResolveResult
            {
                Success = false,
                Message = "你无权挂起工单。"
            }.ToSuccess());
        return Json(
            new
            {
                Success = true,
                Message = "恢复成功",
                order.Status
            }.ToSuccess());
    }

    /// <summary>
    ///     列出一个工作活动
    /// </summary>
    /// <param name="id"></param>
    /// <param name="all">是否显示全部历史</param>
    /// <returns></returns>
    [HttpGet("list-flow/{id:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<FlowViewModel>>))]
    public IActionResult List([FromRoute] int id, [FromQuery] bool all = false)
    {
        //流程中被系统关闭的工作项不显示 add by amen
        var processInstance = _workflowManager.GetById(id);
        if (!_workflowPermissionManager.HasReadPermissionAsync(processInstance, User).Result) return Forbid();
        var resultList = _workflowManager.GetFlow(id, all);
        return Json(resultList.ToSuccess());
    }


    /// <summary>
    ///     取消工作活动
    /// </summary>
    /// <param name="processInstanceId">工作流实例id</param>
    /// <returns></returns>
    [HttpPut("cancel/{processInstanceId:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<CancelProcessInstanceResult>))]
    public IActionResult Cancel([FromRoute] int processInstanceId)
    {
        //流程中被系统关闭的工作项不显示 add by amen
        var processInstance = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.HasCancel(processInstance, User).Result)
            return Ok(new CancelProcessInstanceResult { Success = true, Message = "你无权取消工单" }.ToSuccess());
        _workflowManager.Cancel(processInstanceId, User.ToUserViewModel());
        return Ok(new CancelProcessInstanceResult { Success = true, Message = "成功取消" }.ToSuccess());
    }


    /// <summary>
    ///     接受工作活动
    /// </summary>
    /// <param name="workActivityId">工作活动id</param>
    /// <param name="user">接受任务的用户，如果不是工作流管理者或者admin角色，</param>
    /// <param name="manager"></param>
    /// <returns></returns>
    [HttpPut("accept/{workActivityId:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<AcceptResult>))]
    public async Task<IActionResult> Accept([FromRoute] int workActivityId, [FromQuery] string user,
        [FromServices] WorkflowPermissionManager manager)
    {
        var userName = User.Identity?.Name;
        if (!string.IsNullOrWhiteSpace(user) && user != User.Identity?.Name)
        {
            var workActivity = _workflowManager.GetWorkActivityById(workActivityId);
            var isManager = await manager.IsManagerAsync(workActivity, User);
            if (!isManager)
            {
                var acceptResult = new AcceptResult
                {
                    Message = "你无权指派其他用户处理本工单。"
                };

                return Ok(acceptResult.ToError("你无权指派其他用户处理本工单", -1));
            }

            userName = user;
        }

        
        var result = _workflowManager.Accept(workActivityId, User.ToUserViewModel());
        return Ok(result);
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="tagManager"></param>
    /// <returns></returns>
    [HttpGet("{processInstanceId}/tags")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceTagViewModel[]>))]
    public IActionResult GetTags([FromRoute] int processInstanceId, [FromServices] TagManager tagManager)
    {
        var processInstance = _workflowManager.GetById(processInstanceId);

        if (!_workflowPermissionManager.HasReadPermissionAsync(processInstance, User).Result) return Forbid();
        var result = tagManager.GetTagsByProcessInstanceId(new[] { processInstanceId }).Result;
        if (result.Count == 0)
            return Ok(Array.Empty<ProcessInstanceTagViewModel>().ToSuccess());
        return Ok(result.Values.First().ToSuccess());
    }

    /// <summary>
    ///     添加标签
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="tag"></param>
    /// <param name="tagManager"></param>
    /// <returns></returns>
    [HttpPut("{processInstanceId}/add-tag")]
    public SwfResult<AcceptResult> AddTag([FromRoute] int processInstanceId, [FromBody] TagSubmit tag,
        [FromServices] TagManager tagManager)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.IsManage(pi.WorkProcess, User))
        {
            var result = new AcceptResult
            {
                Message = "你无权执行增加标记的操作。"
            };
            return result.ToError(result.Message, -403);
        }

        tagManager.AddTag(pi, tag);
        var result1 = new AcceptResult
        {
            Message = "标签添加成功。"
        };
        return result1.ToSuccess(result1.Message);
    }

    /// <summary>
    ///     移除标签
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="tag"></param>
    /// <param name="tagManager"></param>
    /// <returns></returns>
    [HttpPut("{processInstanceId}/remove-tag")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceResolveResult>))]
    public IActionResult RemoveTag([FromRoute] int processInstanceId, [FromQuery] string tag,
        [FromServices] TagManager tagManager)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.IsManage(pi.WorkProcess, User))
            return Ok(new AcceptResult
            {
                Success = false,
                Message = "你无权执行增加标记的操作。"
            });
        tagManager.RemoveTag(pi, tag);
        return Ok(new ProcessInstanceResolveResult { Success = true, Message = "标签移除成功。" }.ToSuccess());
    }


    /// <summary>
    ///     更新所有tags
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="submit"></param>
    /// <param name="tagManager"></param>
    /// <returns></returns>
    [HttpPut("{processInstanceId}/update-tags")]
    [ProducesDefaultResponseType(typeof(SwfResult<ProcessInstanceResolveResult>))]
    public IActionResult UpdateTags([FromRoute] int processInstanceId, [FromBody] TagsSubmit submit,
        [FromServices] TagManager tagManager)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.IsManage(pi.WorkProcess, User))
            return Ok(new AcceptResult
            {
                Success = false,
                Message = "你无权执行修改标记的操作。"
            });
        tagManager.UpdateTags(pi, submit);
        return Ok(new { success = true, message = "标签保存成功。" }.ToSuccess());
    }

    /// <summary>
    ///     放弃工作活动
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <returns></returns>
    [HttpPut("Abandon/{workActivityId:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<AbandonResult>))]
    public IActionResult Abandon([FromRoute] int workActivityId)
    {
        var wa = _workflowManager.GetWorkActivityById(workActivityId);
        if (!_workflowPermissionManager.CanGiveUp(wa, User))
            return Ok(
                new AbandonResult
                {
                    Success = false,
                    Message = "无权放弃",
                    Status = wa.Status
                });
        var workActivityById = _workflowManager.Abandon(workActivityId, User.ToUserViewModel());

        return Ok(
            new AbandonResult
            {
                Success = true,
                Message = "成功放弃",
                Status = workActivityById.Status
            }.ToSuccess());
    }

    /// <summary>
    ///     启动一个工作流实例
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="workflowPermissionManager"></param>
    /// <returns></returns>
    [HttpPut("start/{processInstanceId:int}")]
    [ProducesDefaultResponseType(typeof(SwfResult<StartResult>))]
    public IActionResult Start([FromRoute] int processInstanceId,
        [FromServices] WorkflowPermissionManager workflowPermissionManager)
    {
        var pi = _processInstanceStore.GetById(processInstanceId);
        //检查
        workflowPermissionManager.HasStart(User, pi);
        try
        {
            var was = _workflowManager.Start(processInstanceId, User.ToUserViewModel());
            return Ok(was.ToSuccess());
        }

        catch (WorkflowDefinedException ex)
        {
            return Ok(new { success = false, message = "设置错误:" + ex.Message }.ToSuccess());
        }
        catch (WorkflowException ex)
        {
            return Ok(new { success = false, message = ex.Message }.ToSuccess());
        }
    }

    /// <summary>
    ///     提交form的数据
    /// </summary>
    /// <param name="id"></param>
    /// <param name="processInstanceFormSubmit"></param>
    /// <returns></returns>
    [HttpPut("save-form/{id}")]
    [ProducesDefaultResponseType(typeof(SwfResult<string>))]
    //[ModelValidateFilter]
    public IActionResult FormSave([FromRoute] int id, [FromBody] ProcessInstanceFormSubmit processInstanceFormSubmit)
    {
        var pi = _workflowManager.GetById(id);
        if (!_workflowPermissionManager.IsManage(pi.WorkProcess, User))
            return Ok(new
            {
                Message = "你无权执行此操作。",
                Success = false
            }.ToSuccess());


        _workflowManager.SaveForm(id, processInstanceFormSubmit);
        return Ok(new
        {
            Message = "保存成功。",
            Success = true
        }.ToSuccess());
    }

    /// <summary>
    ///     获取保存的form数据
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    [HttpPut("get-save-form/{number}")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    //[ModelValidateFilter]
    public IActionResult GetFormSave([FromRoute] string number)
    {
        if (number == null)
            return Ok(new
            {
                Success = false,
                Message = "请输入工单号"
            });
        var pi = _processInstanceStore.Get(number);


        return Ok(new
        {
            Success = true,
            Message = pi.Form
        });
    }

    /// <summary>
    ///     提交form的数据
    /// </summary>
    /// <param name="number"></param>
    /// <param name="processInstanceFormSubmit"></param>
    /// <returns></returns>
    [HttpPut("save-form1/{number}")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    //[ModelValidateFilter]
    public IActionResult FormSave1([FromRoute] string number,
        [FromBody] ProcessInstanceFormSubmit processInstanceFormSubmit)
    {
        if (number == null)
            return Ok(new
            {
                Success = false,
                Message = "请输入工单号"
            });
        if (string.IsNullOrWhiteSpace(processInstanceFormSubmit.Form))
            return Ok(new
            {
                Success = false,
                Message = "Form内容不能为空"
            });
        var result = _workflowManager.SaveForm1(number, processInstanceFormSubmit);
        return Ok(result.ToSuccess());
    }

    /// <summary>
    ///     通过WorkActivity保存form。
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <param name="formSubmit"></param>
    /// <returns></returns>
    [HttpPut("save-form-by-work-activity/{workActivityId}")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public IActionResult FormSaveByActivity([FromRoute] int workActivityId, [FromBody] WorkActivityForm formSubmit,
        [FromServices] IUserClient userClient)
    {
        var currentUser = User.ToDebuggerUser(userClient, formSubmit.SubmitFormUser);
        var workActivity = _workflowManager.GetWorkActivityById(workActivityId);

        if (!_workflowPermissionManager.CanResolve(workActivity, currentUser, out _))
            return Ok(new ResolveResult
            {
                Success = false,
                Message = "工作活动已经被处理或你无权处理。"
            }.ToSuccess());

        var submit = _workflowManager.SaveForm(workActivityId, formSubmit);
        return Ok(submit.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="obj"></param>
    /// <returns></returns>
    [HttpPut("save-subject/{processInstanceId:int}")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public IActionResult SaveSubject([FromRoute] int processInstanceId, [FromBody] ProcessInstanceSubject obj)
    {
        var processInstance = _workflowManager.GetById(processInstanceId);

        if (!_workflowPermissionManager.HasReadPermissionAsync(processInstance, User).Result)
            return Ok(new
            {
                Success = true,
                Message = "你无权操作"
            }.ToSuccess());


        processInstance.Subject = obj.Subject;
        _workflowManager.Save(processInstance);


        return Ok(new
        {
            Success = true,
            Message = "保存成功"
        }.ToSuccess());
    }

    /// <summary>
    ///     获取命令列表
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    [HttpGet("get-commands/{workActivityId}")]
    public IActionResult GetCommands(int workActivityId, [FromServices] IWorkActivityStore store)
    {
        var workActivity = store.GetById(workActivityId);

        if (!_workflowPermissionManager.HasReadPermissionAsync(workActivity, User).Result)
            return Forbid();

        if (workActivity == null)
            throw new ArgumentOutOfRangeException(nameof(workActivityId), workActivityId + "不存在。");
        return Ok(new
        {
            commands = workActivity.WorkTask.Commands.Select(command => command.Name)
        });
    }

    /// <summary>
    ///     上传正文
    /// </summary>
    /// <param name="file"></param>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    [HttpPost("upload_text/{processInstanceId:int}")]
    public IActionResult UploadText(IFormFile file, [FromRoute] int processInstanceId)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        var workActivities = _workflowManager.GetWorkActivities(pi.Id, WorkActivityStatus.Processing);
        if (!_workflowPermissionManager.CanUploadFiles(workActivities, User)) return Forbid();


        var directory = new DirectoryInfo("upload");
        if (!directory.Exists) directory.Create();
        var user = "admin";
        if (User.Identity != null) user = User.Identity.Name;
        var fileName = ToDBC(file.FileName);
        var extension = Path.GetExtension(fileName);
        if (!extension.StartsWith(".")) extension = "." + extension;

        var filePath = "upload/" + Guid.NewGuid().ToString("n") + extension;
        using var writer = System.IO.File.OpenWrite(filePath);
        file.CopyTo(writer);
        writer.Dispose();

        var stream = System.IO.File.OpenRead(filePath);
        //var sFile = _fileManager.Post(stream, pi.WorkProcess.Name, file.FileName,
        //    processInstanceId.ToString(), false);

        var sFile = _fileManager.PostFile(stream, new SFileSubmit
        {
            CreateUser = user,
            Name = fileName,
            RefId = processInstanceId.ToString()
        }).Result;
        var newfile = new ProcessInstance.FileAttach
        {
            FileId = sFile.Id,
            FileName = sFile.Name,
            FileType = FileType.正文,
            CreateUser = user
        };
        foreach (var f in pi.Files)
            if (f.FileType == FileType.正文)
                _context.Remove(f);
        pi.Files.Add(newfile);
        _context.Add(newfile);
        _context.SaveChanges();
        //_workflowManager.Save(pi);
        stream.Dispose();
        System.IO.File.Delete(filePath);
        return Ok(new FileAttachSubmit
        {
            FileId = sFile.Id,
            FileName = sFile.Name
        }.ToSuccess());
    }

    /// <summary>
    ///     替换特殊中文符号.
    /// </summary>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public static string ToDBC(string input)
    {
        var c = input.ToCharArray();
        for (var i = 0; i < c.Length; i++)
        {
            if (c[i] == 12288)
            {
                c[i] = (char)32;
                continue;
            }

            if (c[i] > 65280 && c[i] < 65375)
                c[i] = (char)(c[i] - 65248);
        }

        return new string(c);
    }

    /// <summary>
    ///     上传附件,并非正文
    /// </summary>
    /// <param name="file"></param>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    [HttpPost("upload/{processInstanceId:int}")]
    public async Task<IActionResult> Upload(IFormFile file, [FromRoute] int processInstanceId)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (pi == null) throw new NotFoundProcessInstanceException(processInstanceId);

        var workActivities = _workflowManager.GetWorkActivities(pi.Id, WorkActivityStatus.Processing);
        if (!_workflowPermissionManager.CanUploadFiles(workActivities, User)) return Forbid();


        var user = User.Identity?.Name;
#if DEBUG
        if (string.IsNullOrEmpty(user))
            user = "admin";
#endif
        var fileName = ToDBC(file.FileName);
        var stream = file.OpenReadStream();
        var sFileData = await _fileManager.PostFile(stream, new SFileSubmit
        {
            CreateUser = user,
            Name = fileName,
            RefId = processInstanceId.ToString()
        });
        var sFile = sFileData;
        var newfile = new ProcessInstance.FileAttach
        {
            FileId = sFile.Id,
            FileName = fileName,
            FileType = FileType.附件,
            CreateUser = user
        };
        pi.Files.Add(newfile);
        _context.Add(newfile);
        _context.SaveChanges();
        //_workflowManager.Save(pi);
        stream.Dispose();

        return Ok(new FileAttachSubmit
        {
            FileId = sFile.Id,
            FileName = sFile.Name
        }.ToSuccess());
    }

    /// <summary>
    ///     删除附件
    /// </summary>
    /// <param name="id">文件id</param>
    /// <param name="processInstanceId"></param>
    /// <returns></returns>
    [HttpDelete("deleteFile/{id}/{processInstanceId}")]
    public IActionResult DeleteFile([FromRoute] int id, [FromRoute] int processInstanceId)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        var workActivities = _workflowManager.GetWorkActivities(pi.Id, WorkActivityStatus.Processing);
        if (!_workflowPermissionManager.CanUploadFiles(workActivities, User)) return Forbid();

        foreach (var file in pi.Files)
            if (file.Id == id)
            {
                _fileManager.Delete(file.FileId);

                _context.Remove(file);
                _context.SaveChanges();
                break;
            }

        _logger.LogWarning(new EventId(900), $"删除文件{id}，属于工作流实例{processInstanceId}");
        return Ok(new { success = true, message = "删除成功！" }.ToSuccess());
    }

    /// <summary>
    ///     根据流程实例ID获取上传的附件
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="store"></param>
    /// <param name="userClient"></param>
    /// <returns></returns>
    [HttpGet("get-files-by-processInstanceId/{processInstanceId}")]
    public async Task<IActionResult> GetFilesByProcessInstanceId(int processInstanceId,
        [FromServices] IProcessInstanceStore store, [FromServices] IUserClient userClient)
    {
        var processInstance = store.GetById(processInstanceId);

        if (!_workflowPermissionManager.HasReadPermissionAsync(processInstance, User).Result) return Forbid();

        if (processInstance == null)
            return NotFound(new { success = false, message = "没有找到id=" + processInstanceId + "工作实例。" });

        if (processInstance.Files == null || processInstance.Files.Count == 0)
            return Ok(Array.Empty<object>().ToSuccess());
        IEnumerable<ProcessInstance.FileAttach> files = processInstance.Files
            .Where(fileAttach => fileAttach.FileType == FileType.附件)
            .ToArray();

        var userNames = files.Select(fileAttach => fileAttach.CreateUser).ToArray();
        if (userNames.Any())
        {
            var users = await userClient.GetSimpleUsersAsync(userNames);
            var userMapping = users.Data.ToDictionary(userSimpleModel => userSimpleModel.UserName, _ => _.Name);
            return Ok(files.Select(_ => new
            {
                _.Id,
                createUser = userMapping.ContainsKey(_.CreateUser) ? userMapping[_.CreateUser] : _.CreateUser,
                _.FileId,
                _.FileName,
                _.FileType
            }).ToSuccess());
        }

        return Ok(files.Select(fileAttach => new
        {
            fileAttach.Id,
            createUser = fileAttach.CreateUser,
            fileAttach.FileId,
            fileAttach.FileName,
            fileAttach.FileType
        }).ToSuccess());
    }

    /// <summary>
    ///     根据流程实例ID获取上传的正文
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    [HttpGet("get-files-text-by-processInstanceId/{processInstanceId}")]
    public IActionResult GetFilesTextByProcessInstanceId(int processInstanceId,
        [FromServices] IProcessInstanceStore store)
    {
        var wa = store.GetById(processInstanceId);
        if (wa == null)
        {
            return Ok((new { success = false, message = "WorkActivity不存在" }).ToError("不存在", 404));
        }

        if (!_workflowPermissionManager.HasReadPermissionAsync(wa, User).Result) return Forbid();
        return Ok(wa.Files.Where(fileAttach => fileAttach.FileType == FileType.正文).ToSuccess());

    }

    /// <summary>
    ///     根据文件ID获取文件
    /// </summary>
    /// <param name="id"></param>
    /// <param name="fileName"></param>
    /// <param name="fileStore"></param>
    /// <returns></returns>
    [HttpGet("get-file-by-id/{id}/{fileName?}")]
    //[AllowAnonymous]
    public async Task<IActionResult> GetFileById([FromRoute] string id, [FromRoute] string fileName,
        [FromServices] IFileStoreQuery fileStore)
    {
        var fileAttr = fileStore.GetByIdAsync(id).Result;
        if (!_workflowPermissionManager.HasReadPermissionAsync(fileAttr.ProcessInstance, User).Result) return Forbid();

        var fileInfo = await _fileManager.Get(id);
        if (fileInfo == null) throw new ArgumentOutOfRangeException("没有找到文件id=" + id + "的文件。");

        if (fileInfo.Name == null) throw new ArgumentOutOfRangeException("文件id=" + id + "的文件没有文件名称");
        var contentType = HttpCoderUtility.GetContentTypeByExtension(Path.GetExtension(fileInfo.Name));
        var stream = _fileManager.GetStream(id);

        _logger.LogWarning(new EventId(900), $"下载文件{id}");
        return File(stream, contentType, fileName ?? fileInfo.Name);
    }

    /// <summary>
    ///     get-preview-ticket
    /// </summary>
    /// <param name="id">文件id</param>
    /// <param name="tokenClient"></param>
    /// <returns></returns>
    [HttpGet("get-preview-ticket/{id}")]
    [ProducesDefaultResponseType(typeof(TokenResult<BuildTokenTicketResult>))]
    public async Task<IActionResult> GetPreviewTicket([FromRoute] string id,
        [FromServices] ITokenBuilderClient tokenClient)
    {
        var fileInfo = await _fileManager.Get(id);
        if (fileInfo == null) throw new ArgumentOutOfRangeException("没有找到文件id=" + id + "的文件。");
        if (fileInfo.Name == null) throw new ArgumentOutOfRangeException("文件id=" + id + "的文件没有文件名称");

        var claims = new List<ClaimSubmit>();
        foreach (var claim in User.Claims)
            claims.Add(new ClaimSubmit
            {
                Type = claim.Type,
                Value = claim.Value
            });

        var ticketResult = await tokenClient.CreateTicketAsync(new CreateTokenSubmit
        {
            Claims = claims,
            ExpireMinutes = 10
        });
        return Ok(ticketResult);
    }

    /// <summary>
    ///     获取我能够创建的work-process-list
    /// </summary>
    /// <param name="store"></param>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("work-process/list")]
    [ProducesDefaultResponseType(typeof(SwfResult<IEnumerable<WorkProcessListItem>>))]
    public IActionResult ListWorkProcess([FromServices] IWorkProcessPermissionStore store,
        [FromQuery] WorkProcessPermissionSearch searcher)
    {
        var user = User.Identity?.Name;
#if DEBUG
        if (string.IsNullOrEmpty(user)) user = "admin";
#endif
        var performers = new List<PermissionPerformer>();
        var roles = User.Claims.Where(claim => claim.Type == "role");
        foreach (var role in roles)
            performers.Add(new PermissionPerformer
            {
                Name = role.Value,
                Type = PerformerType.Role
            });
        var orgs = User.Claims.Where(claim => claim.Type == "org");
        foreach (var org in orgs)
            performers.Add(new PermissionPerformer
            {
                Name = org.Value,
                Type = PerformerType.Org
            });

        var query = store.List(searcher, performers.ToArray(), user);
        return Ok(query.Select(_ => _.ToViewModel()).ToSuccess());
    }

    /// <summary>
    ///     工作流定义数目
    /// </summary>
    /// <param name="store"></param>
    /// <param name="searcher">查询参数</param>
    /// <returns></returns>
    [HttpGet("work-process/count")]
    [ProducesDefaultResponseType(typeof(SwfResult<int>))]
    public async Task<IActionResult> CountWorkProcess([FromServices] IWorkProcessPermissionStore store,
        [FromQuery] WorkProcessPermissionSearch searcher)
    {
        var user = User.Identity?.Name;
#if DEBUG
        if (string.IsNullOrEmpty(user)) user = "admin";
#endif
        var performers = new List<PermissionPerformer>();
        var roles = User.Claims.Where(claim => claim.Type == "role");
        foreach (var role in roles)
            performers.Add(new PermissionPerformer
            {
                Name = role.Value,
                Type = PerformerType.Role
            });
        var orgs = User.Claims.Where(claim => claim.Type == "org");
        foreach (var org in orgs)
            performers.Add(new PermissionPerformer
            {
                Name = org.Value,
                Type = PerformerType.Org
            });

        var total = await store.Count(searcher, performers.ToArray(), user);
        return Ok(total.ToSuccess());
    }

    /// <summary>
    ///     通过工作流程实例id获取定义的流程简化信息。
    /// </summary>
    /// <param name="processInstanceId">工作流实例id</param>
    /// <param name="store"></param>
    /// <param name="nodeStore"></param>
    /// <returns></returns>
    [HttpGet("work-process-flow/{processInstanceId}")]
    [ProducesDefaultResponseType(typeof(WorkProcessWithNodesViewModel))]
    public async Task<IActionResult> GetWorkActionResult([FromRoute] int processInstanceId,
        [FromServices] IWorkProcessStore store, [FromServices] INodeStore nodeStore)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.HasReadPermissionAsync(pi, User).Result) return Forbid();


        var nodes = await nodeStore.GetNodesByWorkProcessAsync(pi.WorkProcess);
        return Ok(pi.WorkProcess.ToWorkProcessWithNodesViewModel(nodes).ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId">工作流实例id</param>
    /// <param name="priority"></param>
    /// <returns></returns>
    [HttpPut("set-priority/{processInstanceId}")]
    public IActionResult SetPriority([FromRoute] int processInstanceId, [FromQuery] Priority priority)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.IsManage(pi.WorkProcess, User)) return Forbid();

        pi.Priority = priority;
        _workflowManager.Save(pi);
        return Ok(new { success = true, message = "设置优先级成功。" }.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceId"></param>
    /// <param name="submit"></param>
    /// <returns></returns>
    [HttpPut("reset-to-work-task/{processInstanceId}")]
    [Authorize(Roles = "admin,workflowManager")]
    [ProducesDefaultResponseType(typeof(ResetToWorkTaskResult))]
    public async Task<ResetToWorkTaskResult> ResetToWorkTask([FromRoute] int processInstanceId,
        [FromBody] ResetToWorkTaskSubmit submit)
    {
        var pi = _workflowManager.GetById(processInstanceId);
        if (!_workflowPermissionManager.IsManage(pi.WorkProcess, User))
            return new ResetToWorkTaskResult
            {
                Message = "重置失败，你的权限不够。",
                Success = false
            };

        return await _workflowManager.ResetToWorkTaskAsync(pi, submit, User.ToUserViewModel());
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher">查询参数</param>
    /// <param name="userName">用户名称</param>
    private void SetPermissions([FromQuery] WorkActivitySearcher searcher, out string userName)
    {
        userName = User.Identity.Name;
#if DEBUG
        if (string.IsNullOrEmpty(userName)) userName = "admin";
#endif

        var orgClaim = User.Claims.FirstOrDefault(claim => claim.Type == UserDefined.ClaimOrg);
        if (orgClaim != null) searcher.Orgs = new[] { orgClaim.Value };

        var roleClaim = User.Claims.Where(claim => claim.Type == ClaimTypes.Role).Select(claim => claim.Value);
        searcher.Roles = roleClaim.ToArray();
    }

    /// <summary>
    ///     删除workActivity相关的workActivity，并且重新从
    ///     执行排放。
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    [HttpPut("restart-work-activity")]
    public IActionResult RestartWorkActivity([FromBody] RestartWorkActivitySubmit submit,
        [FromServices] IWorkActivityStore store, IUserClient userClient)
    {
        var currentUser = User.ToDebuggerUser(userClient, submit.User);
        var workActivity = store.GetById(submit.WorkActivityId);
        var result = _workflowManager.Restart(workActivity, currentUser.ToUserViewModel());
        return Ok(result.ToSuccess());
    }
}