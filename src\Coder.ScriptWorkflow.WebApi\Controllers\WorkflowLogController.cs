﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流日志
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize(Roles = "admin")]
public class WorkflowLogController : Controller
{
    private readonly ILoggerStore _loggerStore;

    /// <summary>
    /// </summary>
    /// <param name="loggerStore"></param>
    public WorkflowLogController(ILoggerStore loggerStore)
    {
        _loggerStore = loggerStore;
    }

    /// <summary>
    ///     列出相关
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("list")]
    [ProducesDefaultResponseType(typeof(IEnumerable<WorkflowLog>))]
    public IActionResult List([FromQuery] WorkflowLogSearcher searcher)
    {
        var f = _loggerStore.Find(searcher);
        return Ok(f);
    }

    /// <summary>
    ///     log的·1数目
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    [HttpGet("count")]
    public IActionResult Count([FromQuery] WorkflowLogSearcher searcher)
    {
        var result = _loggerStore.Count(searcher);
        return Ok(result);
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("get/{id}")]
    [ProducesDefaultResponseType(typeof(WorkflowLog))]
    public IActionResult Get([FromRoute] int id)
    {
        var log = _loggerStore.GetById(id);
        return Ok(log);
    }
}