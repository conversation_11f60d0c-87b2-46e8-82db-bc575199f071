﻿using System;
using Microsoft.Extensions.Caching.Memory;

namespace Coder.ScriptWorkflow.Tags;

/// <summary>
/// </summary>
public class TagUserTagsCache
{
    private readonly IMemoryCache _cache;

    /// <summary>
    /// </summary>
    /// <param name="cache"></param>
    public TagUserTagsCache(IMemoryCache cache)
    {
        _cache = cache;
    }

    /// <summary>
    /// </summary>
    /// <param name="ptTagTag"></param>
    /// <param name="tag"></param>
    /// <returns></returns>
    public bool TryGetUserTag(string ptTagTag, out string tag)
    {
        if (ptTagTag == null) throw new ArgumentNullException(nameof(ptTagTag));
        return _cache.TryGetValue(ptTagTag, out tag);
    }

    /// <summary>
    /// </summary>
    /// <param name="valueTag"></param>
    /// <param name="userName"></param>
    public void AddUserTag(string valueTag, string userName)
    {
        if (valueTag == null) throw new ArgumentNullException(nameof(valueTag));
        if (userName == null) throw new ArgumentNullException(nameof(userName));
        _cache.Set(valueTag, userName);
    }
}