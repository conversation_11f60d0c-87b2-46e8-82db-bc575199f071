﻿using System;
using System.Linq;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;
using NiL.JS.Core;


namespace Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;

internal class ConditionDecisionTranslator : NodeTranslator<ConditionDecisionSubmit, ConditionDecision>
{
    Random random = new Random();
    protected override void FillToEntity(ConditionDecision node, ConditionDecisionSubmit submit,
        WorkflowSubmitContext context,
        INodeStore nodeStore)
    {
        node.Script = submit.Script;
        node.MatchDescription = submit.MatchDescription;

        foreach (var conditionSettingSubmit in submit.Settings)
        {
            var nodeSetting = (conditionSettingSubmit.Id != 0
                                  ? node.Settings.FirstOrDefault(x => x.Id == conditionSettingSubmit.Id)
                                  : new ConditionSetting()) ??
                              new ConditionSetting();
            //新建setting 在设置普通值的时候，设置id为负数,强制低实现 submit和entity之间的id一致，
            //方便 在buildEntityRelation的时候，获取setting，并且设置node
            if (nodeSetting.Id == 0)
            {
                nodeSetting.Id = random.Next(-1, -99999999);
                conditionSettingSubmit.Id = nodeSetting.Id;
            }
            nodeSetting.Description = conditionSettingSubmit.Description;
            nodeSetting.MatchValue = conditionSettingSubmit.matchValue;
            node.Settings.Add(nodeSetting);

        }
    }

    protected override void FillToViewModel(ConditionDecision src, ConditionDecisionSubmit target)
    {
        foreach (var setting in src.Settings)
            target.Settings.Add(new ConditionSettingSubmit
            {
                Description = setting.Description,
                Id = setting.Id,
                NodeName = setting?.Node?.Name,
                matchValue = setting.MatchValue
            });

        target.Script = src.Script;
        target.MatchDescription = src.MatchDescription;
        target.NextNodeName = src.NextNode.Name;
        target.Id = src.Id;
        target.Name = src.Name;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, ConditionDecision node,
        ConditionDecisionSubmit nodeSubmit)
    {
        base.BuildEntityRelation(context, node, nodeSubmit);

        foreach (var settingSubmit in nodeSubmit.Settings)
        {
            var settingEntity = node.Settings.First(x => x.Id == settingSubmit.Id);
            settingEntity.Node = context.GetNode(settingSubmit.NodeName);

            if (settingEntity.Id < 0)
            {
                settingEntity.Id = 0;
                settingSubmit.Id = 0;
            }
        }

    }
}

/// <summary>
/// </summary>
internal class ScriptDecisionTranslator : NodeTranslator<BooleanScriptDecisionSubmit, BoolScriptDecision>
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <param name="submit"></param>
    /// <param name="context"></param>
    /// <param name="nodeStore"></param>
    protected override void FillToEntity(BoolScriptDecision node, BooleanScriptDecisionSubmit submit,
        WorkflowSubmitContext context, INodeStore nodeStore)
    {
        node.Script = submit.Script;
        node.ElseDescription = submit.ElseDescription;
        node.MatchDescription = submit.MatchDescription;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="target"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    protected override void FillToViewModel(BoolScriptDecision src, BooleanScriptDecisionSubmit target)
    {
        var decision = src;
        if (decision == null)
            throw new ArgumentOutOfRangeException(nameof(src), "node 必须是ScriptDecision");
        target.ElseNodeName = decision?.ElseNode?.Name;
        target.Script = decision.Script;
        target.ElseDescription = decision.ElseDescription;
        target.MatchDescription = decision.MatchDescription;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, BoolScriptDecision node,
        BooleanScriptDecisionSubmit nodeSubmit)
    {
        base.BuildEntityRelation(context, node, nodeSubmit);
        if (context.WorkProcess.Enable && string.IsNullOrEmpty(nodeSubmit.ElseNodeName))
            throw new WorkflowDefinedException("需要设置‘否决节点’", nodeSubmit.Name);
        if (!string.IsNullOrEmpty(nodeSubmit.ElseNodeName))
            node.ElseNode = context.GetNode(nodeSubmit.ElseNodeName);
    }
}