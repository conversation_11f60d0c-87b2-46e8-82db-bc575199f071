﻿namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
/// </summary>
public class FileAttachmentVieModel
{
    /// <summary>
    /// </summary>
    public string ProcessInstanceNumber { get; set; }

    /// <summary>
    /// </summary>
    public string ProcessInstanceName { get; set; }

    /// <summary>
    /// </summary>
    public int ProcessInstanceId { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    /// </summary>
    public string ProcessInstanceSubject { get; set; }
}

public class FilePackSubmit
{
    public string[] FileId { get; set; }
}