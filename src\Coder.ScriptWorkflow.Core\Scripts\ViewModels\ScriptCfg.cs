﻿using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Scripts.ViewModels;

/// <summary>
///     工作流配置，可以方便地在脚本中调用
/// </summary>
public class ScriptCfg
{
    private readonly IList<WorkflowConfiguration> _configurations;

    /// <summary>
    /// </summary>
    /// <param name="configurations"></param>
    public ScriptCfg(IList<WorkflowConfiguration> configurations)
    {
        _configurations = configurations;
    }

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public string Get(string name)
    {
        return _configurations.FirstOrDefault(_ => _.Name == name)?.Value;
    }
}

/// <summary>
/// </summary>
public enum TagChanged
{
    /// <summary>
    /// </summary>
    Normal,

    /// <summary>
    /// </summary>
    Added,

    /// <summary>
    /// </summary>
    Removed
}

/// <summary>
/// </summary>
public class ScriptTagInfo
{
    /// <summary>
    /// </summary>
    public string TagName { get; set; }

    /// <summary>
    ///     在context中，是增加了，
    /// </summary>
    public TagChanged TagStatus { get; set; }

    /// <summary>
    /// </summary>
    public long ProcessInstanceTagId { get; set; }

    public string Color { get; set; }

    /// <summary>
    ///     是否能够被删除
    /// </summary>
    public bool CanDelete { get; set; }
}