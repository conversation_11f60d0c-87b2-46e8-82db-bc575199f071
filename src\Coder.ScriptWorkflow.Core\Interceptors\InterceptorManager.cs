﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Logger;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.Interceptors;

/// <summary>
///     拦截器
/// </summary>
public class InterceptorManager
{
    private readonly IEnumerable<IWorkflowInterceptor> _workflowInterceptors;
    private readonly IEnumerable<IWorkProcessInterceptor> _workProcessInterceptors;
    private readonly ILogger<InterceptorManager> _logger;

    /// <summary>
    /// </summary>
    /// <param name="workflowInterceptors"></param>
    /// <param name="workProcessInterceptors"></param>
    public InterceptorManager([NotNull] IEnumerable<IWorkflowInterceptor> workflowInterceptors, [NotNull] IEnumerable<IWorkProcessInterceptor> workProcessInterceptors,ILogger<InterceptorManager> logger)
    {
        _workflowInterceptors = workflowInterceptors ?? throw new ArgumentNullException(nameof(workflowInterceptors));
        _workProcessInterceptors = workProcessInterceptors;
        _logger = logger;
    }

    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="processInstance"></param>
    /// <param name="provider"></param>
    internal void OnProcessChanged([NotNull] IWorkflowContext topContext, [NotNull] ProcessInstance processInstance, [NotNull] IServiceProvider provider)
    {
        if (_workflowInterceptors == null)
            return;
        foreach (var interceptor in _workflowInterceptors)
            try
            {
                interceptor.OnProcessChanged(topContext, processInstance, provider);
            }
            catch (Exception ex)
            {
                var logManager = provider.GetRequiredService<WorkflowLogManager>();
                logManager.LogError(topContext, WorkflowLogType.Interception, ex, "拦截器" + interceptor.GetType().Name);
                logManager.Flush();
            }
    }

    /// <summary>
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="workActivity"></param>
    /// <param name="provider"></param>
    internal void OnWorkActivityChanged([NotNull] IWorkflowContext topContext, [NotNull] WorkActivity workActivity, [NotNull] IServiceProvider provider)
    {
        if (topContext == null) throw new ArgumentNullException(nameof(topContext));
        if (workActivity == null) throw new ArgumentNullException(nameof(workActivity));
        if (provider == null) throw new ArgumentNullException(nameof(provider));

        if (_workflowInterceptors == null)
            return;
        foreach (var interceptor in _workflowInterceptors)
            try
            {
                interceptor.OnWorkActivityChanged(topContext, workActivity, provider);
            }
            catch (Exception ex)
            {
                var logManager = provider.GetRequiredService<WorkflowLogManager>();
                logManager.LogError(topContext, WorkflowLogType.Interception, ex, "拦截器" + interceptor.GetType().Name);
                logManager.Flush();
            }
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="userName"></param>
    public async Task NotifyDistributionUserAsync(IWorkflowContext workflowContext, [NotNull] IEnumerable<string> userName)
    {
        try
        {
            var tasks = new Task[_workflowInterceptors.Count()];
            var index = 0;
            foreach (var interceptor in _workflowInterceptors)
            {
                var task = interceptor.NotifyDistributionUserAsync(workflowContext, userName);
                tasks[index] = task;
                index++;
            }

            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "NotifyDistributionUserAsync 出错。");
        }
    }

    public void OnWorkProcessCreator([NotNull] WorkProcess workProcess)

    {
        foreach (var workProcessInterceptor in _workProcessInterceptors) workProcessInterceptor.OnCreate(workProcess);
    }

    public void OnWorkProcessUpdate([NotNull] WorkProcess workProcess)
    {
        foreach (var workProcessInterceptor in _workProcessInterceptors) workProcessInterceptor.OnCreate(workProcess);
    }
}