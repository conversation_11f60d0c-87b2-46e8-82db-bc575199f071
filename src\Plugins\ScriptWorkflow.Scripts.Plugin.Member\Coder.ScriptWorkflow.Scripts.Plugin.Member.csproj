﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFramework>net9.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="defined.ts" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="defined.ts">
      <SubType>Code</SubType>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
  	<PackageReference Include="Coder.Member.Clients.Http" Version="9.0.2" />
    <PackageReference Include="Coder.Migrator.HttpClients" Version="1.2.2" />
    <PackageReference Include="NiL.JS" Version="2.6.1700" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
  </ItemGroup>

 

</Project>
