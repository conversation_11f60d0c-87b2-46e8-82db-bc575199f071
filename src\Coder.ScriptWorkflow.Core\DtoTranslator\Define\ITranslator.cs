﻿using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

/// <summary>
/// </summary>
internal interface ITranslator
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    NodeSubmit ToViewModel(Node node);

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    Node GetOrCreate(NodeSubmit nodeSubmit, WorkflowSubmitContext context);

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    bool Validate(NodeSubmit nodeSubmit, out string errorMessage);

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="currentNode"></param>
    /// <param name="nodeSubmit"></param>
    void BuildRelation(WorkflowSubmitContext context, Node currentNode, NodeSubmit nodeSubmit);
}