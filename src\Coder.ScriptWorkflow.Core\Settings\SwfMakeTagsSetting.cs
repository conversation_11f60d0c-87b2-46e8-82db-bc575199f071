﻿using System.Collections.Generic;
using System.Security.Claims;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Settings;

/// <summary>
/// </summary>
public class SwfMakeTagsSetting : SwfSetting
{
    private IList<string> _plugins;

    /// <summary>
    /// </summary>
    public IList<string> Plugins => _plugins ??= _plugins = new List<string>();

    /// <summary>
    /// </summary>
    /// <param name="userTagsScript"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    public IEnumerable<string> MakeUserTags(ClaimsPrincipal userTagsScript, GlobalScriptContext context)
    {
        var scriptContext = new Context(context.GlobalContext);


        var user = userTagsScript.ToScriptModel();
        scriptContext.DefineVariable("currentUser")
            .Assign(user);
        var script = @$"function makeTags(user)
{{ 
    {Script}
}}

__result = makeTags(currentUser);
";


        try
        {
            scriptContext.Eval(script);
        }
        catch (JSException ex)
        {
            var runTimeExeption = ex.ToJSException(null, "SwfMakeTagsSetting", script);

            //var exception = new JsRuntimeException("获取用户" + userTagsScript.Identity.Name + "默认Tag失败。", ex);


            //exception.Code = script;
            //if (ex.CodeCoordinates != null)
            //    exception.ErrorLine = ex.CodeCoordinates.Line.ToString() + ":" + ex.CodeCoordinates.Column.ToString();

            throw runTimeExeption;
        }


        var result = scriptContext.GetVariable("__result");

        return JsValueConvert.ToStringArray(result);
    }
}