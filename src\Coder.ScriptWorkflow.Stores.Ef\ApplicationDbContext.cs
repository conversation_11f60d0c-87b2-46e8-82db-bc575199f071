﻿using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Pomelo.EntityFrameworkCore.MySql.Query.Expressions.Internal;

namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public class ApplicationDbContext : DbContext
{
    /// <summary>
    /// </summary>
    /// <param name="options"></param>
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) :
        base(options)
    {
    }


    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    protected override void OnModelCreating(ModelBuilder builder)
    {
        var isSqlite = Database.ProviderName == "Microsoft.EntityFrameworkCore.Sqlite";

        builder.AddEfModels(isSqlite: isSqlite);
        base.OnModelCreating(builder);
        switch (Database.ProviderName)
        {
            case "Microsoft.EntityFrameworkCore.Sqlite": break;
            case "Pomelo.EntityFrameworkCore.MySql":
                builder.HasDbFunction(() => DbFunctions.JsonValue(default, default));
                var stringTypeMapping = new StringTypeMapping("NVARCHAR(MAX)", null);
                builder.HasDbFunction(typeof(RegexE).GetMethod(nameof(RegexE.IsMatch)))
                    .HasTranslation(e => new MySqlRegexpExpression(e.First(), e.Skip(1).First(), null))
                    .HasParameter("column").Metadata.TypeMapping = stringTypeMapping;

                break;
            case "Microsoft.EntityFrameworkCore.SqlServer":
                break;
        }
    }
#if DEBUG
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
        optionsBuilder.EnableSensitiveDataLogging();
    }
#endif
}