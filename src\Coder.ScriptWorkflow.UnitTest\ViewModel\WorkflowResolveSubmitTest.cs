﻿using System;
using Coder.ScriptWorkflow.ViewModels;
using Newtonsoft.Json;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel;

public class WorkflowResolveSubmitTest
{
    [Fact]
    public void Test()
    {
        var submit = new WorkflowResolveSubmit();
        var disposeTime = new DateTime(DateTime.Now.Year,
            DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, 1);
        var a = new
        {
            PI_subject = "subject",
            WA_disposeTime = disposeTime,
            WA_comment = "wa comment",
            PI_cc = new[] { "a", "b" }
        };
        submit.Form = JsonConvert.SerializeObject(a);
        submit.FillSelfByJsonForm();

        Assert.Equal(a.WA_comment, submit.Comment);
        Assert.Equal(disposeTime, submit.DisposeTime);
        Assert.Equal("a", submit.Distributions[0]);
        Assert.Equal("b", submit.Distributions[1]);
    }
}