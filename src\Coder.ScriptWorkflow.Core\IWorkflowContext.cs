﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow;

/// <summary>
///     流转过程中，存放当前状态。
/// </summary>
/// <remarks>
///     每次Node 切换都会切换 WorkflowContext，但是会保留属于PI的tag
/// </remarks>
public interface IWorkflowContext : IDisposable
{
     UserViewModel CurrentUser { get;  }
    /// <summary>
    /// </summary>
     List<ScriptTagInfo> Tags { get; }

    /// <summary>
    /// </summary>
    Context JavascriptGlobalContext { get; }

    /// <summary>
    /// </summary>
    StartNode StartNode { get; }

    /// <summary>
    /// </summary>
    ProcessInstance ProcessInstance { get; }

    /// <summary>
    /// </summary>
    IEnumerable<WorkTask> WorkTasks { get; }

    /// <summary>
    ///     获取发起Resolve方法的工作活动，如果中通通过多个WorkActivity 这个属性也不会发生变化
    /// </summary>
    WorkActivity CurrentWorkActivity { get; }

    /// <summary>
    /// </summary>
    Node CurrentNode { get; set; }

    /// <summary>
    ///     与CurrentWorkActivity相关的工作活动。
    /// </summary>

    IEnumerable<WorkActivity> RelativeWorkActivities { get; }

    /// <summary>
    /// </summary>
    IEnumerable<WorkActivity> AllWorkActivities { get; }

    /// <summary>
    /// </summary>

    IWorkflowContext Child { get; }

    /// <summary>
    /// </summary>
    DebuggerManager Debugger { get; }

    /// <summary>
    /// </summary>
    WorkflowLogManager Logger { get; }

    /// <summary>
    ///     是否只是运行，但是不执行持久化。
    ///     假设dryRun功能启动，
    ///     那么不应该执行任何持久化的功能。
    ///     在脚本的里里面，应该判断IsDryRun 如果处于DryRun，那么不应该执行会改变第三方服务的命令。
    /// </summary>
    bool IsDryRun { get; set; }

    /// <summary>
    ///     提交cc的数据
    /// </summary>
    public ISet<string> DistributionUsers { get; }

    /// <summary>
    /// </summary>
    /// <param name="workTask"></param>
    /// <returns></returns>
    IWorkflowContext BuildChildWorkflowContext(WorkTask workTask);

    /// <summary>
    /// </summary>
    /// <param name="activitiesInTokenGroup"></param>
    void SetAllWorkActivities(IEnumerable<WorkActivity> activitiesInTokenGroup);

    /// <summary>
    /// </summary>
    /// <param name="workActivity"></param>
    void SwitchCurrentWorkActivity(WorkActivity workActivity);

    /// <summary>
    ///     添加tag，如果遇到相同的tag，那么不会再增加新的tag。
    ///     如果color为空，那么会random一个新的。
    /// </summary>
    /// <param name="newTag"></param>
    /// <param name="color"></param>
    /// <param name="canDelete"></param>
    void AddTag([NotNull] string newTag, [AllowNull] string color, bool canDelete);

    /// <summary>
    ///     移除 processInstance的 tag信息。
    /// </summary>
    /// <param name="newTag"></param>
    void RemoveTag([NotNull] string newTag);

    /// <summary>
    ///     创建
    /// </summary>
    /// <returns></returns>
    Context BuildScriptContext();
}

/// <summary>
/// </summary>
internal static class SendDebuggerHelper
{
    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="runTimeException"></param>
    /// <param name="messagePreFix"></param>
    public static void SendJsExceptionDebuggerInfo(this IWorkflowContext workflowContext, JsRuntimeException runTimeException, string messagePreFix = null)
    {
        workflowContext.Debugger?.SendJsException(workflowContext, runTimeException, messagePreFix);
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="mesage"></param>
    /// <param name="type"></param>
    public static void SendMessageDebugInfo(this IWorkflowContext workflowContext, string mesage, DebuggerType type = DebuggerType.Info)
    {
        workflowContext.Debugger?.SendMessage(workflowContext, mesage, type);
    }
}