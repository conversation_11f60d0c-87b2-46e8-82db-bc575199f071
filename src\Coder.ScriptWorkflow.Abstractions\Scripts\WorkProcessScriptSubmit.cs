﻿namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
/// 用于提交工作流任意脚本。
/// </summary>
public class WorkProcessScriptSubmit
{
    /// <summary>
    /// </summary>
    public int WorkProcessId { get; set; }

    /// <summary>
    /// 节点名称名称
    /// </summary>
    public string Node { get; set; }
   /// <summary>
   /// 如果节点是worktask，那么是命令名称。
   /// </summary>
    public string CommandName { get; set; }
    /// <summary>
    /// 脚本所属类型
    /// </summary>
    public ScriptSettingType WorkTaskSettingType { get; set; }

    /// <summary>
    ///     脚本
    /// </summary>
    public string Script { get; set; }
}