﻿namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     自定下一个工作任务，不受设置影响，
///     直接指定执行人和下一个任务目标
/// </summary>
/// <remarks>
///     Performers 是多少个人。
///     NextWorkTaskName 是下一个workTask是什么，
///     以上两个均可以为空，如果为空，那么就按照设置执行
/// </remarks>
public class CustomerNextWorkTaskSetting
{
    /// <summary>
    ///     全部为空，按照workProcess设置执行
    /// </summary>
    public static readonly CustomerNextWorkTaskSetting FollowWorkProcessSetting = new();

    /// <summary>
    ///     下一个任务的执行者
    /// </summary>
    public string[] Performers { get; set; }

    /// <summary>
    ///     下一个节点
    /// </summary>
    public string NextWorkTaskName { get; set; }
}