﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Coder.ScriptWorkflow.ViewModels;

public class TagSubmit
{
    [Required] public string Name { get; set; }

    public string Color { get; set; }

    /// <summary>
    ///     是否能够被删除
    /// </summary>
    public bool CanDelete { get; set; }
}

public class TagsSubmit
{
    public IEnumerable<TagSubmit> Tags { get; set; }
}

public class DebuggerScriptChangeSubmit
{
    public string Script { get; set; }

    [Required] public string ScriptType { get; set; }
}