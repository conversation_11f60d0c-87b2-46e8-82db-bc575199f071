﻿using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
///     分配保存
/// </summary>
public interface IAssignStore
{
    /// <summary>
    ///     删除分配
    /// </summary>
    /// <param name="id"></param>
    void Delete(int id);

    /// <summary>
    ///     通过id获取分配
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [return: MaybeNull]
    WorkTask GetById(int id);

    /// <summary>
    ///     增加或者更新
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate(WorkTask workTask);

    /// <summary>
    ///     保存数据
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();
}