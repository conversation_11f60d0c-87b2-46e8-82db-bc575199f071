﻿using Coder.ScriptWorkflow.Scripts.GlobalScripts;

namespace Coder.ScriptWorkflow.Scripts.Plugins;

/// <summary>
///     全局插件，在任何脚本的地方都能够采用。
/// </summary>
public interface IGlobalPlugin
{
    /// <summary>
    ///     插件名字
    /// </summary>
    public string Name { get; }

    /// <summary>
    ///     获取
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public object GetObject(GlobalScriptContext context);

    /// <summary>
    /// </summary>
    /// <param name="o"></param>
    public void Dispose(object o);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public string GetJsDefined();
}