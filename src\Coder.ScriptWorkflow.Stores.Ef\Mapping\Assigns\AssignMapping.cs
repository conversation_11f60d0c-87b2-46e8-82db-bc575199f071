﻿using System;
using Coder.ScriptWorkflow.Assigners;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping.Assigns;

/// <summary>
/// </summary>
internal class AssignMapping : IEntityTypeConfiguration<Assigner>
{
    /// <summary>
    /// </summary>
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public AssignMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<Assigner> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.HasDiscriminator()
            .HasValue<ScriptAssigner>("ScriptAssigner")
            .HasValue<UsersAssigner>("UsersAssigner")
            ;
        builder.Property(_ => _.AssignScopeType);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();

        builder.ToTable($"{_prefix}_assigner");
    }
}