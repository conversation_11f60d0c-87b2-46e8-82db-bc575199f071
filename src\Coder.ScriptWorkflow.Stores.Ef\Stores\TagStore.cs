﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

/// <inheritdoc />
internal class TagStore<T> : ITagStore where T : DbContext
{
    private readonly T _dbContext;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public TagStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    /// <inheritdoc />
    public void Delete(int id)
    {
        var enttiy = _dbContext.Set<Tag>().FirstOrDefault(_ => _.Id == id);
        if (enttiy != null)
            _dbContext.Remove(enttiy);
    }

    /// <inheritdoc />
    public Tag GetByName(string name)
    {
        return _dbContext.Set<Tag>().FirstOrDefault(_ => _.Name == name);
    }

    /// <inheritdoc />
    public void AddOrUpdate(Tag entity)
    {
        _dbContext.Update(entity);
    }

    /// <inheritdoc />
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <inheritdoc />
    public IEnumerable<Tag> GetOrCreate(string[] tags)
    {
        var mappingTabg = _dbContext.Set<Tag>().Where(_ => tags.Contains(_.Name)).ToList().ToDictionary(_ => _.Name, _ => _);
        foreach (var tag in tags)
            if (!mappingTabg.ContainsKey(tag))
            {
                var entity = new Tag
                {
                    Name = tag,
                    Number = 0
                };
                mappingTabg.Add(tag, entity);
                _dbContext.Update(entity);
            }

        _dbContext.SaveChangesAsync().Wait();

        return mappingTabg.Values;
    }

    /// <inheritdoc />
    public void UpdateRemoveReference(IEnumerable<string> beRemoveItem)
    {
        GetTagUpdateNumberCommand(false, beRemoveItem);
    }

    /// <inheritdoc />
    public void UpdateAddReference(IEnumerable<string> addedTags)
    {
        GetTagUpdateNumberCommand(true, addedTags);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Tag>> GetByNamesAsync(IEnumerable<string> userTags)
    {
        return await _dbContext.Set<Tag>()
            .Where(_ => userTags.Contains(_.Name)).ToListAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProcessInstanceTag>> GetProcessInstanceTagsAsync(int processInstanceId)
    {
        return await _dbContext.Set<ProcessInstanceTag>().Where(_ => _.ProcessInstance.Id == processInstanceId).ToListAsync();
    }

    /// <inheritdoc />
    private void GetTagUpdateNumberCommand(bool isAddNumber, IEnumerable<string> tagName)
    {
        var tableName = _dbContext.Model.FindEntityType(typeof(Tag)).GetTableName();

        var addNumber = isAddNumber ? "1" : "-1";
        var text = $@"Update {tableName} set Number=Number + {addNumber} where Name=";
        var connection = _dbContext.Database.GetDbConnection();
        if (connection.State == ConnectionState.Closed) connection.Open();

        foreach (var item in tagName) _dbContext.Database.ExecuteSqlRaw(text + "{0}", item);
    }
}