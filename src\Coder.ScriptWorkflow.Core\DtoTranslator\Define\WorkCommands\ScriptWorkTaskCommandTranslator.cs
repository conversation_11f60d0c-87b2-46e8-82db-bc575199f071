﻿using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Coder.ScriptWorkflow.WorkTaskCommands;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.WorkCommands;

/// <summary>
/// </summary>
internal class ScriptWorkTaskCommandTranslator : WorkTaskCommandTranslator
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected override WorkTaskCommand CreateEntity()
    {
        return new WorkTaskScriptCommand();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected override WorkTaskCommandSubmit CreateViewModel()
    {
        return new WorkTaskScriptCommandSubmit();
    }

    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="workTask"></param>
    /// <returns></returns>
    public override WorkTaskCommand FillTo(WorkTaskCommandSubmit submit, WorkTask workTask)
    {
        var scriptCommandSubmit = (WorkTaskScriptCommandSubmit)submit;
        var command = (WorkTaskScriptCommand)base.FillTo(submit, workTask);
        command.Script = scriptCommandSubmit.Script;
        return command;
    }

    /// <summary>
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    public override WorkTaskCommandSubmit ToViewModel(WorkTaskCommand command)
    {
        var submit = (WorkTaskScriptCommandSubmit)base.ToViewModel(command);
        submit.Script = ((WorkTaskScriptCommand)command).Script;
        return submit;
    }
}