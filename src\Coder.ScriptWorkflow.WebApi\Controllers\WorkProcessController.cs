﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.Member.Clients;
using Coder.Member.ViewModels.Roles;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.Permissions.Cache;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Permissions;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;
using Coder.ScriptWorkflow.WebApi.Modules;
using Coder.WebHttpClient;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize]
public class WorkProcessController : Controller
{
    private readonly ILogger<WorkProcessController> _logger;
    private readonly IRoleClient _roleClient;
    private readonly IUserClient _userClient;
    private readonly IWorkProcessPermissionStore _workProcessPermissionStore;

    /// <summary>
    /// </summary>
    public WorkProcessController(IWorkProcessPermissionStore workProcessPermissionStore, IUserClient userClient,
        IRoleClient roleClient, ILogger<WorkProcessController> logger)
    {
        _workProcessPermissionStore = workProcessPermissionStore;
        _userClient = userClient;
        _roleClient = roleClient;
        _logger = logger;
    }

    /// <summary>
    ///     列出我能够创建的工作流实例。
    /// </summary>
    /// <param name="search"></param>
    /// <param name="permissionManager"></param>
    /// <param name="cacheManager"></param>
    /// <returns></returns>
    [HttpGet("list-my-work-process")]
    [ProducesDefaultResponseType(typeof(IEnumerable<WorkProcessListItem>))]
    public async Task<IActionResult> ListMyWorkPrcess([FromQuery] WorkProcessPermissionSearch search,
        [FromServices] WorkflowPermissionManager permissionManager,
        [FromServices] WorkProcessPermissionCacheManager cacheManager)
    {
        var user = User.Identity?.Name;

        var performers = PerformerHelper.GetCurrentPerformerList(User.Claims);


        var query = _workProcessPermissionStore.List(search, performers.ToArray(), user).ToArray();
        //第一步：按照 version 倒叙排列
        var processListItems = query.OrderByDescending(wp => wp.Version).Where(wp => wp.Enable)
            .Select(_ => _.ToViewModel()).ToArray();

        //通过第一步，那么version大的都是优先插入map，所以越大越优先插入
        var result = new Dictionary<string, WorkProcessListItem>();
        foreach (var item in processListItems) result.TryAdd(item.Name, item);

        var workProcessListItems = result.Values.ToArray();

        await permissionManager.SetWorkProcessListItemPermissionAsync(workProcessListItems, User);

        return Ok(workProcessListItems.ToSuccess());
    }

    /// <summary>
    ///     获取你能够管理或者发起的工作流程
    /// </summary>
    /// <returns></returns>
    [HttpGet("list")]
    [ProducesDefaultResponseType(typeof(IEnumerable<WorkProcessListItem>))]
    public async Task<IActionResult> List([FromQuery] WorkProcessPermissionSearch search,
        [FromServices] WorkflowPermissionManager permissionManager,
        [FromServices] WorkProcessPermissionCacheManager cacheManager)
    {
        var user = User.Identity?.Name;
#if DEBUG
        if (string.IsNullOrEmpty(user)) user = "admin";
#endif


        var performers = PerformerHelper.GetCurrentPerformerList(User.Claims);


        var query = _workProcessPermissionStore.List(search, performers.ToArray(), user).ToArray();
        var result = query.Select(_ => _.ToViewModel()).ToArray();


        await permissionManager.SetWorkProcessListItemPermissionAsync(result.ToArray(), User);

        return Ok(result.ToSuccess());
    }


    /// <summary>
    ///     获取你能够管理或者发起的工作流程 的数目
    /// </summary>
    /// <returns></returns>
    [HttpGet("count")]
    [ProducesDefaultResponseType(typeof(int))]
    public IActionResult CountForPage([FromQuery] WorkProcessPermissionSearch search)
    {
        var user = User.Identity?.Name;
#if DEBUG
        if (string.IsNullOrEmpty(user)) user = "admin";
#endif
        var performers = PerformerHelper.GetCurrentPerformerList(User.Claims);

        var count = _workProcessPermissionStore.Count(search, performers.ToArray(), user).Result;
        return Ok(count.ToSuccess());
    }

    /// <summary>
    ///     保存
    /// </summary>
    /// <returns></returns>
    [HttpPost("permission/save")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public async Task<IActionResult> Update([FromBody] WorkProcessPermissionSubmit submit,
        [FromServices] WorkProcessPermissionCacheManager cacheManager)
    {
        var entity = (_workProcessPermissionStore.GetById(submit.Id)
                      ?? await _workProcessPermissionStore.GetByProcessNameAsync(submit.ProcessName)) ??
                     new WorkProcessPermission();
        submit.Fill(entity, out var removeItems);

        _workProcessPermissionStore.Remove(removeItems);
        _workProcessPermissionStore.AddOrUpdate(entity);
        cacheManager.Replace(entity, _workProcessPermissionStore);
        await _workProcessPermissionStore.SaveChangesAsync();

        return Ok(new { success = true, message = "保存成功." }.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="performerType"></param>
    /// <returns></returns>
    [HttpGet("performer-list/{performerType}")]
    public IActionResult PerformerList([FromRoute] PerformerType performerType)
    {
        switch (performerType)
        {
            case PerformerType.User:
                var userSearch = new SimpleUserSearch
                {
                    Page = 1,
                    PageSize = 20
                };
                return Ok(_userClient.ListAsync(userSearch).Result.Data.Select(_ => new
                {
                    _.Name,
                    CodeName = _.UserName,
                    key = _.UserName
                }.ToSuccess()));
            case PerformerType.Role:
                var roleSearch = new RoleSearch
                {
                    Page = 1,
                    PageSize = 20
                };
                var rolelist = _roleClient.ListAsync(roleSearch).Result.Data;

                return Ok(rolelist.Select(model => new
                {
                    model.Id,
                    Name = model.Description,
                    CodeName = model.Name,
                    key = model.Name
                }.ToSuccess()));
            case PerformerType.Org:
                return NoContent();
            default:
                return NoContent();
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="performerType"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    [HttpGet("performer-list/{performerType}/{name}")]
    public IActionResult PerformerList([FromRoute] PerformerType performerType, string name)
    {
        switch (performerType)
        {
            case PerformerType.User:
                var userSearch = new SimpleUserSearch
                {
                    Page = 1,
                    PageSize = 20,
                    Name = name
                };
                return Ok(_userClient.ListAsync(userSearch).Result.Data.Select(_ => new
                {
                    _.Name,
                    CodeName = _.UserName,
                    key = _.UserName,
                    Type = PerformerType.User
                }).ToSuccess());
            case PerformerType.Role:
                var roleSearch = new RoleSearch
                {
                    Page = 1,
                    PageSize = 20,
                    Description = name
                };
                return Ok(_roleClient.ListAsync(roleSearch).Result.Data.Select(_ => new
                {
                    _.Id,
                    Name = _.Description,
                    CodeName = _.Name,
                    key = _.Name,
                    Type = PerformerType.Role
                }).ToSuccess());
            case PerformerType.Org:
                return NoContent();
            default:
                return NoContent();
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="performerType"></param>
    /// <param name="code"></param>
    /// <returns></returns>
    [HttpGet("verify-list/{performerType}/{code}")]
    public IActionResult VerifyList([FromRoute] PerformerType performerType, string code)

    {
        var codes = new string[1];
        codes[0] = code;
        switch (performerType)
        {
            case PerformerType.User:
                var users = _userClient.GetSimpleUsersAsync(codes).Result.Data;
                return Ok(users.Select(_ => new
                {
                    _.UserName,
                    _.Name
                }).ToSuccess());
            case PerformerType.Role:
                var roleUsers = _userClient.GetByRolesAsync(new[] { code }).Result.Data;
                return Ok(roleUsers.Select(_ => new
                {
                    _.UserName,
                    _.Name
                }).ToSuccess());
            case PerformerType.Org:
                return NoContent();
            default:
                return NoContent();
        }
    }


    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    [HttpDelete("remove-permission/{id}")]
    [Authorize(Roles = "admin")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public IActionResult Delete([FromRoute] int id, [FromServices] IProcessInstanceStore store)
    {
        var exist = store.HasProcessInstance(id).Result;
        if (!exist)
        {
            _workProcessPermissionStore.Delete(id);
            _workProcessPermissionStore.SaveChangesAsync().Wait();
            return Ok(new { success = true, message = "删除成功." });
        }

        return Ok(new { success = false, message = "还有工单，不能删除。请清空工单后再尝试。" }.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="processName">流程名称</param>
    /// <returns></returns>
    [HttpGet("permission/{processName}")]
    [ProducesDefaultResponseType(typeof(WorkProcessPermissionViewModel))]
    public async Task<IActionResult> Get([FromRoute] string processName)
    {
        var entity = await _workProcessPermissionStore.GetByProcessNameAsync(processName);

        var data = entity?.ToViewModel() ?? new WorkProcessPermissionViewModel
        {
            ProcessName = processName
        };
        return Ok(data.ToSuccess());
    }

    /// <summary>
    ///     是否有工单
    /// </summary>
    /// <param name="id"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    [HttpGet("has-process-instance/{id}")]
    public async Task<IActionResult> Get([FromRoute] int id, [FromServices] IProcessInstanceStore store)
    {
        var result = await store.HasProcessInstance(id);

        return Ok(result.ToSuccess());
    }

    /// <summary>
    ///     检查工作流程是否可以删除
    ///     验证是否存在正在进行的工作流实例
    /// </summary>
    /// <param name="id">工作流程定义ID</param>
    /// <param name="processInstanceStore">流程实例存储（依赖注入）</param>
    /// <returns>检查结果，包含是否可删除和相关信息</returns>
    [HttpGet("{id}/can-delete")]
    [Authorize(Roles = "admin")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public async Task<IActionResult> CanDelete([FromRoute] int id,
        [FromServices] IProcessInstanceStore processInstanceStore)
    {
        try
        {
            // 检查是否有正在进行的工作流实例
            var hasActiveInstances = await processInstanceStore.HasProcessInstance(id);

            if (hasActiveInstances)
                return Ok(new
                {
                    canDelete = false,
                    message = "该工作流程还有正在进行的工单，无法删除。请先处理完所有相关工单。"
                }.ToSuccess());

            return Ok(new
            {
                canDelete = true,
                message = "该工作流程可以安全删除。"
            }.ToSuccess());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查工作流程 {WorkProcessId} 是否可删除时发生异常", id);
            return Ok(new
            {
                canDelete = false,
                message = $"检查失败：{ex.Message}"
            }.ToSuccess());
        }
    }

    /// <summary>
    ///     删除工作流程定义
    ///     注意：此操作会删除工作流程定义及其相关的所有数据，包括节点、实例、工作活动等，请谨慎使用
    /// </summary>
    /// <param name="id">工作流程定义ID</param>
    /// <param name="workflowDefinedManager">工作流定义管理器（依赖注入）</param>
    /// <returns>删除操作结果</returns>
    [HttpDelete("{id}")]
    [Authorize(Roles = "admin")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public IActionResult DeleteWorkProcess([FromRoute] int id,
        [FromServices] WorkflowDefinedManager workflowDefinedManager)
    {
        try
        {
            _logger.LogInformation("用户 {User} 尝试删除工作流程定义 {WorkProcessId}", User.Identity?.Name, id);

            // 尝试删除工作流程定义，该方法会自动检查是否有活跃的实例
            var deleteResult = workflowDefinedManager.TryDelete(id, out var message);

            if (deleteResult)
            {
                _logger.LogInformation("用户 {User} 成功删除工作流程定义 {WorkProcessId}", User.Identity?.Name, id);
                return Ok(new { success = true, message }.ToSuccess());
            }

            _logger.LogWarning("用户 {User} 删除工作流程定义 {WorkProcessId} 失败: {Message}", User.Identity?.Name, id, message);
            return Ok(new { success = false, message }.ToSuccess());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除工作流程定义 {WorkProcessId} 时发生异常", id);
            return Ok(new { success = false, message = $"删除失败：{ex.Message}" }.ToSuccess());
        }
    }
}