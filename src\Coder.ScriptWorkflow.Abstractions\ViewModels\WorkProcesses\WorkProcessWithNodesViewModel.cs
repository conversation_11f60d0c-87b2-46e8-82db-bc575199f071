﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.ViewModels.WorkProcesses;

/// <summary>
/// </summary>
public class WorkProcessWithNodesViewModel : WorkProcessViewModel
{
    /// <summary>
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// </summary>
    public IList<NodeSubmit> Nodes { get; set; } = new List<NodeSubmit>();
}