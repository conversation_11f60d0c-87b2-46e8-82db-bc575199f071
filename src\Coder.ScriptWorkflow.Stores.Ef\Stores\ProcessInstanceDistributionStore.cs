﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
internal class ProcessInstanceDistributionStore<T> : IProcessInstanceDistributionStore where T : DbContext
{
    private readonly T _dbContext;

    /// <summary>
    /// </summary>
    /// <param name="dbContext"></param>
    public ProcessInstanceDistributionStore(T dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    /// <inheritdoc cref="" />
    public Task<ProcessInstanceDistribution> GetByIdAsync(int id)
    {
        return ProcessInstanceDistributions.FirstOrDefaultAsync(_ => _.Id == id);
    }

    /// <inheritdoc cref="" />
    public async Task Delete(int id)
    {
        var entity = await GetByIdAsync(id);
        if (entity == null)
            throw new ArgumentOutOfRangeException(nameof(id), $"找不到id={id}的抄送");
        _dbContext.Remove(entity);
    }

    public void Delete(ProcessInstanceDistribution item)
    {
        if (item == null) throw new ArgumentNullException(nameof(item));
        if (item.Id != 0)
            _dbContext.Remove(item);
    }

    /// <inheritdoc cref="" />
    public void Update(ProcessInstanceDistribution distribution)
    {
        if (distribution == null) throw new ArgumentNullException(nameof(distribution));
        if (distribution == null) throw new ArgumentNullException(nameof(distribution));
        _dbContext.Update(distribution);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <inheritdoc cref="" />
    public IQueryable<ProcessInstanceDistribution> ProcessInstanceDistributions => _dbContext.Set<ProcessInstanceDistribution>();

    public ProcessInstanceDistribution GetByUser(int processInstance, string userName)
    {
        if (userName == null) throw new ArgumentNullException(nameof(userName));
        return ProcessInstanceDistributions.FirstOrDefault(_ => _.ProcessInstance.Id == processInstance && _.UserName == userName);
    }


    /// <inheritdoc cref="" />
    public IEnumerable<ProcessInstanceDistribution> FindBy(ProcessInstance contextProcessInstance)
    {
        if (contextProcessInstance == null) throw new ArgumentNullException(nameof(contextProcessInstance));
        return ProcessInstanceDistributions.Where(_ => _.ProcessInstance.Id == contextProcessInstance.Id);
    }
}