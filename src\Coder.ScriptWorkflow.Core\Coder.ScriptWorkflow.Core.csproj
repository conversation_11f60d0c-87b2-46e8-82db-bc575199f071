﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Authors>珠海酷迪技术有限公司</Authors>
		<Company>珠海酷迪技术有限公司</Company>
		<Copyright>珠海酷迪技术有限公司@2018-2022</Copyright>
		<RootNamespace>Coder.ScriptWorkflow</RootNamespace>
		<Product>脚本工作流</Product>
		<Version>3.1.0</Version>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="TypeScript.d\dayjs.min.js" />
		<None Remove="TypeScript.d\workActivity.js" />
		<None Remove="TypeScript.d\workflowManagerPlugin.d.ts" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="TypeScript.d\dayjs.min.js" />
		<EmbeddedResource Include="TypeScript.d\workActivity.js" />
		<EmbeddedResource Include="TypeScript.d\workflowManagerPlugin.d.ts" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Coder.FileSystem.Abstractions" Version="6.3.2" />
		<PackageReference Include="Coder.FileSystem.Clients.Http" Version="6.3.3" />
		<PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.0" />

		<PackageReference Include="Coder.Member.Abstractions" Version="9.0.2" />
		<PackageReference Include="NiL.JS" Version="2.6.1700" />
		<PackageReference Include="Coder.Object2Report.Renders.NPOI" Version="1.5.0-preview1" />
		<PackageReference Include="SharpZipLib" Version="1.4.2" />
		<PackageReference Include="System.Drawing.Common" Version="8.0.10" />
	</ItemGroup>
	<ItemGroup>
		
		<ProjectReference Include="..\Coder.ScriptWorkflow.Abstractions\Coder.ScriptWorkflow.Abstractions.csproj" />
	</ItemGroup>
	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>
</Project>