﻿using System;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts;

/// <summary>
///     WorkTask脚本开始
/// </summary>
public class WorkTaskStartScript : ScriptDefined
{
    /// <summary>
    /// </summary>
    public WorkTaskStartScript()
    {
        Script = @"//processInstance 流程实例
//workActivities 所有工作做活动
";
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void Invoke(IWorkflowContext workflowContext)
    {
        if (string.IsNullOrEmpty(Script)) return;


        var context = workflowContext.BuildScriptContext();
        context.DefineVariable("workActivities").Assign(
            context.GlobalContext.ProxyValue(new ScriptWorkActivityCollection(workflowContext.AllWorkActivities))
        );
        try
        {
            context.Eval(Script);
        }
        catch (JSException ex)
        {
            var node = workflowContext.CurrentNode;
            var runTimeException = ex.ToJSException(workflowContext, "工作活动结束", Script, node);
            workflowContext.SendJsExceptionDebuggerInfo(runTimeException);
            throw runTimeException;
        }

        context.Eval("var __RESULT=JSON.stringify(processInstance.Form)");
        var result = context.GetVariable("__RESULT").Value;
        workflowContext.ProcessInstance.ReplaceForm((string)result);
    }
}