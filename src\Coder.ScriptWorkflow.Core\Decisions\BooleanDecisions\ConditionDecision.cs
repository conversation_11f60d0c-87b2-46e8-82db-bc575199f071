﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using NiL.JS.Extensions;
using Array = System.Array;

namespace Coder.ScriptWorkflow.Decisions.BooleanDecisions;

/// <summary>
///     多条件匹配
/// </summary>
public class ConditionDecision : Decision
{
    /// <summary>
    ///     多个设置
    /// </summary>
    public virtual List<ConditionSetting> Settings { get; set; } = new();

    /// <summary>
    ///     都是true
    /// </summary>
    public override bool Auto
    {
        get => true;

        set { }
    }

    public string Script { get; set; }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="nextNode"></param>
    /// <returns></returns>
    /// <exception cref="WorkflowDefinedException"></exception>
    /// <exception cref="ArgumentNullException"></exception>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        if (NextNode == null)
            throw new ArgumentNullException(nameof(NextNode), "NextNode没有设定。");
        if (!Settings.Any())
            throw new ArgumentNullException(nameof(NextNode), "多个条件没有设定。");

        var val = ExecuteScript(workflowContext);
        foreach (var conditionSetting in Settings)
            if (val == conditionSetting.MatchValue)
            {
                nextNode = conditionSetting.Node;
                return true;
            }

        nextNode = NextNode;
        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <returns></returns>
    /// <exception cref="JsRuntimeException"></exception>
    private string ExecuteScript(IWorkflowContext workflowContext)
    {
        var context = workflowContext.BuildScriptContext();
        var script = @$"
var __CALLER = () =>{{
            {Script}
            }}";

        var previous = workflowContext.AllWorkActivities;

        var was = new ScriptWorkActivityCollection(previous ?? Array.Empty<WorkActivity>());
        context.DefineVariable("workActivities").Assign(context.GlobalContext.ProxyValue(was));
        try
        {
            context.Eval(script);
            var concatFunction = context.GetVariable("__CALLER").As<Function>();
            var jsValue = concatFunction.Call(new Arguments());


            workflowContext.SendMessageDebugInfo("判断器 jsValue = " + jsValue.Value + ";,判断为:" + jsValue.Value);


            return jsValue.Value.ToString()?.Trim() ?? "";
        }
        catch (JSException ex)
        {
            throw ex.ToJSException(workflowContext, null, script, this);
        }
    }
}