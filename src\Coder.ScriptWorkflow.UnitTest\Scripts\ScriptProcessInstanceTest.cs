﻿using System;
using Coder.ScriptWorkflow.Scripts;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.Scripts;

/// <summary>
/// </summary>
public class ScriptProcessInstanceTest
{
    [Fact]
    public void TestOnStart()
    {
        var wp = new WorkProcess("name");
        var pi = new ProcessInstance(wp, "user")
        {
            Number = "Number",
            Id = 10,
            Subject = "subject"
        };

        var scriptProcessInstance = pi.ToScriptModel(true);
        Context ct = new();
        ct.DefineVariable("processInstance")
            .Assign(Context.CurrentGlobalContext.ProxyValue(scriptProcessInstance));
        var script = @"
processInstance.Subject='111112af'
";
        ct.Eval(script);

        Assert.Equal("111112af", scriptProcessInstance.Subject);
        Assert.Equal("111112af", pi.Subject);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void TestDateTimeType()
    {
        var wp = new WorkProcess("name");
        var pi = new ProcessInstance(wp, "user")
        {
            Number = "Number",
            Id = 10
        };

        pi.Complete();

        var instance = pi.ToScriptModel(true);

        instance.FinishTime = new Date(new DateTimeOffset(2010, 1, 1, 13, 0, 1, TimeSpan.FromHours(8)));
        Context ct = new();

        ct.DefineVariable("processInstance")
            .Assign(ct.GlobalContext.ProxyValue(instance));
        var script = @"
var finishTime=processInstance.FinishTime;
var createTime=processInstance.CreateTime;
var number=processInstance.Number
var id=processInstance.Id
var creator=processInstance.Creator
var status=processInstance.Status
processInstance.Form.Asdf = '123'
";
        ct.Eval(script);


        var finishTimeJValue = ct.GetVariable("finishTime");
        var finishTime = (Date)finishTimeJValue.Value;
        Assert.Equal(instance.FinishTime, finishTime);

        var createTimeJValue = ct.GetVariable("createTime");
        var createTime = (Date)createTimeJValue.Value;
        Assert.Equal(instance.CreateTime.ToDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
            createTime.ToDateTime().ToString("yyyy-MM-dd HH:mm:ss"));

        var creator = ct.GetVariable("creator");
        Assert.Equal(instance.Creator, (string)creator.Value);

        var id = ct.GetVariable("id");
        Assert.Equal(instance.Id, (int)id.Value);

        var number = ct.GetVariable("number");
        Assert.Equal(instance.Number, (string)number.Value);


        var status = ct.GetVariable("status");
        Assert.Equal(instance.Status, (int)status);

        Assert.Equal("123", instance.Form["Asdf"].Value.ToString());
    }
}