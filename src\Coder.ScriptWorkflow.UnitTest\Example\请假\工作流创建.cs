﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;

namespace Coder.ScriptWorkflow.UnitTest.Example.请假;

internal class 工作流创建
{
    public const string 总经理审批 = "总经理审批";


    public const string 主管部门审批 = "主管部门";

    public static WorkProcessSubmit Create()
    {
        var submi = new WorkProcessSubmit
        {
            Name = "demo-会签",
            Prefix = "Demo",
            Id = 5,
            Version = 1,
            Enable = true,
            OnCompleteScript = null,
            OnStartScript = "processInstance.Form.会签同意=false"
        };

        submi.Nodes = new List<NodeSubmit>();
        //草拟
        var startNodeSubmit = new StartNodeSubmit
        {
            NextNodeName = "草稿"
        };
        submi.Nodes.Add(startNodeSubmit);
        submi.Nodes.Add(new EndNodeSubmit());

        var wt = CreateTaskSubmit("草稿", 主管部门审批);
        submi.Nodes.Add(wt);
        wt.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "提交",
            Script = @"
processInstance.Form.主管部门=false
var startDate = new Date(processInstance.Form.开始日期)
var endDate = new Date(processInstance.Form.结束日期)

var millSeconds=endDate-startDate;
logger.info(millSeconds)
processInstance.Form.总天数 = millSeconds/(1*24*60*60*1000);
"
        });

        wt.Assigner = new ScriptAssignerSubmit
        {
            Script = "return processInstance.Creator"
        };


        //主管部门
        wt = CreateTaskSubmit(主管部门审批, "是否同意");
        submi.Nodes.Add(wt);

        wt.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "同意",
            Script = @"processInstance.Form.主管部门=true
"
        });
        wt.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "不同意",
            Script = @"processInstance.Form.主管部门=false"
        });

        wt.Assigner = new ScriptAssignerSubmit
        {
            Script = "return \"xugongwei\""
        };


        //test
        var sc = new BooleanScriptDecisionSubmit
        {
            Name = "是否同意",
            NextNodeName = "是否多余3天",
            ElseNodeName = "草稿",
            Script = @"
logger.Info('主管部门'+ processInstance.Form.主管部门)
return processInstance.Form.主管部门"
        };
        submi.Nodes.Add(sc);

        sc = new BooleanScriptDecisionSubmit
        {
            Name = "是否多余3天",
            NextNodeName = 总经理审批,
            ElseNodeName = "结束",
            Script = "return processInstance.Form.总天数>=3"
        };
        submi.Nodes.Add(sc);


        wt = CreateTaskSubmit(总经理审批, "总经理是否同意");
        submi.Nodes.Add(wt);
        wt.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "同意",
            Script = "processInstance.Form.总经理=true"
        });
        wt.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = "拒绝",
            Script = "processInstance.Form.总经理=false"
        });

        wt.Assigner = new ScriptAssignerSubmit
        {
            Script = "return \"wuyuefei\""
        };

        sc = new BooleanScriptDecisionSubmit
        {
            ElseNodeName = "草稿",
            Name = "总经理是否同意",
            Script = "return processInstance.Form.总经理",
            NextNodeName = "结束"
        };
        submi.Nodes.Add(sc);

        return submi;
    }

    private static WorkTaskSubmit CreateTaskSubmit(string name,
        string nextNodeName)
    {
        return new WorkTaskSubmit
        {
            Name = name,
            NextNodeName = nextNodeName
        };
    }
}