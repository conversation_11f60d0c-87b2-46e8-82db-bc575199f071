﻿using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels;

/// <summary>
///     带有form提交的接口
/// </summary>
public interface IJsonFormSubmit
{
    /// <summary>
    ///     客户端提交上来的 form数据，字符串。
    /// </summary>
    string Form { get; set; }

    /// <summary>
    ///     根据form的内容，填充自己其他属性。
    ///     原因是：form由自定义表达提交，因此可以通过自定义的表单对特定数据
    ///     进行赋值。
    ///     优先级别由集成这个接口的对象自行赋值
    /// </summary>
    JObject FillSelfByJsonForm();
}