﻿using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

internal class AssignStore<T> : IAssignStore where T : DbContext
{
    private readonly T _dbContext;

    public AssignStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    public void Delete(int id)
    {
        var asign = GetById(id);
        _dbContext.Remove(asign);
    }

    public WorkTask GetById(int id)
    {
        return _dbContext.Set<WorkTask>().FirstOrDefault(_ => _.Id == id);
    }

    public void AddOrUpdate(WorkTask workTask)
    {
        _dbContext.Update(workTask);
    }

    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }
}