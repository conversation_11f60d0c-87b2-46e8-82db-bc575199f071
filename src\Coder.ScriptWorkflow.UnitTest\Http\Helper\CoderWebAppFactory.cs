﻿using Coder.ScriptWorkflow.WebApi.Data;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;

namespace Coder.ScriptWorkflow.UnitTest.Http.Helper;

public class CoderWebAppFactory : WebApplicationFactory<TestStartup>
{
    protected override TestServer CreateServer(IWebHostBuilder builder)
    {
        builder.UseStartup<TestStartup>();
        var server = base.CreateServer(builder);
        Seed.Init(server.Host.Services, true);
        return server;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        base.ConfigureWebHost(builder);

        builder.ConfigureServices(services =>
        {
            //附加service 放在这个地方。
        });
    }

    protected override IWebHostBuilder CreateWebHostBuilder()
    {
        return WebHost.CreateDefaultBuilder()
            .UseStartup<TestStartup>();
    }
}