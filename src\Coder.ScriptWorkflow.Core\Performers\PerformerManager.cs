﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Assigners;

namespace Coder.ScriptWorkflow.Performers;

/// <summary>
/// </summary>
public class PerformerManager
{
    private readonly IPerformerQueryStore _store;

    /// <summary>
    /// </summary>
    /// <param name="store"></param>
    public PerformerManager(IPerformerQueryStore store)
    {
        _store = store;
    }

    /// <summary>
    /// </summary>
    /// <param name="roleNames"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByRoles(string[] roleNames)
    {
        if (roleNames == null) throw new ArgumentNullException(nameof(roleNames));
        return _store.GetByRolesAsync(roleNames);
    }

    /// <summary>
    /// </summary>
    /// <param name="orgNames"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByOrgs(string[] orgNames)
    {
        if (orgNames == null) throw new ArgumentNullException(nameof(orgNames));
        return _store.GetByOrgAsync(orgNames);
    }

    /// <summary>
    /// </summary>
    /// <param name="users"></param>
    /// <returns></returns>
    public Task<IEnumerable<AssignUser>> GetByUserNamesAsync(IEnumerable<string> users)
    {
        return _store.GetByUserNamesAsync(users);
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    public Task<IEnumerable<Performer>> SearchAsync(PerformerSearcher searcher)
    {
        if (searcher == null) throw new ArgumentNullException(nameof(searcher));
        return _store.SearchAsync(searcher);
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    public Task<int> CountAsync(PerformerSearcher searcher)
    {
        if (searcher == null) throw new ArgumentNullException(nameof(searcher));
        return _store.CountAsync(searcher);
    }

    /// <summary>
    /// </summary>
    /// <param name="performers"></param>
    /// <returns></returns>
    public async Task<IEnumerable<AssignUser>> FindByPerformerAsync(IEnumerable<Performer> performers)
    {
        var users = new List<string>();
        var orgs = new List<string>();
        var roles = new List<string>();

        foreach (var performer in performers)
            switch (performer.Type)
            {
                case PerformerType.Org:
                    orgs.Add(performer.Key);
                    break;
                case PerformerType.Role:
                    roles.Add(performer.Key);
                    break;
                case PerformerType.User:
                    users.Add(performer.Key);
                    break;
                default:
                    throw new WorkflowDefinedException("AssignScope 没有被识别，输入位：" + performer.Type);
            }

        var result = new ConcurrentDictionary<string, AssignUser>();
        var userTask = Task.CompletedTask;
        var roleTask = Task.CompletedTask;
        var orgTask = Task.CompletedTask;
        if (users.Any())
            userTask = GetByUserNamesAsync(users.ToArray()).ContinueWith(_ =>
            {
                foreach (var u in _.Result)
                    result.TryAdd(u.UserName, new AssignUser
                    {
                        Name = u.Name,
                        UserName = u.UserName
                    });
            });

        if (roles.Any())
            roleTask = GetByRoles(roles.ToArray()).ContinueWith(_ =>
            {
                foreach (var u in _.Result)
                    result.TryAdd(u.UserName, new AssignUser
                    {
                        Name = u.Name,
                        UserName = u.UserName
                    });
            });

        if (orgs.Any())
            orgTask = GetByOrgs(orgs.ToArray()).ContinueWith(_ =>
            {
                foreach (var u in _.Result)
                    result.TryAdd(u.UserName, new AssignUser
                    {
                        Name = u.Name,
                        UserName = u.UserName
                    });
            });

        await userTask;
        await roleTask;
        await orgTask;
        return result.Values;
    }
}