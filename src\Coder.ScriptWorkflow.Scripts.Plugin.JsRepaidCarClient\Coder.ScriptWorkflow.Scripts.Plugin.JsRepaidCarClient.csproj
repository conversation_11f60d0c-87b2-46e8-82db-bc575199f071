﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="defined.d.ts">
      <SubType>Code</SubType>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="coder.car.service.Abstractions" Version="2.0.2" />
    <PackageReference Include="coder.car.service.HttpClients" Version="2.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Coder.ScriptWorkflow.Core\Coder.ScriptWorkflow.Core.csproj" />
  </ItemGroup>

</Project>
