﻿using System.Linq;

namespace Coder.ScriptWorkflow.ViewModels;

public class SwfResult
{
    public SwfResult()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="success"></param>
    public SwfResult(string message, int code)
    {
        Message = message;
        Code = code;
    }

    /// <summary>
    /// </summary>
    public bool Success => Code == 0;

    /// <summary>
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// </summary>
    public int Code { get; set; }
}

/// <summary>
/// </summary>
public class SwfResult<T> : SwfResult
{
    /// <summary>
    /// </summary>
    public SwfResult()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    /// <param name="success"></param>
    public SwfResult(string message, int code) : base(message, code)
    {
    }


    /// <summary>
    /// </summary>
    public T Data { get; set; }
}
public static class SwfResultHelper
{
    public static SwfResult<T> ToSuccess<T>(this T data, string message="执行成功。")
    {
        return new SwfResult<T>(message, 0)
        {
            Data = data
        };
    }

    public static SwfResult<T> ToError<T>(this T o, string errorMessage, int code)
    {
        return new SwfResult<T>(errorMessage, code)
        {
            Data = o,
        };
    }
}
/// <summary>
///     分页协助函数
/// </summary>
public class Pager
{
    private int? _page = 1;

    /// <summary>
    /// </summary>
    public int? Page
    {
        get => _page <= 0 ? 1 : _page;
        set => _page = value;
    }

    /// <summary>
    /// </summary>
    public int? PageSize { get; set; } = 30;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public int GetTake()
    {
        return PageSize ?? 30;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public int GetSkip()
    {
        var result = ((Page ?? 1) - 1) * PageSize ?? 30;
        return result;
    }

    /// <summary>
    ///     因为query做分页，必须按照先skip，在take。因此多了这个方法，避免出现问题。
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <returns></returns>
    public IQueryable<T> ForPager<T>(IQueryable<T> query)
    {
        return query.Skip(GetSkip()).Take(GetTake());
    }
}