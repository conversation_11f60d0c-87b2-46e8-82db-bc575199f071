﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Stores;

namespace Coder.ScriptWorkflow.WorkTaskCommands;

/// <summary>
///     只能选择下一个节点的命令。
///     前端需要做出处理，在点击之后弹出选择框，选择下一步。
/// </summary>
public class PreviousWorkTaskCommand : WorkTaskScriptCommand
{
    /// <summary>
    ///     获取某个Work-task之前的workTask，给用户选择。
    /// </summary>
    /// <param name="workActivityId"></param>
    /// <param name="store"></param>
    /// <returns></returns>
    public IEnumerable<string> GetPreviousWorkTasks(WorkActivity workActivityId, IWorkActivityStore store)
    {
        return store.WasRun(workActivityId);
    }
}