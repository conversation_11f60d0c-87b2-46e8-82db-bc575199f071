﻿using Coder.ScriptWorkflow.Logger;

namespace Coder.ScriptWorkflow.Scripts.Plugins.Logger;

/// <summary>
/// </summary>
public class Logger
{
    private readonly IWorkflowContext _context;
    private readonly WorkflowLogManager _logger;

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="logManager"></param>
    public Logger(IWorkflowContext context, WorkflowLogManager logManager)
    {
        _context = context;
        _logger = logManager;
    }

    /// <summary>
    /// </summary>
    /// <param name="args"></param>
    public void Error(params string[] args)
    {
        string content = string.Join(",", args);
        System.Console.WriteLine($"Error:{WorkflowLogType.Script}, {content}");
        _logger.LogError(_context, WorkflowLogType.Script, content);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="content"></param>
    public void error(params string[]  content)
    {
        Error(content);
    }

    /// <summary>
    /// </summary>
    /// <param name="args"></param>
    public void Info(params string[] args)
    {
        string content = string.Join(",", args);
        System.Console.WriteLine($"Info:{WorkflowLogType.Script}, {content}");
        _logger.LogInfo(_context, WorkflowLogType.Script, content);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="args"></param>
    public void info(params string[] args)
    {
        string content = string.Join(",", args);
        Info(content);
    }
}