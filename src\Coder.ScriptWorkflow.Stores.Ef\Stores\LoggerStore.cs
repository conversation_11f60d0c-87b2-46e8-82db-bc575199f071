﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace Coder.ScriptWorkflow.Stores;

public class LoggerStore<T> : ILoggerStore where T : DbContext
{
    private readonly T _dbContext;

    public LoggerStore(T dbContext)
    {
        _dbContext = dbContext;
    }

    public void Delete(int id)
    {
        var entity = GetById(id);
        _dbContext.Remove(entity);
    }

    public WorkflowLog GetById(int id)
    {
        return _dbContext.Set<WorkflowLog>().FirstOrDefault(_ => _.Id == id);
    }

    public void AddOrUpdate(WorkflowLog workTask)
    {
        if (workTask.Id == 0)
            _dbContext.Add(workTask);
        else
            _dbContext.Update(workTask);
    }

    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    public IEnumerable<WorkflowLog> Find(WorkflowLogSearcher searcher)
    {
        var query = MakeQuery(searcher);

        return query.Skip((searcher.Page - 1) * searcher.PageSize).Take(searcher.PageSize)
            .OrderByDescending(_ => _.CreateTime)
            .ToList();
    }

    public int Count(WorkflowLogSearcher searcher)
    {
        var query = MakeQuery(searcher);
        return query.Count();
    }

    private IQueryable<WorkflowLog> MakeQuery(WorkflowLogSearcher searcher)
    {
        var query = _dbContext.Set<WorkflowLog>()
            .Where(_ => searcher.ProcessInstanceNumber == null || _.ProcessInstanceNumber == searcher.ProcessInstanceNumber);
        return query;
    }
}