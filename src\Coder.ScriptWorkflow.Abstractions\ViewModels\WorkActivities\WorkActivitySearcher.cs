﻿using System;

namespace Coder.ScriptWorkflow.ViewModels.WorkActivities;

public class RestartWorkActivitySubmit
{
    public int WorkActivityId { get; set; }

    public string User { get; set; }
}
/// <summary>
///     共做活动查询输入
/// </summary>
public class WorkActivitySearcher : Pager

{
    private DateTime? _createTimeEnd;
    private DateTime? _createTimeStart;
    private DateTime? _startTime;
    private string _number;

    /// <summary>
    /// </summary>
    public string[] Roles { get; set; }

    /// <summary>
    ///     组织单元
    /// </summary>
    public string[] Orgs { get; set; }

    /// <summary>
    ///     工作活动状态
    /// </summary>
    public WorkActivityStatus[] Statuses { get; set; }

    /// <summary>
    ///     流程类型
    /// </summary>
    public string[] WorkProcessName { get; set; }

    /// <summary>
    ///     流程id
    /// </summary>
    public int? WorkProcessId { get; set; }

    /// <summary>
    ///     工作流编号
    /// </summary>
    public string Number
    {
        get => _number;
        set => _number = value?.Trim();
    }

    public string Subject { get; set; }
    /// <summary>
    ///     手机端查询专用Name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     手机端查询专用StartTime
    /// </summary>
    public DateTime? StartTime
    {
        get => _startTime;
        set => _startTime = DateHelp.GetDateS(value);
    }
    
    public string Creator { get; set; }

    public DateTime? CreateTimeStart
    {
        get => _createTimeStart;
        set => _createTimeStart = DateHelp.GetDateS(value);
    }

    public DateTime? CreateTimeEnd
    {
        get => _createTimeEnd;
        set => _createTimeEnd = DateHelp.GetDateE(value);
    }
}

/// <summary>
/// </summary>
public class ListWorkActivityByProcessInstanceSearch : Pager
{
    private string _workTask;

    /// <summary>
    /// </summary>
    public WorkActivityStatus[] Statuses { get; set; }

    /// <summary>
    ///     工作任务的名称
    /// </summary>
    public string WorkTask
    {
        get => _workTask;
        set => _workTask = value?.Trim();
    }
}