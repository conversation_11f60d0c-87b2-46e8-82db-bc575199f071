﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

internal class TagMapping : IEntityTypeConfiguration<Tag>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    public TagMapping(string prefix)
    {
        _prefix = prefix;
    }

    public void Configure(EntityTypeBuilder<Tag> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id)
            .ValueGeneratedOnAdd();

        builder.Property(_ => _.Number);
        builder.Property(_ => _.Name);

        builder.ToTable($"{_prefix}_PI_Tags").HasComment("工作流程标记");
    }
}