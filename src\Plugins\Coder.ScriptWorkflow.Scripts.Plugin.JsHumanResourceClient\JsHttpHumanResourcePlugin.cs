﻿using Coder.ScriptWorkflow.Scripts.Plugins;
using NiL.JS.Core;

namespace Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient
{
    public class JsHttpHumanResourcePlugin : IPlugin
    {
        private readonly HumanResourceHttpFactory _humanResourceHttpFactory;

        public JsHttpHumanResourcePlugin(HumanResourceHttpFactory humanResourceHttpFactory)
        {
            _humanResourceHttpFactory = humanResourceHttpFactory;
        }

        public string Name { get; set; } = "jsHumanResource";
        public object GetObject(IWorkflowContext context)
        {
            return new HumanResourceHttpClient(context, _humanResourceHttpFactory);
        }

        public void Dispose(object o)
        {
        }


        public string GetJsDefined()
        {
            var stream = typeof(JsHttpHumanResourcePlugin).Assembly
                .GetManifestResourceStream("Coder.ScriptWorkflow.Scripts.Plugin.JsHumanResourceClient.defined.d.ts");
            if (stream == null)
                throw new ArgumentNullException("内置文件不存在");

            using var reader = new StreamReader(stream);
            var txt = reader.ReadToEnd();
            return txt;
        }
    }
}
