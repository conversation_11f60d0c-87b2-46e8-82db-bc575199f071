﻿using System;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.PresistentTest;

/// <summary>
/// </summary>
public class DefinedTest
{
    /// <summary>
    ///     i
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<AppContext>();
        });

        var dbFile = Guid.NewGuid().ToString("N");

        service.AddDbContext<AppContext>(options =>
        {
            options.UseLazyLoadingProxies();
            options.UseSqlite($"Data Source={dbFile}.db;");
        });

        var sp = service.BuildServiceProvider();

        using (var scope = sp.CreateScope())
        {
            var services = scope.ServiceProvider;
            var dbContext = services.GetRequiredService<AppContext>();
            // only for unit-tet
            dbContext.Database.EnsureDeleted();
            dbContext.Database.EnsureCreated();
        }

        return sp;
    }

    [Fact]
    public async Task MultiEntryPoint()
    {
        var sp = Sp();
        /*
        * 草拟-》审批1-》审批2=》发布
         * 后面 3个worktask都返回Task
        * 测试用例，审批先拒绝一次，然后再执行同意。
        */
        using var scope = sp.CreateScope();
        var sp1 = scope.ServiceProvider;
        var wpStore = sp1.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp1.GetRequiredService<IWorkTaskStore>();

        var workProcess = new WorkProcess("流程1")
        {
            Enable = true
        };
        wpStore.AddOrUpdate(workProcess);
        await wpStore.SaveChangesAsync();


        var worktask1 = new WorkTask("草拟", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'user'"
            }
        };
        var worktask2 = new WorkTask("审批1", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'user'"
            }
        };
        var worktask3 = new WorkTask("审批2", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'user'"
            }
        };
        var worktask4 = new WorkTask("发布", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'user'"
            }
        };

        var dc1 = new BoolScriptDecision
        {
            NextNode = worktask1,
            ElseNode = worktask1
        };

        worktask1.NextNode = worktask2;
        worktask2.NextNode = worktask3;
        worktask3.NextNode = worktask4;
        worktask4.NextNode = new EndNode();

        wtStore.AddOrUpdate(worktask1);
        wtStore.AddOrUpdate(worktask2);
        wtStore.AddOrUpdate(worktask3);
        wtStore.AddOrUpdate(worktask4);
        wpStore.SaveChangesAsync().Wait();

        scope.Dispose();

        using var scope2 = sp.CreateScope();
        var sp2 = scope2.ServiceProvider;


        wpStore = sp2.GetRequiredService<IWorkProcessStore>();
        wtStore = sp2.GetRequiredService<IWorkTaskStore>();
        var checkWt1 = wtStore.GetById(worktask1.Id);
        Assert.Equal(worktask2.Id, checkWt1.NextNode.Id);
        Assert.Equal(worktask3.Id, checkWt1.NextNode.NextNode.Id);
        Assert.Equal(worktask4.Id, checkWt1.NextNode.NextNode.NextNode.Id);
    }

    public class AppContext : DbContext
    {
        /// <summary>
        /// </summary>
        /// <param name="options"></param>
        public AppContext(DbContextOptions<AppContext> options) : base(options)
        {
        }

        /// <summary>
        /// </summary>
        /// <param name="builder"></param>
        protected override void OnModelCreating(ModelBuilder builder)
        {
            // reference   f;

            builder.AddEfModels("Test");

            base.OnModelCreating(builder);
        }
    }
}