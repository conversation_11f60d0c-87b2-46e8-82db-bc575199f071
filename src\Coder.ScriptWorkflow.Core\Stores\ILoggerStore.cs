﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.ViewModels;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
/// </summary>
public interface ILoggerStore
{
    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    void Delete(int id);

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    WorkflowLog GetById(int id);

    /// <summary>
    /// </summary>
    /// <param name="workTask"></param>
    void AddOrUpdate(WorkflowLog workTask);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    Task SaveChangesAsync();

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    IEnumerable<WorkflowLog> Find(WorkflowLogSearcher searcher);

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <returns></returns>
    int Count(WorkflowLogSearcher searcher);
}