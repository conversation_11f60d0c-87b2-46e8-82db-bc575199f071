﻿using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Filtters;

/// <summary>
/// 全局异常处理过滤器
/// </summary>
public class CustomExceptionFilterAttribute : ExceptionFilterAttribute
{
    private readonly IWebHostEnvironment _hostingEnvironment;
    private readonly ILogger<CustomExceptionFilterAttribute> _logger;

    public CustomExceptionFilterAttribute(
        IWebHostEnvironment hostingEnvironment,
        ILogger<CustomExceptionFilterAttribute> logger)
    {
        _hostingEnvironment = hostingEnvironment ?? throw new ArgumentNullException(nameof(hostingEnvironment));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public override void OnException(ExceptionContext context)
    {
        if (context.ExceptionHandled)
            return;

        var exception = context.Exception;
        var requestPath = context.HttpContext.Request.Path.Value;
        var userId = context.HttpContext.User.Identity?.Name ?? "Anonymous";

        _logger.LogError(exception, 
            "未处理的异常发生在 {RequestPath}，用户: {UserId}", 
            requestPath, userId);

        var response = CreateErrorResponse(exception);
        
        context.HttpContext.Response.StatusCode = GetStatusCode(exception);
        context.Result = new JsonResult(response);
        context.ExceptionHandled = true;
    }

    private object CreateErrorResponse(Exception exception)
    {
        var isDevelopment = _hostingEnvironment.IsDevelopment();
        
        return new
        {
            success = false,
            message = GetUserFriendlyMessage(exception),
            timestamp = DateTimeOffset.UtcNow,
            details = isDevelopment ? new
            {
                type = exception.GetType().Name,
                stackTrace = exception.StackTrace,
                innerException = exception.InnerException?.Message
            } : null
        };
    }

    private static string GetUserFriendlyMessage(Exception exception)
    {
        return exception switch
        {
            ArgumentException => "请求参数无效",
            UnauthorizedAccessException => "访问被拒绝",
            TimeoutException => "操作超时，请稍后重试",
            InvalidOperationException => "当前操作无效",
            _ => "系统处理请求时发生错误，请稍后重试"
        };
    }

    private static int GetStatusCode(Exception exception)
    {
        return exception switch
        {
            ArgumentException => StatusCodes.Status400BadRequest,
            UnauthorizedAccessException => StatusCodes.Status401Unauthorized,
            TimeoutException => StatusCodes.Status408RequestTimeout,
            _ => StatusCodes.Status500InternalServerError
        };
    }
}