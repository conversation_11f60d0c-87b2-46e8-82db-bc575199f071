﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Decisions;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;
using Coder.WebHttpClient;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.WebApi.Controllers;

/// <summary>
///     工作流定义接口
/// </summary>
[ApiController]
[Route("[controller]")]
[EnableCors]
[Authorize(Roles = "admin")]
public class DefinedController : Controller
{
    private readonly ILogger<DefinedController> _logger;
    private readonly WorkflowDefinedManager _manager;


    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="manager">定于manager</param>
    /// <param name="logger">logger</param>
    public DefinedController(WorkflowDefinedManager manager, ILogger<DefinedController> logger)
    {
        _manager = manager;
        _logger = logger;
    }

    /// <summary>
    ///     保存工作流程定义对象
    /// </summary>
    /// <param name="submit"></param>
    /// <returns></returns>
    [HttpPost("submit")]
    [ProducesDefaultResponseType(typeof(SwfResult<SaveWorkProcessResult>))]
    public IActionResult Submit([FromBody] WorkProcessSubmit submit)
    {
        try
        {
            submit.Creator = User.Identity.Name;
            var result = _manager.Save(submit);
            return Ok(result);
        }
        catch (WorkflowDefinedException ex)
        {
            return Ok(new { id = submit.Id, success = false, message = ex.Message });
        }
    }


    /// <summary>
    ///     通过工作流名称获取工作流生效最新的定义。
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="workProcessStore">工作流数据访问</param>
    /// <param name="nodeStore"></param>
    /// <returns></returns>
    [HttpGet("get-by-name/{name}")]
    [ProducesDefaultResponseType(typeof(SwfResult<WorkProcessSubmit>))]
    [ProducesResponseType(typeof(ResponseMessage), 404)]
    public async Task<IActionResult> GetByName(string name, [FromServices] IWorkProcessStore workProcessStore,
        [FromServices] INodeStore nodeStore)
    {
        var wf = workProcessStore.GetByEffectName(name);
        if (wf == null) return NotFound(new { success = false, message = "没有找到" + name + "的工作流，或者该工作流没有发布" });
        var nodes = await nodeStore.GetNodesByWorkProcessAsync(wf);
        var result = wf.ToSubmitViewModel(nodes);
        return Ok(result.ToSuccess());
    }

    /// <summary>
    ///     通过工作流ID称获取工作流定义。
    /// </summary>
    /// <param name="id"></param>
    /// <param name="workProcessStore">workProcessStore</param>
    /// <param name="nodeStore"></param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ResponseMessage), 404)]
    [ProducesDefaultResponseType(typeof(WorkProcessSubmit))]
    public IActionResult GetById([FromRoute] int id, [FromServices] IWorkProcessStore workProcessStore,
        [FromServices] INodeStore nodeStore)
    {
        var workProcess = workProcessStore.WorkProcesses.Include(_ => _.OnCancel).Include(_ => _.OnComplete)
            .Include(_ => _.OnStart).FirstOrDefault(_ => id == _.Id);

        if (workProcess == null) return NotFound(new { success = false, message = $"无法找打id={id}的工作流" });

        var stopWatch = new Stopwatch();
        stopWatch.Start();
        var nodes = nodeStore.GetNodesByWorkProcessAsync(workProcess).Result;
        stopWatch.Stop();
        _logger.LogInformation("通过Id获取工作流定义。耗时：" + stopWatch.Elapsed.TotalSeconds);
        return Ok(workProcess.ToSubmitViewModel(nodes).ToSuccess());
    }

    /// <summary>
    ///     列出一个工作下的多个版本
    /// </summary>
    /// <param name="name"></param>
    /// <param name="workProcessStore"></param>
    /// <returns></returns>
    [HttpGet("{name}/list")]
    [ProducesDefaultResponseType(typeof(IEnumerable<WorkProcessListItem>))]
    public IActionResult ListByName([FromRoute] string name, [FromServices] IWorkProcessStore workProcessStore)
    {
        var wf = workProcessStore.ListByName(name);
        return Ok(wf.Select(_ => _.ToViewModel()));
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcessStore"></param>
    /// <returns></returns>
    [HttpGet("list-all-name")]
    [ProducesDefaultResponseType(typeof(IEnumerable<string>))]
    public IActionResult ListDistinct([FromServices] IWorkProcessStore workProcessStore)
    {
        var nams = workProcessStore.WorkProcesses.Select(_ => _.Name).Distinct();

        return Ok(nams);
    }


    /// <summary>
    ///     开启一个工作定义。创建新的工作流实例的时候，会自动采用
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut("enable/{id}")]
    [ProducesResponseType(typeof(ResponseMessage), 400)]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public IActionResult Publish([FromRoute] int id)
    {
        try
        {
            var wf = _manager.Enable(id, true);
            return Ok(new { success = true, message = wf.Name + "版本 " + wf.Version + "已经生效。" });
        }
        catch (WorkflowDefinedException ex)
        {
            return BadRequest(new { success = false, message = "开启失败:" + ex.Message });
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut("disable/{id}")]
    [ProducesDefaultResponseType(typeof(ResponseMessage))]
    public IActionResult Disable([FromRoute] int id)
    {
        var wf = _manager.Enable(id, false);

        return Ok(new { success = true, message = wf.Name + "版本 " + wf.Version + "已经失效。" });
    }

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="version"></param>
    /// <param name="notIncludeId"></param>
    /// <returns></returns>
    [HttpGet("exist-work-process-name/{name}")]
    public IActionResult ExistWorkProcessName([FromRoute] string name, [FromQuery] int? version,
        [FromQuery] int? notIncludeId)
    {
        var exist = _manager.IsExistWorkProcessName(name, version, notIncludeId);
        return Ok(exist.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="workProcessId"></param>
    /// <param name="notIncludeId"></param>
    /// <returns></returns>
    [HttpGet("exist-work-task-name/{workProcessId}/{name}")]
    public IActionResult ExistTask([FromRoute] string name, [FromRoute] int workProcessId,
        [FromQuery] int? notIncludeId)
    {
        var exit = _manager.IsExistWorkTaskName(workProcessId, name, notIncludeId);
        return Ok(exit.ToSuccess());
    }

    /// <summary>
    ///     检查version是否存在。
    /// </summary>
    /// <param name="id"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [HttpGet("check-version/{id}")]
    [ProducesDefaultResponseType(typeof(SwfResult<CheckVersionResult>))]
    public SwfResult<CheckVersionResult> CheckVersion([FromRoute] int id, [FromQuery] int version)
    {
        var exist = _manager.CheckVersionExist(id, version);
        return exist;
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="submit"></param>
    /// <param name="workTaskStore"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    /// <exception cref="System.ArgumentOutOfRangeException"></exception>
    [HttpPut("change-script-work-task/{id}")]
    [ProducesDefaultResponseType(typeof(SwfResult<string>))]
    public async Task<IActionResult> ChangeWorkTaskScript([FromRoute] int id,
        [FromForm] DebuggerScriptChangeSubmit submit,
        [FromServices] IWorkTaskStore workTaskStore, [FromServices] WorkflowPermissionManager permissionManager)
    {
        if (permissionManager == null) throw new ArgumentNullException(nameof(permissionManager));
        var workTask = workTaskStore.GetById(id);

        if (!permissionManager.IsManage(workTask.WorkProcess, User))
            return Ok(new { success = true, message = "你无权改动本工作流。" });

        switch (submit.ScriptType)
        {
            case "start":
                workTask.WorkTaskStartScript ??= new WorkTaskStartScript();
                workTask.WorkTaskStartScript.Script = submit.Script;
                break;
            case "workActivity-complete":
                workTask.WorkActivityCompleteScript ??= new WorkActivityScript();
                workTask.WorkActivityCompleteScript.Script = submit.Script;
                ;
                break;
            case "workTask-complete":
                workTask.WorkTaskCompleteScript ??= new WorkTaskCompleteScript();
                workTask.WorkTaskCompleteScript.Script = submit.Script;
                ;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(submit),
                    "DebuggerScriptChangeSubmit.ScripType 必须是 start/workActivity-complete/workTask-complete之一。 ");
        }

        workTaskStore.AddOrUpdate(workTask);
        await workTaskStore.SaveChangesAsync();
        return Ok("保存成功".ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="submit"></param>
    /// <param name="nodeStore"></param>
    /// <returns></returns>
    [HttpPut("change-script-decison/{id}")]
    public async Task<IActionResult> ChangeDecisionScript([FromRoute] int id,
        [FromBody] DebuggerScriptChangeSubmit submit, [FromServices] INodeStore nodeStore,
        [FromServices] WorkflowPermissionManager permissionManager)
    {
        var workTask = (ScriptDecision)nodeStore.GetById(id);
        if (!permissionManager.IsManage(workTask.WorkProcess, User))
            return Ok(new { success = true, message = "你无权改动本工作流。" });

        workTask.Script = submit.Script;
        nodeStore.AddOrUpdate(workTask);
        await nodeStore.SaveChangesAsync();
        return Ok(new { success = true, message = "成功保存。" }.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="id">工作流定义id</param>
    /// <param name="submit">提交的script</param>
    /// <param name="workProcessStore"></param>
    /// <returns></returns>
    /// <exception cref="System.ArgumentOutOfRangeException"></exception>
    [HttpPut("change-process/{id}")]
    public async Task<IActionResult> ChangeProcessScript([FromRoute] int id,
        [FromBody] DebuggerScriptChangeSubmit submit, [FromServices] IWorkProcessStore workProcessStore,
        [FromServices] WorkflowPermissionManager permissionManager)
    {
        var wp = workProcessStore.Get(id);
        if (!permissionManager.IsManage(wp, User)) return Ok(new { success = true, message = "你无权改动本工作流。" });

        switch (submit.ScriptType)
        {
            case "start":
                wp.OnStart ??= new WorkProcessScript();
                wp.OnStart.Script = submit.Script;
                break;
            case "cancel":
                wp.OnCancel ??= new WorkProcessScript();
                wp.OnCancel.Script = submit.Script;
                break;
            case "complete":
                wp.OnComplete ??= new WorkProcessScript();
                wp.OnComplete.Script = submit.Script;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(submit),
                    "DebuggerScriptChangeSubmit.ScripType 必须是 start/cancel/complete 之一。 ");
        }


        workProcessStore.AddOrUpdate(wp);
        await workProcessStore.SaveChangesAsync();
        return Ok(new { success = true, message = "成功保存。" }.ToSuccess());
    }

    /// <summary>
    /// </summary>
    /// <param name="globalScriptStore"></param>
    /// <returns></returns>
    [HttpGet("list-global-script")]
    public async Task<IActionResult> ListAllGlobalScript([FromServices] IGlobalScriptStore globalScriptStore)
    {
        var r = await globalScriptStore.ListAsync();
        return Ok(r.Select(_ => new GlobalScriptSubmit
        {
            Name = _.Name,
            Id = _.Id,
            Script = _.Script?.Length > 30 ? _.Script.Substring(0, 30) + "..." : _.Script
        }).ToSuccess());
    }

    /// <summary>
    ///     获取Id。
    /// </summary>
    /// <param name="globalScriptStore"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("global-script/{id}")]
    public async Task<IActionResult> Get([FromServices] IGlobalScriptStore globalScriptStore, [FromRoute] int id)
    {
        var result = await globalScriptStore.GetAsync(id);
        return Ok(new GlobalScriptSubmit
        {
            Name = result.Name,
            Id = result.Id,
            Script = result.Script
        }.ToSuccess());
    }

    /// <summary>
    ///     删除全部对象。
    /// </summary>
    /// <param name="globalScriptStore"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("global-script/{id}")]
    public async Task<IActionResult> ListAllGlobalScript([FromServices] IGlobalScriptStore globalScriptStore,
        [FromRoute] int id)
    {
        await globalScriptStore.DeleteAsync(id);
        return Ok(new { success = true, message = "保存成功。" });
    }

    /// <summary>
    ///     单独保存脚本
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    [HttpPost("save-script")]
    public async Task<IActionResult> Save([FromBody] WorkProcessScriptSubmit item)
    {
        var responseMessage = await _manager.SaveScript(item);
        return Ok(responseMessage);
    }
}