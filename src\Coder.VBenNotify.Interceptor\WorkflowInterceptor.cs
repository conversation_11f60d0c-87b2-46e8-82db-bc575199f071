﻿using System.Diagnostics;
using Coder.Notification.Clients;
using Coder.Notification.ViewModels;
using Coder.ScriptWorkflow;
using Coder.ScriptWorkflow.Interceptors;
using Newtonsoft.Json;

namespace Coder.VBenNotify.Interceptor;
/// <summary>
/// 
/// </summary>
public class WorkflowInterceptor : IWorkflowInterceptor
{
    /**
     * * 通知采用一下模式进行
     * *  {
     * id: '000000001',
     * avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
     * title: '你收到了 14 份新周报',
     * description: '',
     * datetime: '2017-08-09',
     * type: '1',
     * },
     */
    private readonly INotificationClient _notificationClient;

    public WorkflowInterceptor(INotificationClient notificationClient)
    {
        _notificationClient = notificationClient;
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="processInstance"></param>
    /// <param name="provider"></param>
    public void OnProcessChanged(IWorkflowContext topContext, ProcessInstance processInstance, IServiceProvider provider)
    {

    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="topContext"></param>
    /// <param name="workActivity"></param>
    /// <param name="provider"></param>
    public void OnWorkActivityChanged(IWorkflowContext topContext, WorkActivity workActivity, IServiceProvider provider)
    {
        //throw new NotImplementedException();
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="userName"></param>
    /// <returns></returns>
    public async Task NotifyDistributionUserAsync(IWorkflowContext workflowContext, IEnumerable<string> userName)
    {
        var processInstance = workflowContext.ProcessInstance;
        var messageBody = new VBenMessageBody
        {
            avatar = "workflow-notify|svg",
            type = "工作",
            title = processInstance.WorkProcess.Name + "发生改变",
            description = processInstance.Subject ?? processInstance.Number + "发生了改变，点击查看详细内容",
        };

        var performers = userName.Select(_ => new Performer()
        {
            Name = _,
            Key = _,
            Type = AssignScope.User
        }).ToList();

        var r = await _notificationClient.SaveOrUpdateAsync(new MessageSubmit
        {
            RefId = processInstance.Id.ToString(),
            TypeName = "工作",
            Content = JsonConvert.SerializeObject(messageBody),
            Creator = processInstance.Creator,
            Performers = performers,
        });
        //workflowContext.Debugger?.SendMessage(workflowContext, "分发-通知", $"发送{(r.Data.Success ? "成功" : "失败")},返回信息:{r.Message}");
    }
}