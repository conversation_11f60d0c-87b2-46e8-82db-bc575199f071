﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text.RegularExpressions;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.DtoTranslator.Define;
using Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Settings;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Coder.ScriptWorkflow.ViewModels.Settings;
using Coder.ScriptWorkflow.ViewModels.WorkTaskCommand;
using Coder.ScriptWorkflow.WorkTaskCommands;
using NiL.JS.Core;

// ReSharper disable once CheckNamespace
namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public static class DtoExtensions
{
    //(2:13)
    private static readonly Regex GetJsRegex = new("\\((\\d+):(\\d+)\\)");

    //2:13
    private static readonly Regex GetCodeLineFromStack = new("at anonymous:line (\\d+):(\\d+)");

    /// <summary>
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    public static WorkTaskCommandViewModel ToViewModel(this WorkTaskCommand command)
    {
        var result = new WorkTaskCommandViewModel
        {
            Name = command.Name
        };
        switch (command)
        {
            case PreviousWorkTaskCommand:
                result.Type = WorkTaskCommandSubmitJsonConverter.PreviousCommandType;
                break;
            case WorkTaskScriptCommand:
                result.Type = WorkTaskCommandSubmitJsonConverter.WorkTaskCommandScriptType;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(command));
        }

        return result;
    }


    /// <summary>
    /// </summary>
    /// <param name="script"></param>
    /// <returns></returns>
    public static WorkTaskScriptCommandSubmit ToViewModel(this WorkTaskScriptCommand script)
    {
        if (script == null) throw new ArgumentNullException(nameof(script));
        var result = new WorkTaskScriptCommandSubmit();
        result.Id = script.Id;
        result.Script = script.Script;
        result.Name = script.Name;
        result.Order = script.Order;
        return result;
    }


    /// <summary>
    /// </summary>
    /// <param name="descDecision"></param>
    /// <returns></returns>
    public static BooleanScriptDecisionSubmit ToViewModel(this BoolScriptDecision descDecision)
    {
        var script = new ScriptDecisionTranslator();
        return script.ToViewModel(descDecision);
    }


    /// <summary>
    /// </summary>
    /// <param name="startNode"></param>
    /// <returns></returns>
    public static StartNodeSubmit ToSubmitViewModel(this StartNode startNode)
    {
        return new StartNodeTranslator().ToViewModel(startNode);
    }

    /// <summary>
    /// </summary>
    /// <param name="startNode"></param>
    /// <returns></returns>
    public static EndNodeSubmit ToSubmitViewModel(this EndNode startNode)
    {
        return new EndNodeTranslator().ToViewModel(startNode);
    }

    /// <summary>
    /// </summary>
    /// <param name="ex"></param>
    /// <param name="node"></param>
    /// <param name="eventNameOrCommand"></param>
    /// <param name="script"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    [return: NotNull]
    public static JsRuntimeException ToJSException(this JSException ex, IWorkflowContext context,
        string eventNameOrCommand, string script, [AllowNull] Node node)
    {
        var isPluginError = ex.SourceCode == null;


        var result = isPluginError
            ? new JsRuntimeException(context, ex.Message.Replace("TypeError: ", "插件出错: "))
            : new JsRuntimeException(context, ex);
        result.Code = script;
        result.EventName = eventNameOrCommand;

        var codeCoordinates = ex.CodeCoordinates;
        if (codeCoordinates == null)
        {
            var match = GetJsRegex.Match(ex.Message);
            if (match.Success)
            {
                var lien = match.Groups[1].Value;
                var col = match.Groups[2].Value;
                codeCoordinates = new CodeCoordinates(
                    Convert.ToInt32(lien),
                    Convert.ToInt32(col),
                    0
                );
            }
            else if (ex.StackTrace != null)
            {
                match = GetCodeLineFromStack.Match(ex.StackTrace);
                if (match.Success)
                {
                    var lien = match.Groups[1].Value;
                    var col = match.Groups[2].Value;
                    codeCoordinates = new CodeCoordinates(
                        Convert.ToInt32(lien),
                        Convert.ToInt32(col),
                        0
                    );
                }
            }
        }
        else
        {
            result.Coordinates.Line = codeCoordinates.Line;
            result.Coordinates.Column = codeCoordinates.Column;
            result.Coordinates.Length = codeCoordinates.Length;
        }


        result.NodeName = node?.Name;
        if (isPluginError)
        {
            if (ex.InnerException is PluginException pluginException) result.PluginName = pluginException.PluginName;
            result.PluginErrorMessage = ex.InnerException?.Message;
            result.PluginErrorStack = ex.InnerException?.StackTrace;
        }

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="ex"></param>
    /// <param name="eventNameOrCommand"></param>
    /// <param name="script"></param>
    /// <returns></returns>
    public static JsRuntimeException ToJSException(this JSException ex, [AllowNull] IWorkflowContext context,
        string eventNameOrCommand, string script)
    {
        return ex.ToJSException(context, eventNameOrCommand, script, null);
    }

    /// <summary>
    /// </summary>
    /// <param name="result"></param>
    /// <param name="runtimeException"></param>
    public static void FillCodeResult(this SwfExecuteResult result, JsRuntimeException runtimeException)
    {
        result.ErrorScript ??= new ScriptError();
        result.ErrorScript.PluginErrorStack = runtimeException.PluginErrorStack;
        result.ErrorScript.PluginName = runtimeException.PluginName;
        result.ErrorScript.Code = runtimeException.Code;
        result.ErrorScript.EventName = runtimeException.EventName;
        if (runtimeException.Coordinates.Line != 0)
            result.ErrorScript.Coordinates = runtimeException.Coordinates;

        result.ErrorScript.PluginName = runtimeException.PluginName;
        result.ErrorScript.Reason = runtimeException.Message;
        result.ErrorScript.NodeName = runtimeException.NodeName;
    }


    public static SwfMakeTagsSettingSubmit ToViewModel(this SwfMakeTagsSetting setting)
    {
        return new SwfMakeTagsSettingSubmit
        {
            Id = setting.Id,
            Plugins = setting.Plugins.ToArray(),
            Script = setting.Script
        };
    }

    public static void Fill(this SwfMakeTagsSettingSubmit submit, SwfMakeTagsSetting setting)
    {
        setting.Script = submit.Script;
        foreach (var plugin in setting.Plugins)
            setting.Plugins.Add(plugin);
    }
}