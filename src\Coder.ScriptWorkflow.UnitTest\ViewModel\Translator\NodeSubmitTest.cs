﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.ViewModel.Translator;

/// <summary>
/// </summary>
public class NodeSubmitTest
{
    /// <summary>
    /// </summary>
    [Fact]
    public void Test_StartNode()
    {
        var t = new StartNode();
        t.Id = 1;

        t.NextNode = new EndNode();

        var startNodeSubmit = t.ToSubmitViewModel();
        Assert.Equal(1, startNodeSubmit.Id);
        Assert.Equal(t.NextNode.Name, startNodeSubmit.NextNodeName);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void Test_EndNode()
    {
        var t = new EndNode();
        t.Id = 1;
        var startNodeSubmit = t.ToSubmitViewModel();
        Assert.Equal(1, startNodeSubmit.Id);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void AllTest()
    {
        /*
        * 草拟-》审批-》发布
        * 测试用例，审批先拒绝一次，然后再执行同意。
        */


        var workProcess = new WorkProcess("流程1")
        {
            Enable = true
        };
        var nodes = new List<Nodes.Node>();

        var worktask1 = new WorkTask("草拟", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'user'"
            }
        };

        worktask1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意"
        });
        var worktask2 = new WorkTask("审批", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'auth-user'"
            }
        };
        worktask2.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意",
            Script = "processInstance.Form='同意'"
        });
        worktask2.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "拒绝",
            Script = "processInstance.Form='拒绝'"
        });
        var worktask3 = new WorkTask("发布", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'pub-user'"
            },
            NextNode = new EndNode()
        };
        worktask3.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "发布",
            Script = "processInstance.Form='发布'"
        });
        var startNode = new StartNode
        {
            NextNode = worktask1
        };

        nodes.AddRange(new Nodes.Node[] { worktask1, worktask2, worktask3, startNode });

        worktask1.NextNode = worktask2;

        var autCondition = new BoolScriptDecision(worktask3, worktask1)
        {
            Script = "return processInstance.Form=='同意'"
        };
        worktask2.NextNode = autCondition;

        var publishCondition = new BoolScriptDecision(new EndNode(), worktask1);
        publishCondition.Script = "return processInstance.Form=='发布'";
        worktask3.NextNode = publishCondition;


        var workflowSubmit = workProcess.ToSubmitViewModel(nodes);

        Assert.Equal("流程1", workflowSubmit.Name);
    }
}