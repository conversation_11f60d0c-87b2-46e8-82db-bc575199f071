﻿using System;
using Coder.ScriptWorkflow.NumberGenerators;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class ProcessInstanceMapping : IEntityTypeConfiguration<ProcessInstance>
{
    private readonly NumberFrom20200101Generator _numberGenerator;


    private readonly string prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    /// <param name="numberGenerator"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public ProcessInstanceMapping(string prefix, NumberFrom20200101Generator numberGenerator)
    {
        this.prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
        _numberGenerator = numberGenerator;
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ProcessInstance> builder)
    {
        builder.HasKey(piBuilder => piBuilder.Id);

        builder.Property(piBuilder => piBuilder.Id).ValueGeneratedOnAdd();
        builder.Property(piBuilder => piBuilder.Subject).HasMaxLength(500);
        builder.Property(piBuilder => piBuilder.Number).HasMaxLength(20).IsRequired()
            .HasValueGenerator((property, entity) => _numberGenerator);

        builder.Property(piBuilder => piBuilder.Form).HasColumnType("json");
        builder.Property(piBuilder => piBuilder.Status);
        builder.Property(piBuilder => piBuilder.FinishTime);
        builder.Property(piBuilder => piBuilder.CreateTime);


        builder.Property(piBuilder => piBuilder.Creator).HasMaxLength(32);
        builder.Property(piBuilder => piBuilder.CreatorName).HasMaxLength(50);

        builder.Property(piBuilder => piBuilder.SuspendComment).HasMaxLength(128);
        builder.Property(piBuilder => piBuilder.Comment).HasMaxLength(256);
        builder.Property(piBuilder => piBuilder.SuspendTime);

        builder.Property(piBuilder => piBuilder.WorkActivityCount);
        builder.Property(piBuilder => piBuilder.IsDebug).HasComment("是否处于debug模式");
        builder.Property(piBuilder => piBuilder.IsDelete).HasComment(("是否已经删除"));

        builder.HasIndex(piBuilder => piBuilder.Number);

        builder.ToTable($"{prefix}_processInstance", builder =>
        {
            builder.HasComment("工作流实例");
        });
        builder.Property(piBuilder => piBuilder.StartTime);
        builder.Property(piBuilder => piBuilder.Priority).HasDefaultValue(Priority.Normal);
        builder.HasOne(piBuilder => piBuilder.WorkProcess);
    }
}