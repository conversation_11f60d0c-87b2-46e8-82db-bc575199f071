﻿using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Coder.Member;
using Coder.Member.Clients;
using Coder.Member.ViewModels.Claims;
using Coder.Member.ViewModels.Users;

// ReSharper disable CheckNamespace

namespace Coder.ScriptWorkflow.WebApi;

public static class UserClaimsIdentityHelper
{
    private static readonly string[] skipClaimName =
    {
        "real_name", ClaimTypes.NameIdentifier, ClaimTypes.Role, ClaimTypes.MobilePhone, ClaimTypes.Name,
        ClaimTypes.Email,
        ClaimTypes.NameIdentifier
    };
    /// <summary>
    /// 获取identityUser,主要用于调试下
    /// </summary>
    /// <param name="user"></param>
    /// <param name="currentUser"></param>
    /// <param name="userClient"></param>
    /// <param name="debugUser"></param>
    /// <returns></returns>
    public static ClaimsPrincipal ToDebuggerUser(this ClaimsPrincipal currentUser,
        IUserClient userClient, string debugUser = null)
    {
        if (debugUser == null) return currentUser;
        //非超级管理员
        if (!currentUser.IsInRole("admin"))
            return currentUser;

        var debuggerUserViewModel = userClient.GetUserAsync(debugUser).Result.Data;

        var a = new ClaimsPrincipal(new ClaimsIdentity(debuggerUserViewModel.ToTokenClaims()));

        return a;

    }

    private static IEnumerable<Claim> ToTokenClaims(this UserViewModel viewModel)
    {
        var result = new List<Claim>();

        result.Add(new Claim(ClaimTypes.Name, viewModel.UserName));
        result.Add(new Claim(ClaimTypes.NameIdentifier, viewModel.UserName));

        foreach (var viewModelRole in viewModel.Roles)
            result.Add(new Claim(ClaimTypes.Role, viewModelRole));


        foreach (var claims in viewModel.Claims)
            result.Add(new Claim(claims.Type, claims.Value));


        result.Add(new Claim(UserDefined.ClaimRealName, viewModel.Name));


        if (result.All(c => c.Type != "unique_name"))
            result.Add(new Claim("unique_name", viewModel.UserName));
        if (result.All(c => c.Type != "nameid"))
            result.Add(new Claim("nameid", viewModel.UserName));

        if (!string.IsNullOrEmpty(viewModel.PhoneNumber))
            result.Add(new Claim(ClaimTypes.MobilePhone, viewModel.PhoneNumber));


        return result;
    }

    /// <summary>
    ///     把ClaimsIdentity转换成UserViewModel
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public static UserViewModel ToUserViewModel(this ClaimsPrincipal user)
    {
        var result = new UserViewModel
        {
            /**
             * 兼容6.0和8.0 的jwt cliam的设置。
             */
            UserName = user.Claims.First(claim =>
                claim.Type == ClaimTypes.NameIdentifier || claim.Type == "nameid" || claim.Type == "unique_name").Value,
            Name = user.Claims.First(claim => claim.Type == "real_name").Value
        };

        result.Email = user.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Email)?.Value;

        result.Roles = user.Claims.Where(claim => claim.Type == ClaimTypes.Role).Select(claim => claim.Value).ToList();
        result.PhoneNumber = user.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.MobilePhone)?.Value;

        result.Claims =
            user.Claims.Where(claim => !skipClaimName.Contains(claim.Type))
                .Select(f => new ClaimViewModel(f));
        return result;
    }
}